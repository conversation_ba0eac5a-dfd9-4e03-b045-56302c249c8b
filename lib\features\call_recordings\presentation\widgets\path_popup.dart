import 'package:flutter/material.dart';

class PathDialog extends StatelessWidget {
  final String title;
  final String? path;
  final bool isSetupMode;
  final VoidCallback onCancel;
  final VoidCallback onConfirm;
  final TextEditingController? controller;

  const PathDialog({
    super.key,
    required this.title,
    this.path,
    required this.isSetupMode,
    required this.onCancel,
    required this.onConfirm,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      backgroundColor: const Color(0xFF111015), // Dark background
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.mic, color: Colors.redAccent),
                  onPressed: () {}, // Handle voice input
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(fontSize: 18, color: Color(0XFFFFFFFF), fontWeight: FontWeight.w400),
                      ),
                      const SizedBox(height: 8),
                      if (isSetupMode && path != null)
                        RichText(
                          text: TextSpan(
                            children: <TextSpan>[
                              TextSpan(style: TextStyle(fontWeight: FontWeight.w300, fontSize: 12, color: Color(0XFFD1D1D1))),
                              TextSpan(text: path, style: TextStyle(fontWeight: FontWeight.w300, fontSize: 12, color: Color(0XFF37A3DE))),
                            ],
                          ),
                        ),
                      if (!isSetupMode && path == null)
                        TextField(
                          controller: controller,
                          decoration: InputDecoration(
                            hintText: "Enter path...",
                            filled: true,
                            fillColor: Colors.grey[800],
                            border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
                            hintStyle: TextStyle(color: Colors.grey[500]),
                          ),
                          style: const TextStyle(color: Colors.white),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _customButton(text: "Cancel", onPressed: onCancel, isPrimary: false),
                _customButton(text: isSetupMode ? "Setup" : "Save", onPressed: onConfirm, isPrimary: true),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _customButton({required String text, required VoidCallback onPressed, required bool isPrimary}) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            backgroundColor: isPrimary ? Color(0XFF1A1C2D) : Color(0XFF2C2C2D),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(300)),
          ),
          child: Text(
            text,
            style: TextStyle(color: isPrimary ? Color(0XFF373ADE) : Color(0XFFFFFFFF), fontWeight: FontWeight.w400, fontSize: 12),
          ),
        ),
      ),
    );
  }
}
