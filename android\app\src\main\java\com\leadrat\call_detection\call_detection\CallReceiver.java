package com.leadrat.call_detection.call_detection;

import android.Manifest;
import android.app.ActivityManager;
import android.content.pm.PackageManager;
import androidx.core.content.ContextCompat;

import com.leadrat.call_detection.call_detection.enums.AppEnvironment;
import com.leadrat.call_detection.call_detection.enums.CallDirection;
import com.leadrat.call_detection.call_detection.enums.CallStatus;
import com.leadrat.call_detection.call_detection.enums.CallType;
import com.leadrat.call_detection.call_detection.models.CallInfo;
import com.leadrat.call_detection.call_detection.models.CallLogInfo;
import com.leadrat.call_detection.call_detection.models.LeadCallLogDTO;
import com.leadrat.call_detection.call_detection.models.LeadStatusDTO;
import com.leadrat.call_detection.call_detection.models.ProjectDTO;
import com.leadrat.call_detection.call_detection.models.PropertyTypeDTO;
import com.leadrat.call_detection.call_detection.models.ProspectCallLogDTO;
import com.leadrat.call_detection.call_detection.services.CallDetectionService;
import com.leadrat.call_detection.call_detection.services.OverlayService;
import com.leadrat.call_detection.call_detection.services.DataSyncService;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.media.MediaCodec;
import android.os.Build;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.telephony.TelephonyManager;
import android.util.Log;
import java.io.File;
import java.io.IOException;
import android.media.AudioManager;
import android.media.AudioFocusRequest;
import android.media.audiofx.NoiseSuppressor;
import android.media.audiofx.AcousticEchoCanceler;
import android.media.audiofx.AutomaticGainControl;
import android.media.AudioFormat;
import android.media.AudioRecord;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.ByteArrayOutputStream;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.nio.ByteBuffer;
import java.util.Arrays;
import android.os.Build;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.embedding.engine.plugins.FlutterPlugin;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Scanner;
import org.json.JSONObject;
import org.json.JSONException;
import java.io.OutputStream;
import java.util.UUID;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import org.json.JSONArray;
import android.provider.CallLog;
import android.provider.Settings;
import android.database.Cursor;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.TimeZone;
import java.util.Date;
import android.util.Base64;
import java.io.FileInputStream;
import java.io.*;
import java.util.concurrent.CountDownLatch;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.HashMap;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.content.ContentValues;
import java.util.concurrent.CountDownLatch;
import java.io.File;


public class CallReceiver extends BroadcastReceiver {
    private static final String TAG = "CallReceiver";

    private static MethodChannel channel;
    private static OverlayService overlayService;
    private Context context;
    public static final String ACTION_USER_LOGIN = "com.leadrat.black.mobile.droid.USER_LOGIN";
    public static final String ACTION_USER_LOGIN_ENHANCED = "com.leadrat.black.mobile.droid.USER_LOGIN_ENHANCED";
    private static boolean callInProgress = false;

    // Store the last successful search result to ensure consistency
    private static Map<String, Object> lastSuccessfulSearchResult = null;
    private static String lastSearchedPhoneNumber = null;

    // Store the current call's lead data to use when call ends
    private static Object currentCallLeadData = null;
    private static String currentCallPhoneNumber = null;

    static {
        setAppEnvironment(AppEnvironment.PRD);
    }

    public static void setMethodChannel(MethodChannel methodChannel) {
        channel = methodChannel;
    }

    public static void setOverlayService(OverlayService service) {
        overlayService = service;
    }

    public static String baseUrl;

    public static String bucketName;

    public static void setAppEnvironment(AppEnvironment env) {
        switch (env) {
            case DEV:
                baseUrl = "https://lrb-qa-mobile.leadrat.info/api/";
                bucketName = "qleadrat-black";
                break;
            case QA:
                baseUrl = "https://lrb-qa-mobile.leadrat.info/api/";
                bucketName = "qleadrat-black";
                break;
            case PRD:
                baseUrl = "https://prd-mobile.leadrat.com/api/";
                bucketName = "leadrat-black";
                break;
            default:
                throw new IllegalArgumentException("Unknown environment: " + env);
        }
    }

    @Override
    public void onReceive(Context context, Intent intent) {
        Log.d(TAG, "On Receive Method with intent: " + intent.getAction());
        this.context = context;

        // Handle login actions
        if (ACTION_USER_LOGIN.equals(intent.getAction())) {
            handleLoginAction(context, intent);
        } else if (ACTION_USER_LOGIN_ENHANCED.equals(intent.getAction())) {
            handleLoginEnhancedAction(context, intent);
        }

        // Handle check recording enabled action
        if ("com.leadrat.call_detection.CHECK_RECORDING_ENABLED".equals(intent.getAction())) {
            Log.d(TAG, "Received request to check if call recording is enabled");
            isCallRecordingEnabledInBackground((isEnabled, shouldViewOnlyAssigned, shouldOpenPopupAfterCall) -> {
                Log.d(TAG, "Call recording enabled check result: " + isEnabled);
                Log.d(TAG, "shouldViewOnlyAssigned check result: " + shouldViewOnlyAssigned);
                Log.d(TAG, "shouldOpenPopupAfterCall check result: " + shouldOpenPopupAfterCall);
            });
        }

        // For phone state changes, let the foreground service handle it
        // This is just a fallback in case the service is not running
        if (intent.getAction() != null && intent.getAction().equals(TelephonyManager.ACTION_PHONE_STATE_CHANGED)) {
            // Check if CallDetectionService is running
            if (!isServiceRunning(context, CallDetectionService.class)) {
                Log.d(TAG, "CallDetectionService not running, checking permissions before starting");

                // Check for required permissions before starting service
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED ||
                        ContextCompat.checkSelfPermission(context, Manifest.permission.READ_CALL_LOG) != PackageManager.PERMISSION_GRANTED) {

                        Log.e(TAG, "Required permissions not granted. Cannot start CallDetectionService from CallReceiver.");
                        return; // Don't start service if permissions are not granted
                    }
                }

                // Start the service if it's not running and permissions are granted
                Intent serviceIntent = new Intent(context, CallDetectionService.class);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    context.startForegroundService(serviceIntent);
                } else {
                    context.startService(serviceIntent);
                }
            }
        }
    }

    /**
     * Handle user login action
     */
    private void handleLoginAction(Context context, Intent intent) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();

        // Clear existing values
        editor.putString("UserId", "");
        editor.putString("TenantId", "");

        String tenantId = intent.getStringExtra("TenantId");
        String userId = intent.getStringExtra("UserId");
        String isLoggedIn = intent.getStringExtra("IsLoggedIn");

        if (isLoggedIn != null && isLoggedIn.toLowerCase().equals("true")) {
            if (tenantId != null && !tenantId.isEmpty()) {
                editor.putString("TenantId", tenantId);
            }

            if (userId != null && !userId.isEmpty()) {
                editor.putString("UserId", userId);
            }
        } else {
            editor.putString("UserId", "");
            editor.putString("TenantId", "");
        }

        // Apply the changes
        editor.apply();

        Log.d(TAG, "Login preferences updated - TenantId: " + preferences.getString("TenantId", "") +
                ", UserId: " + preferences.getString("UserId", ""));
    }

    /**
     * Handle enhanced user login action - reads from external storage JSON file
     */
    private void handleLoginEnhancedAction(Context context, Intent intent) {
        Log.d(TAG, "🔧 [USER_LOGIN_ENHANCED] Received enhanced login broadcast");

        try {
            // The enhanced login action indicates that LeadRat global data has been updated
            // We should refresh data in all services that depend on tenant/user ID

            // Trigger data refresh in DataSyncService if it's running
            Intent refreshIntent = new Intent(context, com.leadrat.call_detection.call_detection.services.DataSyncService.class);
            refreshIntent.setAction("REFRESH_DATA");
            context.startService(refreshIntent);

            Log.d(TAG, "🔧 [USER_LOGIN_ENHANCED] Triggered data refresh in DataSyncService");

        } catch (Exception e) {
            Log.e(TAG, "❌ [USER_LOGIN_ENHANCED] Error handling enhanced login: " + e.getMessage());
        }
    }

    /**
     * Check if a service is running
     */
    private boolean isServiceRunning(Context context, Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    public interface CallLogCallback {
        void onSuccess();
        void onError(Exception e);
    }

    public String getMatchingCallRecording(String directoryPath, long callEndTime) {
        // Get phone number from preferences for enhanced matching
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        String phoneNumber = preferences.getString("LastPhoneNumber", "");
        long callStartTime = preferences.getLong("CallDialedStartTime", 0);
        if (callStartTime == 0) {
            callStartTime = preferences.getLong("RingingStartTime", 0);
        }

        return getMatchingCallRecordingEnhanced(directoryPath, phoneNumber, callStartTime, callEndTime);
    }

    /**
     * Enhanced call recording matching with multiple validation criteria
     * @param directoryPath Directory containing call recordings
     * @param phoneNumber Phone number of the call for validation
     * @param callStartTime Start time of the call
     * @param callEndTime End time of the call
     * @return Base64 encoded recording or null if no match found
     */
    public String getMatchingCallRecordingEnhanced(String directoryPath, String phoneNumber, long callStartTime, long callEndTime) {
        Log.d(TAG, "🎵 [RECORDING_MATCH] Enhanced search for call recording");
        Log.d(TAG, "🎵 [RECORDING_MATCH] Directory: " + directoryPath);
        Log.d(TAG, "🎵 [RECORDING_MATCH] Phone: " + phoneNumber);
        Log.d(TAG, "🎵 [RECORDING_MATCH] Call start: " + new java.util.Date(callStartTime));
        Log.d(TAG, "🎵 [RECORDING_MATCH] Call end: " + new java.util.Date(callEndTime));

        File dir = new File(directoryPath);
        if (!dir.exists() || !dir.isDirectory()) {
            Log.d(TAG, "🎵 [RECORDING_MATCH] ❌ Directory not found: " + directoryPath);
            return null;
        }

        // Get all audio files
        File[] files = dir.listFiles((dir1, name) -> {
            String lowerName = name.toLowerCase();
            return lowerName.endsWith(".mp3") || lowerName.endsWith(".wav") ||
                   lowerName.endsWith(".m4a") || lowerName.endsWith(".aac") ||
                   lowerName.endsWith(".3gp") || lowerName.endsWith(".amr");
        });

        if (files == null || files.length == 0) {
            Log.d(TAG, "🎵 [RECORDING_MATCH] ❌ No audio files found");
            return null;
        }

        Log.d(TAG, "🎵 [RECORDING_MATCH] Found " + files.length + " audio files");

        // Create recording candidates with scoring
        List<RecordingCandidate> candidates = new ArrayList<>();
        for (File file : files) {
            RecordingCandidate candidate = analyzeRecordingFile(file, phoneNumber, callStartTime, callEndTime);
            if (candidate != null) {
                candidates.add(candidate);
                Log.d(TAG, "🎵 [RECORDING_MATCH] Candidate: " + file.getName() +
                      " (score: " + candidate.totalScore + ")");
            }
        }

        if (candidates.isEmpty()) {
            Log.d(TAG, "🎵 [RECORDING_MATCH] ❌ No valid candidates found");
            return null;
        }

        // Sort by total score (highest first)
        candidates.sort((c1, c2) -> Double.compare(c2.totalScore, c1.totalScore));

        RecordingCandidate bestMatch = candidates.get(0);

        // Log detailed analysis of top candidates
        logDetailedAnalysis(candidates, phoneNumber, callStartTime, callEndTime);

        Log.d(TAG, "🎵 [RECORDING_MATCH] ✅ SELECTED: " + bestMatch.file.getName());
        Log.d(TAG, "🎵 [RECORDING_MATCH] ✅ Total score: " + String.format("%.1f", bestMatch.totalScore));
        Log.d(TAG, "🎵 [RECORDING_MATCH] ✅ Phone match: " + bestMatch.phoneNumberMatch);
        Log.d(TAG, "🎵 [RECORDING_MATCH] ✅ Time diff: " + (bestMatch.timeDifference / 1000) + "s");

        // Only return the match if it meets minimum quality criteria
        if (bestMatch.totalScore >= 50.0) { // Minimum 50% confidence
            Log.d(TAG, "🎵 [RECORDING_MATCH] ✅ CONFIDENCE ACCEPTABLE - returning recording");
            return encodeFileToBase64(bestMatch.file.getAbsolutePath());
        } else {
            Log.d(TAG, "🎵 [RECORDING_MATCH] ❌ CONFIDENCE TOO LOW: " + String.format("%.1f", bestMatch.totalScore) + " (minimum: 50.0)");
            return null;
        }
    }

    /**
     * Log detailed analysis of recording candidates for debugging
     */
    private void logDetailedAnalysis(List<RecordingCandidate> candidates, String phoneNumber, long callStartTime, long callEndTime) {
        Log.d(TAG, "🎵 [DETAILED_ANALYSIS] ==========================================");
        Log.d(TAG, "🎵 [DETAILED_ANALYSIS] Call Details:");
        Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Phone: " + phoneNumber);
        Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Start: " + new java.util.Date(callStartTime));
        Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - End: " + new java.util.Date(callEndTime));
        Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Duration: " + ((callEndTime - callStartTime) / 1000) + "s");
        Log.d(TAG, "🎵 [DETAILED_ANALYSIS] ==========================================");

        int rank = 1;
        for (RecordingCandidate candidate : candidates) {
            if (rank <= 3) { // Show top 3 candidates
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] Rank #" + rank + ": " + candidate.file.getName());
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Total Score: " + String.format("%.1f", candidate.totalScore));
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Phone Match: " + candidate.phoneNumberMatch);
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Time Score: " + String.format("%.1f", candidate.timeScore));
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Size Score: " + String.format("%.1f", candidate.sizeScore));
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Time Diff: " + (candidate.timeDifference / 1000) + "s");
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - File Size: " + (candidate.file.length() / 1024) + "KB");
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] - Modified: " + new java.util.Date(candidate.file.lastModified()));
                Log.d(TAG, "🎵 [DETAILED_ANALYSIS] ------------------------------------------");
            }
            rank++;
        }
        Log.d(TAG, "🎵 [DETAILED_ANALYSIS] ==========================================");
    }

    /**
     * Class to represent a recording candidate with scoring
     */
    private static class RecordingCandidate {
        File file;
        double totalScore;
        boolean phoneNumberMatch;
        double timeScore;
        double sizeScore;
        double durationScore;
        long timeDifference;

        RecordingCandidate(File file) {
            this.file = file;
            this.totalScore = 0.0;
            this.phoneNumberMatch = false;
            this.timeScore = 0.0;
            this.sizeScore = 0.0;
            this.durationScore = 0.0;
            this.timeDifference = Long.MAX_VALUE;
        }
    }

    /**
     * Analyze a recording file and calculate its match score
     */
    private RecordingCandidate analyzeRecordingFile(File file, String phoneNumber, long callStartTime, long callEndTime) {
        RecordingCandidate candidate = new RecordingCandidate(file);

        try {
            // 1. Phone number validation (40% weight)
            candidate.phoneNumberMatch = validatePhoneNumberInFileName(file.getName(), phoneNumber);
            if (candidate.phoneNumberMatch) {
                candidate.totalScore += 40.0;
                Log.d(TAG, "🎵 [RECORDING_ANALYSIS] ✅ Phone number match in filename: " + file.getName());
            }

            // 2. Timestamp validation (35% weight)
            candidate.timeScore = calculateTimeScore(file, callStartTime, callEndTime);
            candidate.totalScore += candidate.timeScore * 0.35;

            // Store time difference for reference
            long fileModified = file.lastModified();
            long diffFromStart = Math.abs(fileModified - callStartTime);
            long diffFromEnd = Math.abs(fileModified - callEndTime);
            candidate.timeDifference = Math.min(diffFromStart, diffFromEnd);

            // 3. File size validation (15% weight)
            candidate.sizeScore = calculateSizeScore(file, callStartTime, callEndTime);
            candidate.totalScore += candidate.sizeScore * 0.15;

            // 4. File creation/modification time analysis (10% weight)
            double metadataScore = calculateMetadataScore(file, callStartTime, callEndTime);
            candidate.totalScore += metadataScore * 0.10;

            // 5. Audio file validation - ensure it's a valid audio file
            if (!isValidAudioFile(file)) {
                candidate.totalScore *= 0.5; // Penalize invalid audio files
                Log.d(TAG, "🎵 [RECORDING_ANALYSIS] ⚠️ File may not be valid audio: " + file.getName());
            }

            Log.d(TAG, "🎵 [RECORDING_ANALYSIS] File: " + file.getName() +
                  " | Phone: " + candidate.phoneNumberMatch +
                  " | Time: " + String.format("%.1f", candidate.timeScore) +
                  " | Size: " + String.format("%.1f", candidate.sizeScore) +
                  " | Total: " + String.format("%.1f", candidate.totalScore));

            return candidate;

        } catch (Exception e) {
            Log.e(TAG, "🎵 [RECORDING_ANALYSIS] Error analyzing file " + file.getName() + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Validate if phone number appears in filename
     */
    private boolean validatePhoneNumberInFileName(String fileName, String phoneNumber) {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return false;
        }

        // Clean the phone number for comparison
        String cleanPhoneNumber = cleanPhoneNumber(phoneNumber);
        String cleanFileName = fileName.toLowerCase().replaceAll("[^0-9]", "");

        // Check various phone number formats in filename
        String[] phoneVariants = {
            cleanPhoneNumber,
            cleanPhoneNumber.replaceFirst("^91", ""), // Remove country code
            cleanPhoneNumber.replaceFirst("^\\+91", ""), // Remove +91
            phoneNumber.replaceAll("[^0-9]", "") // Original number digits only
        };

        for (String variant : phoneVariants) {
            if (variant.length() >= 10 && cleanFileName.contains(variant)) {
                Log.d(TAG, "🎵 [PHONE_VALIDATION] Found phone variant '" + variant + "' in filename: " + fileName);
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate time-based score for recording file
     */
    private double calculateTimeScore(File file, long callStartTime, long callEndTime) {
        long fileModified = file.lastModified();
        long callDuration = callEndTime - callStartTime;

        // Calculate differences from both start and end times
        long diffFromStart = Math.abs(fileModified - callStartTime);
        long diffFromEnd = Math.abs(fileModified - callEndTime);
        long minDiff = Math.min(diffFromStart, diffFromEnd);

        Log.d(TAG, "🎵 [TIME_SCORE] File: " + file.getName() +
              " | Modified: " + new java.util.Date(fileModified) +
              " | Min diff: " + (minDiff / 1000) + "s");

        // Score based on time proximity (closer = higher score)
        if (minDiff <= 30000) { // Within 30 seconds
            return 100.0;
        } else if (minDiff <= 60000) { // Within 1 minute
            return 90.0;
        } else if (minDiff <= 120000) { // Within 2 minutes
            return 75.0;
        } else if (minDiff <= 300000) { // Within 5 minutes
            return 50.0;
        } else if (minDiff <= 600000) { // Within 10 minutes
            return 25.0;
        } else {
            return 0.0;
        }
    }

    /**
     * Calculate size-based score for recording file
     */
    private double calculateSizeScore(File file, long callStartTime, long callEndTime) {
        long fileSize = file.length();
        long callDuration = Math.max(callEndTime - callStartTime, 1000); // Minimum 1 second

        // Estimate expected file size (rough calculation)
        // Assume ~8KB per second for compressed audio (varies by codec)
        long expectedMinSize = (callDuration / 1000) * 4 * 1024; // 4KB/sec minimum
        long expectedMaxSize = (callDuration / 1000) * 16 * 1024; // 16KB/sec maximum

        if (fileSize >= expectedMinSize && fileSize <= expectedMaxSize) {
            return 100.0; // Perfect size match
        } else if (fileSize >= expectedMinSize / 2 && fileSize <= expectedMaxSize * 2) {
            return 75.0; // Reasonable size
        } else if (fileSize >= 1024) { // At least 1KB
            return 50.0; // Some content
        } else {
            return 0.0; // Too small to be valid
        }
    }

    /**
     * Calculate metadata-based score with enhanced analysis
     */
    private double calculateMetadataScore(File file, long callStartTime, long callEndTime) {
        try {
            long fileModified = file.lastModified();
            long fileSize = file.length();
            String fileName = file.getName().toLowerCase();

            double score = 0.0;

            // 1. File modification time analysis (60% of metadata score)
            if (fileModified >= callStartTime && fileModified <= callEndTime + 60000) {
                score += 60.0; // Created during call or within 1 minute after
            } else if (fileModified >= callStartTime - 30000 && fileModified <= callEndTime + 300000) {
                score += 45.0; // Within reasonable window
            } else if (fileModified >= callStartTime - 300000 && fileModified <= callEndTime + 600000) {
                score += 20.0; // Extended window
            }

            // 2. File name pattern analysis (25% of metadata score)
            if (fileName.contains("call") || fileName.contains("rec") || fileName.contains("audio")) {
                score += 15.0; // Likely a call recording
            }
            if (fileName.matches(".*\\d{10,}.*")) { // Contains 10+ digit number
                score += 10.0; // Contains phone number pattern
            }

            // 3. File size reasonableness (15% of metadata score)
            long callDuration = Math.max(callEndTime - callStartTime, 1000);
            long expectedMinSize = (callDuration / 1000) * 2 * 1024; // 2KB/sec minimum
            if (fileSize >= expectedMinSize && fileSize <= expectedMinSize * 20) {
                score += 15.0; // Reasonable size for call duration
            } else if (fileSize >= 1024) { // At least 1KB
                score += 5.0; // Has some content
            }

            Log.d(TAG, "🎵 [METADATA_SCORE] File: " + file.getName() +
                  " | Size: " + (fileSize / 1024) + "KB" +
                  " | Score: " + String.format("%.1f", score));

            return Math.min(score, 100.0); // Cap at 100

        } catch (Exception e) {
            Log.e(TAG, "🎵 [METADATA_SCORE] Error analyzing " + file.getName() + ": " + e.getMessage());
            return 25.0; // Default score if metadata unavailable
        }
    }

    /**
     * Extract timestamp from filename if present
     * Common patterns: timestamp_number.ext, call_timestamp.ext, etc.
     */
    private long extractTimestampFromFileName(String fileName) {
        try {
            // Look for timestamp patterns in filename
            String[] patterns = {
                "\\d{13}", // 13-digit timestamp (milliseconds)
                "\\d{10}", // 10-digit timestamp (seconds)
                "\\d{8}_\\d{6}", // YYYYMMDD_HHMMSS
                "\\d{4}-\\d{2}-\\d{2}_\\d{2}-\\d{2}-\\d{2}" // YYYY-MM-DD_HH-MM-SS
            };

            for (String pattern : patterns) {
                java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
                java.util.regex.Matcher m = p.matcher(fileName);
                if (m.find()) {
                    String timestampStr = m.group();
                    if (timestampStr.length() == 13) {
                        return Long.parseLong(timestampStr); // Milliseconds
                    } else if (timestampStr.length() == 10) {
                        return Long.parseLong(timestampStr) * 1000; // Convert seconds to milliseconds
                    }
                    // Handle other patterns as needed
                }
            }
        } catch (Exception e) {
            Log.d(TAG, "🎵 [TIMESTAMP_EXTRACT] Could not extract timestamp from: " + fileName);
        }
        return 0;
    }

    /**
     * Validate if file is a proper audio file by checking basic properties
     */
    private boolean isValidAudioFile(File file) {
        try {
            // Basic checks
            if (!file.exists() || !file.canRead()) {
                return false;
            }

            // Size check - audio files should be at least 1KB
            if (file.length() < 1024) {
                Log.d(TAG, "🎵 [AUDIO_VALIDATION] File too small: " + file.getName() + " (" + file.length() + " bytes)");
                return false;
            }

            // Extension check
            String fileName = file.getName().toLowerCase();
            boolean hasAudioExtension = fileName.endsWith(".mp3") || fileName.endsWith(".wav") ||
                                      fileName.endsWith(".m4a") || fileName.endsWith(".aac") ||
                                      fileName.endsWith(".3gp") || fileName.endsWith(".amr");

            if (!hasAudioExtension) {
                Log.d(TAG, "🎵 [AUDIO_VALIDATION] Invalid audio extension: " + file.getName());
                return false;
            }

            // Try to read first few bytes to check for audio file signatures
            try (FileInputStream fis = new FileInputStream(file)) {
                byte[] header = new byte[12];
                int bytesRead = fis.read(header);

                if (bytesRead >= 4) {
                    // Check for common audio file signatures
                    String headerStr = new String(header, 0, Math.min(bytesRead, 4));

                    // MP3 files often start with ID3 or have MP3 frame sync
                    if (headerStr.startsWith("ID3") ||
                        (header[0] == (byte)0xFF && (header[1] & 0xE0) == 0xE0)) {
                        return true;
                    }

                    // WAV files start with "RIFF"
                    if (headerStr.startsWith("RIFF")) {
                        return true;
                    }

                    // M4A/AAC files often start with specific signatures
                    if (bytesRead >= 8) {
                        String extendedHeader = new String(header, 0, 8);
                        if (extendedHeader.contains("ftyp") || extendedHeader.contains("M4A")) {
                            return true;
                        }
                    }

                    // 3GP files
                    if (bytesRead >= 12) {
                        String fullHeader = new String(header, 0, 12);
                        if (fullHeader.contains("3gp") || fullHeader.contains("3GP")) {
                            return true;
                        }
                    }
                }

                // If we can't identify the format but it has the right extension and size, assume it's valid
                Log.d(TAG, "🎵 [AUDIO_VALIDATION] Could not identify format but assuming valid: " + file.getName());
                return true;

            } catch (Exception e) {
                Log.d(TAG, "🎵 [AUDIO_VALIDATION] Error reading file header: " + file.getName() + " - " + e.getMessage());
                return true; // Assume valid if we can't read header
            }

        } catch (Exception e) {
            Log.e(TAG, "🎵 [AUDIO_VALIDATION] Error validating audio file: " + file.getName() + " - " + e.getMessage());
            return true; // Assume valid on error
        }
    }

    private File findBestMatch(File[] files, long callEndTime, long toleranceMillis) {
        File bestMatch = null;
        long minDiff = Long.MAX_VALUE;

        for (File file : files) {
            long diff = Math.abs(file.lastModified() - callEndTime);
            Log.d("Recording", "File: " + file.getName() + ", diff: " + (diff / 1000) + " seconds");

            if (diff < minDiff && diff <= toleranceMillis) {
                minDiff = diff;
                bestMatch = file;
            }
        }

        if (bestMatch != null) {
            Log.d("Recording", "Best match in " + (toleranceMillis / 1000) + "s window: " +
                  bestMatch.getName() + ", diff: " + (minDiff / 1000) + "s");
        }

        return bestMatch;
    }

    public void saveCallLog(Context context, String callRecording, String phoneNumber, SharedPreferences.Editor editor) {
        Log.d(TAG, "📞 [OFFLINE_QUEUE] saveCallLog called for phone: " + phoneNumber + ", hasRecording: " + (callRecording != null && !callRecording.isEmpty()));
        // Set the context for this instance
        this.context = context;



        // Get call information from the service if available
        CallInfo callInfo;
        if (context instanceof CallDetectionService) {
            callInfo = ((CallDetectionService) context).getCallInformation();
        } else {
            callInfo = getCallInformation(context);
        }

        Log.d(TAG, "Call Information: Direction=" + callInfo.direction +
              ", Status=" + callInfo.status + ", Duration=" + callInfo.duration);

        // Ensure we have a valid phone number
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            Log.e(TAG, "Phone number is null or empty, cannot save call log");
            resetPreferences(editor);
            return;
        }

        // Ensure we have a valid duration
        double duration;
        try {
            duration = Double.parseDouble(callInfo.duration);
        } catch (NumberFormatException e) {
            Log.e(TAG, "Invalid duration format: " + callInfo.duration + ", using 0");
            duration = 0;
        }

        addCallLogAsync(
                phoneNumber,
                callInfo.startTime,
                callInfo.endTime,
                duration,
                callInfo.direction,
                "",
                callInfo.status,
                callRecording,
                new CallLogCallback() {
                    @Override
                    public void onSuccess() {
                        resetPreferences(editor);
                        Log.d(TAG, "Call log saved successfully.");
                        context.stopService(new Intent(context, OverlayService.class));
                    }

                    @Override
                    public void onError(Exception e) {
                        Log.e(TAG, "Error saving call log: " + e.getMessage());
                        // Still reset preferences to avoid state corruption
                        resetPreferences(editor);
                    }
                }
        );
    }



    private void notifyFlutter(String phoneNumber) {
        if (channel != null) {
            try {
                channel.invokeMethod("onIncomingCall",
                    java.util.Collections.singletonMap("phone_number", phoneNumber));
            } catch (Exception e) {
                Log.e(TAG, "Error notifying Flutter: " + e.getMessage());
            }
        }
    }

    private void resetPreferences(SharedPreferences.Editor editor) {
        editor.putBoolean("Ringing", false);
        editor.putBoolean("CallPicked", false);
        editor.putBoolean("CallDialed", false);
        editor.putBoolean("CallApi", false);
        editor.putBoolean("MissedCall", false); // Reset missed call flag
        editor.putLong("RingingStartTime", 0);
        editor.putLong("CallPickedStartTime", 0);
        editor.putLong("CallDialedStartTime", 0);
        editor.putLong("CallEndTime", 0);
        editor.apply();

        // Clear current call data after call ends to prevent reuse for subsequent calls
        currentCallLeadData = null;
        currentCallPhoneNumber = null;
        Log.d(TAG, "📞 [CALL_END] Cleared current call lead data");
    }

    private void showOverlay(Context context, String phoneNumber) {
        if (!isOverlayServiceRunning(context, OverlayService.class)) {
            Intent overlayIntent = new Intent(context, OverlayService.class);
            context.startService(overlayIntent);
        } else {
            Log.d(TAG, "Overlay is already running. No need to start again.");
        }
    }

    private boolean isOverlayServiceRunning(Context context, Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }





    /**
     * Clean up old recordings from the cache directory
     * Keeps only the most recent 5 recordings to avoid filling up storage
     */
    private void cleanupOldRecordings(File directory) {
        try {
            if (directory == null || !directory.exists() || !directory.isDirectory()) {
                return;
            }

            // Get all audio files
            File[] files = directory.listFiles((dir, name) ->
                name.toLowerCase().endsWith(".mp3") ||
                name.toLowerCase().endsWith(".wav") ||
                name.toLowerCase().endsWith(".m4a"));

            if (files == null || files.length <= 5) {
                return; // No need to clean up if we have 5 or fewer files
            }

            // Sort files by last modified time (newest first)
            Arrays.sort(files, (f1, f2) -> Long.compare(f2.lastModified(), f1.lastModified()));

            // Delete all but the 5 most recent files
            for (int i = 5; i < files.length; i++) {
                boolean deleted = files[i].delete();
                if (deleted) {
                    System.out.println("Deleted old recording: " + files[i].getName());
                }
            }
        } catch (Exception e) {
            System.out.println("Error cleaning up old recordings: " + e.getMessage());
        }
    }





    /**
     * Copy a file from source to destination
     * @param sourcePath Source file path
     * @param destPath Destination file path
     */
    private void copyFile(String sourcePath, String destPath) {
        FileInputStream fis = null;
        FileOutputStream fos = null;

        try {
            fis = new FileInputStream(sourcePath);
            fos = new FileOutputStream(destPath);

            byte[] buffer = new byte[8192];
            int bytesRead;

            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            System.out.println("Error copying file: " + e.getMessage());
        } finally {
            try {
                if (fis != null) fis.close();
                if (fos != null) fos.close();
            } catch (IOException e) {
                System.out.println("Error closing streams: " + e.getMessage());
            }
        }
    }

    /**
     * Trim audio file to keep only the last N seconds
     * @param inputFilePath Path to the input audio file
     * @param callDurationSeconds Duration of the call in seconds
     * @return Path to the trimmed file, or null if trimming failed
     */
    private String trimAudioFile(String inputFilePath, int callDurationSeconds) {
        if (inputFilePath == null || inputFilePath.isEmpty() || callDurationSeconds <= 0) {
            Log.e(TAG, "Invalid input for trimming: inputPath=" + inputFilePath + ", duration=" + callDurationSeconds);
            return null;
        }

        File inputFile = new File(inputFilePath);
        if (!inputFile.exists() || !inputFile.canRead()) {
            Log.e(TAG, "Input file does not exist or cannot be read: " + inputFilePath);
            return null;
        }

        // Create output file path with _trimmed suffix
        String outputFilePath = inputFilePath.replace(".mp3", "_trimmed.mp3");

        MediaExtractor extractor = null;
        MediaMuxer muxer = null;

        try {
            // Set up the MediaExtractor to read from the input file
            extractor = new MediaExtractor();
            extractor.setDataSource(inputFilePath);

            // Find the audio track
            int audioTrackIndex = -1;
            for (int i = 0; i < extractor.getTrackCount(); i++) {
                MediaFormat format = extractor.getTrackFormat(i);
                String mime = format.getString(MediaFormat.KEY_MIME);
                if (mime != null && mime.startsWith("audio/")) {
                    audioTrackIndex = i;
                    break;
                }
            }

            if (audioTrackIndex == -1) {
                Log.e(TAG, "No audio track found in file: " + inputFilePath);
                return null;
            }

            // Select the audio track
            extractor.selectTrack(audioTrackIndex);
            MediaFormat format = extractor.getTrackFormat(audioTrackIndex);

            // Get the duration of the audio file in microseconds
            long durationUs = format.getLong(MediaFormat.KEY_DURATION);
            long durationSeconds = durationUs / 1000000; // Convert to seconds

            Log.d(TAG, "Audio file duration: " + durationSeconds + " seconds");
            Log.d(TAG, "Call duration: " + callDurationSeconds + " seconds");

            // If the audio file is shorter than the call duration, no need to trim
            if (durationSeconds <= callDurationSeconds) {
                Log.d(TAG, "Audio file is shorter than call duration, no need to trim");
                return inputFilePath;
            }

            // Calculate the start position for trimming (to keep only the last N seconds)
            long startPositionUs = durationUs - (callDurationSeconds * 1000000L);

            // Seek to the start position
            extractor.seekTo(startPositionUs, MediaExtractor.SEEK_TO_CLOSEST_SYNC);

            // Set up the MediaMuxer to write to the output file
            muxer = new MediaMuxer(outputFilePath, MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);
            int outputTrackIndex = muxer.addTrack(format);
            muxer.start();

            // Allocate a buffer for reading from the extractor
            int maxBufferSize = format.getInteger(MediaFormat.KEY_MAX_INPUT_SIZE, 64 * 1024);
            ByteBuffer buffer = ByteBuffer.allocate(maxBufferSize);
            MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();

            // Read and write the samples
            while (true) {
                int sampleSize = extractor.readSampleData(buffer, 0);
                if (sampleSize < 0) {
                    break; // End of stream
                }

                long sampleTime = extractor.getSampleTime();
                bufferInfo.offset = 0;
                bufferInfo.size = sampleSize;
                bufferInfo.presentationTimeUs = sampleTime - startPositionUs;
                bufferInfo.flags = extractor.getSampleFlags();

                muxer.writeSampleData(outputTrackIndex, buffer, bufferInfo);
                extractor.advance();
            }

            // Finalize the muxer
            muxer.stop();
            muxer.release();
            extractor.release();

            Log.d(TAG, "Audio trimming completed successfully: " + outputFilePath);

            // Verify the output file exists and has content
            File outputFile = new File(outputFilePath);
            if (outputFile.exists() && outputFile.length() > 0) {
                return outputFilePath;
            } else {
                Log.e(TAG, "Trimmed file does not exist or is empty");
                return inputFilePath; // Return original file as fallback
            }

        } catch (Exception e) {
            Log.e(TAG, "Error trimming audio file: " + e.getMessage(), e);
            return inputFilePath; // Return original file if trimming fails
        } finally {
            // Clean up resources
            if (muxer != null) {
                try {
                    muxer.release();
                } catch (Exception e) {
                    Log.e(TAG, "Error releasing muxer: " + e.getMessage());
                }
            }
            if (extractor != null) {
                try {
                    extractor.release();
                } catch (Exception e) {
                    Log.e(TAG, "Error releasing extractor: " + e.getMessage());
                }
            }
        }
    }



    /**
     * Save base64 data to temporary file for offline processing
     */
    private String saveBase64ToTempFile(String base64Data, String entityId) {
        if (base64Data == null || base64Data.isEmpty()) {
            Log.e(TAG, "Cannot save null or empty base64 data");
            return null;
        }

        try {
            // Create temp directory if it doesn't exist
            File tempDir = new File(context.getFilesDir(), "temp_recordings");
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }

            // Create temp file
            String fileName = "recording_" + entityId + "_" + System.currentTimeMillis() + ".mp3";
            File tempFile = new File(tempDir, fileName);

            // Decode base64 and write to file
            byte[] decodedBytes = android.util.Base64.decode(base64Data, android.util.Base64.DEFAULT);

            FileOutputStream fos = new FileOutputStream(tempFile);
            fos.write(decodedBytes);
            fos.close();

            Log.d(TAG, "📁 [OFFLINE_QUEUE] Saved base64 data to temp file: " + tempFile.getAbsolutePath());
            return tempFile.getAbsolutePath();

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Failed to save base64 to temp file: " + e.getMessage());
            return null;
        }
    }

    /**
     * Convert file to Base64 string with robust error handling
     */
    public String encodeFileToBase64(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            System.out.println("Cannot encode null or empty file path");
            return null;
        }

        FileInputStream fis = null;
        try {
            File file = new File(filePath);
            if (!file.exists() || !file.canRead()) {
                System.out.println("File does not exist or cannot be read: " + filePath);
                return null;
            }

            if (file.length() == 0) {
                System.out.println("File is empty: " + filePath);
                return null;
            }

            if (file.length() > 10 * 1024 * 1024) { // 10MB limit
                System.out.println("File is too large for Base64 encoding: " + filePath);
                return null;
            }

            fis = new FileInputStream(file);
            byte[] bytes = new byte[(int) file.length()];
            int bytesRead = fis.read(bytes);

            if (bytesRead != file.length()) {
                System.out.println("Could not read entire file: " + filePath);
                return null;
            }

            // Use Android's Base64 utility if available, otherwise use Java's
            try {
                // Try Android's Base64 first
                return android.util.Base64.encodeToString(bytes, android.util.Base64.NO_WRAP);
            } catch (Exception e) {
                // Fall back to Java's Base64
                return java.util.Base64.getEncoder().encodeToString(bytes);
            }
        } catch (Exception e) {
            System.out.println("Base64 encoding failed: " + e.getMessage());
            return null;
        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    System.out.println("Error closing file input stream: " + e.getMessage());
                }
            }
        }
    }

    /**
     * Send Base64 string to the server
     */

    public interface UploadCallback {
        void onSuccess(String fileUrl);
        void onFailure(String error);
    }

    private void enableSpeakerMode(Context context) {
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        if (audioManager != null) {
            audioManager.setMode(AudioManager.MODE_IN_CALL);
            audioManager.setSpeakerphoneOn(true);
            audioManager.setStreamVolume(AudioManager.STREAM_VOICE_CALL, audioManager.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL), 0);
            Log.d("CallReceiver", "Speakerphone enabled");
        }
    }

    private void requestAudioFocus(Context context) {
        AudioManager audioManager = (AudioManager) context.getSystemService(Context.AUDIO_SERVICE);
        if (audioManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                AudioFocusRequest audioFocusRequest = new AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN_TRANSIENT)
                        .setAudioAttributes(new android.media.AudioAttributes.Builder()
                                .setUsage(android.media.AudioAttributes.USAGE_VOICE_COMMUNICATION)
                                .setContentType(android.media.AudioAttributes.CONTENT_TYPE_SPEECH)
                                .build())
                        .build();
                audioManager.requestAudioFocus(audioFocusRequest);
            } else {
                audioManager.requestAudioFocus(null, AudioManager.STREAM_VOICE_CALL, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT);
            }
            Log.d("CallReceiver", "Audio focus requested.");
        }
    }

    public void makeApiCall(String phoneNumber, Context callContext) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            Log.e(TAG, "📞 [CALL_DETECTION] Phone number is null or empty, not showing overlay");
            return;
        }

        // Set the context for this instance
        this.context = callContext;

        Log.d(TAG, "📞 [CALL_DETECTION] Starting call detection for phone number: " + phoneNumber);

        // First, search in local database
        searchLocalDatabase(phoneNumber, callContext);
    }

    private void searchLocalDatabase(String phoneNumber, Context callContext) {
        Log.d(TAG, "📞 [CALL_DETECTION] Searching local database for phone number: " + phoneNumber);

        try {
            // First try Flutter-based search if available
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity != null && mainActivity.isFlutterEngineReady()) {
                Log.d(TAG, "📞 [CALL_DETECTION] Flutter is available - searching local database via Flutter");

                // Use the public searchLeadByPhoneNumber method in MainActivity
                mainActivity.searchLeadByPhoneNumber(phoneNumber, new MethodChannel.Result() {
                    @Override
                    public void success(Object result) {
                        handleLocalSearchResult(result, phoneNumber, callContext);
                    }

                    @Override
                    public void error(String errorCode, String errorMessage, Object errorDetails) {
                        Log.e(TAG, "📞 [CALL_DETECTION] Flutter search failed: " + errorMessage);
                        // Fallback to native search
                        searchNativeDatabase(phoneNumber, callContext);
                    }

                    @Override
                    public void notImplemented() {
                        Log.e(TAG, "📞 [CALL_DETECTION] Flutter search method not implemented");
                        // Fallback to native search
                        searchNativeDatabase(phoneNumber, callContext);
                    }
                });
            } else {
                Log.d(TAG, "📞 [CALL_DETECTION] Flutter not available - using native database search");
                // Use native database search when Flutter is not available
                searchNativeDatabase(phoneNumber, callContext);
            }
        } catch (Exception e) {
            Log.e(TAG, "📞 [CALL_DETECTION] Error during Flutter search: " + e.getMessage());
            // Fallback to native search
            searchNativeDatabase(phoneNumber, callContext);
        }
    }

    private void handleLocalSearchResult(Object result, String phoneNumber, Context callContext) {
        if (result != null && result instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> leadData = (Map<String, Object>) result;

            String leadId = (String) leadData.get("id");
            String leadName = (String) leadData.get("name");
            String contactNo = (String) leadData.get("contactNo");

            Log.d(TAG, "📞 [CALL_DETECTION] Lead found in local database: " + leadName + " (" + contactNo + ")");

            // Store the successful search result for later use in call log
            lastSuccessfulSearchResult = new HashMap<>(leadData);
            lastSearchedPhoneNumber = phoneNumber;

            // Store the current call's lead data to use when call ends
            currentCallLeadData = new HashMap<>(leadData);
            currentCallPhoneNumber = phoneNumber;

            Log.d(TAG, "📞 [CALL_DETECTION] Stored successful search result for call log: " + leadName + " (ID: " + leadId + ")");

            // Show simplified overlay with only name and phone number
            showSimplifiedOverlay(callContext, leadName, phoneNumber, leadId);
        } else {
            Log.d(TAG, "📞 [CALL_DETECTION] No lead found in local database for: " + phoneNumber);
            // Clear stored result if no lead found
            lastSuccessfulSearchResult = null;
            lastSearchedPhoneNumber = null;

            // Clear current call data if no lead found
            currentCallLeadData = null;
            currentCallPhoneNumber = null;

            // Don't show overlay if no lead found
        }
    }

    /**
     * Search local database for call log purposes (without showing overlay)
     * Uses the exact same logic as call detection search
     */
    private void searchLocalDatabaseForCallLog(String phoneNumber, LocalSearchCallback callback) {
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Searching local database for call log: " + phoneNumber);

        // First check if we have current call's lead data (highest priority)
        if (currentCallLeadData != null && phoneNumber.equals(currentCallPhoneNumber)) {
            Log.d(TAG, "📁 [OFFLINE_QUEUE] Using current call's lead data from call detection");
            @SuppressWarnings("unchecked")
            Map<String, Object> leadData = (Map<String, Object>) currentCallLeadData;
            String leadName = (String) leadData.get("name");
            String leadId = (String) leadData.get("id");
            Log.d(TAG, "📁 [OFFLINE_QUEUE] Reusing current call data: " + leadName + " (ID: " + leadId + ")");
            callback.onResult(currentCallLeadData);
            return;
        }

        // Fallback to stored successful search result
        if (lastSuccessfulSearchResult != null && phoneNumber.equals(lastSearchedPhoneNumber)) {
            Log.d(TAG, "📁 [OFFLINE_QUEUE] Using stored successful search result from call detection");
            String leadName = (String) lastSuccessfulSearchResult.get("name");
            String leadId = (String) lastSuccessfulSearchResult.get("id");
            Log.d(TAG, "📁 [OFFLINE_QUEUE] Reusing stored result: " + leadName + " (ID: " + leadId + ")");
            callback.onResult(lastSuccessfulSearchResult);
            return;
        }

        try {
            // Try to get MainActivity instance to use the same Flutter search that worked during call detection
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity != null) {
                Log.d(TAG, "📁 [OFFLINE_QUEUE] Flutter is available - searching via MainActivity (same as call detection)");

                // Use the exact same method that successfully found "sufiyan" during call detection
                mainActivity.searchLeadByPhoneNumber(phoneNumber, new MethodChannel.Result() {
                    @Override
                    public void success(Object result) {
                        Log.d(TAG, "📁 [OFFLINE_QUEUE] Flutter search completed successfully");
                        callback.onResult(result);
                    }

                    @Override
                    public void error(String errorCode, String errorMessage, Object errorDetails) {
                        Log.e(TAG, "📁 [OFFLINE_QUEUE] Flutter search failed: " + errorMessage);
                        // Try current call data first, then stored result as fallback before native search
                        if (currentCallLeadData != null && phoneNumber.equals(currentCallPhoneNumber)) {
                            Log.d(TAG, "📁 [OFFLINE_QUEUE] Using current call data as fallback after Flutter error");
                            callback.onResult(currentCallLeadData);
                        } else if (lastSuccessfulSearchResult != null && phoneNumber.equals(lastSearchedPhoneNumber)) {
                            Log.d(TAG, "📁 [OFFLINE_QUEUE] Using stored result as fallback after Flutter error");
                            callback.onResult(lastSuccessfulSearchResult);
                        } else {
                            // Fallback to native search
                            searchNativeDatabaseForCallLog(phoneNumber, callback);
                        }
                    }

                    @Override
                    public void notImplemented() {
                        Log.e(TAG, "📁 [OFFLINE_QUEUE] Flutter search method not implemented");
                        // Try current call data first, then stored result as fallback before native search
                        if (currentCallLeadData != null && phoneNumber.equals(currentCallPhoneNumber)) {
                            Log.d(TAG, "📁 [OFFLINE_QUEUE] Using current call data as fallback after Flutter not implemented");
                            callback.onResult(currentCallLeadData);
                        } else if (lastSuccessfulSearchResult != null && phoneNumber.equals(lastSearchedPhoneNumber)) {
                            Log.d(TAG, "📁 [OFFLINE_QUEUE] Using stored result as fallback after Flutter not implemented");
                            callback.onResult(lastSuccessfulSearchResult);
                        } else {
                            // Fallback to native search
                            searchNativeDatabaseForCallLog(phoneNumber, callback);
                        }
                    }
                });
            } else {
                Log.d(TAG, "📁 [OFFLINE_QUEUE] MainActivity not available");
                // Try current call data first, then stored result as fallback before native search
                if (currentCallLeadData != null && phoneNumber.equals(currentCallPhoneNumber)) {
                    Log.d(TAG, "📁 [OFFLINE_QUEUE] Using current call data as fallback when MainActivity not available");
                    callback.onResult(currentCallLeadData);
                } else if (lastSuccessfulSearchResult != null && phoneNumber.equals(lastSearchedPhoneNumber)) {
                    Log.d(TAG, "📁 [OFFLINE_QUEUE] Using stored result as fallback when MainActivity not available");
                    callback.onResult(lastSuccessfulSearchResult);
                } else {
                    // Use native database search when MainActivity is not available
                    searchNativeDatabaseForCallLog(phoneNumber, callback);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "📁 [OFFLINE_QUEUE] Error during Flutter search: " + e.getMessage());
            // Try current call data first, then stored result as fallback before native search
            if (currentCallLeadData != null && phoneNumber.equals(currentCallPhoneNumber)) {
                Log.d(TAG, "📁 [OFFLINE_QUEUE] Using current call data as fallback after exception");
                callback.onResult(currentCallLeadData);
            } else if (lastSuccessfulSearchResult != null && phoneNumber.equals(lastSearchedPhoneNumber)) {
                Log.d(TAG, "📁 [OFFLINE_QUEUE] Using stored result as fallback after exception");
                callback.onResult(lastSuccessfulSearchResult);
            } else {
                // Fallback to native search
                searchNativeDatabaseForCallLog(phoneNumber, callback);
            }
        }
    }

    /**
     * Native database search for call log purposes
     * Uses the exact same logic as the working searchNativeDatabase method
     */
    private void searchNativeDatabaseForCallLog(String phoneNumber, LocalSearchCallback callback) {
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Starting native database search for: " + phoneNumber);

        try {
            // First, try to search in Hive native database (primary source)
            Map<String, Object> hiveLeadData = searchInHiveNativeDatabase(phoneNumber, context);

            if (hiveLeadData != null) {
                String leadName = (String) hiveLeadData.get("name");
                String leadId = (String) hiveLeadData.get("id");
                String contactNo = (String) hiveLeadData.get("contactNo");
                Object isLeadValue = hiveLeadData.get("isLead");

                Log.d(TAG, "📁 [OFFLINE_QUEUE] Lead found in Hive native database: " + leadName + " (" + contactNo + ") with ID: " + leadId);

                // Convert to the format expected by callback
                Map<String, Object> result = new HashMap<>();
                result.put("id", leadId);
                result.put("name", leadName);
                result.put("contactNo", contactNo);

                // Include isLead field from Hive database result
                if (isLeadValue != null) {
                    result.put("isLead", isLeadValue);
                    Log.d(TAG, "📁 [OFFLINE_QUEUE] Including isLead field from Hive database: " + isLeadValue);
                } else {
                    result.put("isLead", true); // Default for backward compatibility
                    Log.d(TAG, "📁 [OFFLINE_QUEUE] isLead field not found in Hive result, defaulting to true");
                }

                callback.onResult(result);
                return;
            }

            // Second, try to search in original native SQLite database (fallback)
            Map<String, String> leadData = searchInNativeSQLiteDatabase(phoneNumber, context);

            if (leadData != null) {
                String leadName = leadData.get("name");
                String leadId = leadData.get("id");
                String contactNo = leadData.get("contactNo");

                Log.d(TAG, "📁 [OFFLINE_QUEUE] Lead found in original native SQLite: " + leadName + " (" + contactNo + ") with ID: " + leadId);

                // Convert to the format expected by callback
                Map<String, Object> result = new HashMap<>();
                result.put("id", leadId);
                result.put("name", leadName);
                result.put("contactNo", contactNo);
                result.put("isLead", true); // Default to true for native SQLite fallback
                Log.d(TAG, "📁 [OFFLINE_QUEUE] Native SQLite fallback - defaulting isLead to true");

                callback.onResult(result);
                return;
            }

            // If not found in native SQLite, try searching original Hive files
            Log.d(TAG, "📁 [OFFLINE_QUEUE] No lead found in native SQLite, trying original Hive files...");
            Map<String, String> hiveFileLeadData = searchInOriginalHiveFiles(phoneNumber, context);

            if (hiveFileLeadData != null) {
                String leadName = hiveFileLeadData.get("name");
                String leadId = hiveFileLeadData.get("id");
                String contactNo = hiveFileLeadData.get("contactNo");

                Log.d(TAG, "📁 [OFFLINE_QUEUE] Lead found in original Hive files: " + leadName + " (" + contactNo + ") with ID: " + leadId);

                // Convert to the format expected by callback
                Map<String, Object> result = new HashMap<>();
                result.put("id", leadId);
                result.put("name", leadName);
                result.put("contactNo", contactNo);
                result.put("isLead", true); // Default to true for Hive files fallback
                Log.d(TAG, "📁 [OFFLINE_QUEUE] Hive files fallback - defaulting isLead to true");

                callback.onResult(result);
                return;
            }

            Log.d(TAG, "📁 [OFFLINE_QUEUE] No lead found via any method for: " + phoneNumber);
            callback.onResult(null);

        } catch (Exception e) {
            Log.e(TAG, "📁 [OFFLINE_QUEUE] Error in native database search: " + e.getMessage());
            callback.onResult(null);
        }
    }

    /**
     * Callback interface for local search results
     */
    public interface LocalSearchCallback {
        void onResult(Object result);
    }

    private void showSimplifiedOverlay(Context context, String name, String phoneNumber, String entityId) {
        // Show overlay for valid names, even if ID is unknown
        if (name == null || name.trim().isEmpty() || name.equals("Unknown Contact")) {
            Log.d(TAG, "📞 [CALL_DETECTION] Skipping overlay for invalid name: " + name);
            return;
        }

        Log.d(TAG, "📞 [CALL_DETECTION] Checking overlay permission before showing overlay");

        // Check if overlay permission is granted
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(context)) {
            Log.e(TAG, "❌ [CALL_DETECTION] Overlay permission not granted - cannot show overlay");
            return;
        }

        // Use a valid entityId even if extraction failed
        String validEntityId = entityId;
        if (validEntityId == null || validEntityId.isEmpty() || validEntityId.startsWith("unknown-")) {
            validEntityId = "lead-" + System.currentTimeMillis();
            Log.d(TAG, "📞 [CALL_DETECTION] Generated fallback ID for overlay: " + validEntityId);
        }

        Log.d(TAG, "📞 [CALL_DETECTION] Showing simplified overlay for: " + name + " (" + phoneNumber + ") with ID: " + validEntityId);

        try {
            // Stop existing service first
            context.stopService(new Intent(context, OverlayService.class));

            // Small delay to ensure previous service is stopped
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // Start new service with simplified data (only name and phone)
            Intent overlayIntent = new Intent(context, OverlayService.class);
            overlayIntent.putExtra("name", name);
            overlayIntent.putExtra("phoneNumber", phoneNumber);
            overlayIntent.putExtra("entityId", validEntityId);
            // Don't pass other details to keep overlay simple

            // Use startForegroundService for Android 8.0+ to ensure it works when app is killed
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(overlayIntent);
                Log.d(TAG, "📞 [CALL_DETECTION] Started overlay as foreground service");
            } else {
                context.startService(overlayIntent);
                Log.d(TAG, "📞 [CALL_DETECTION] Started overlay as regular service");
            }

            Log.d(TAG, "✅ [CALL_DETECTION] Simplified overlay started successfully for: " + name);

        } catch (Exception e) {
            Log.e(TAG, "❌ [CALL_DETECTION] Error starting overlay service: " + e.getMessage());
        }
    }

    private void searchNativeDatabase(String phoneNumber, Context callContext) {
        Log.d(TAG, "📞 [NATIVE_SEARCH] Starting native database search for: " + phoneNumber);

        try {
            // FIRST: Try to search in Hive native database (primary source for call detection)
            Map<String, Object> hiveLeadData = searchInHiveNativeDatabase(phoneNumber, callContext);

            if (hiveLeadData != null) {
                String leadName = (String) hiveLeadData.get("name");
                String leadId = (String) hiveLeadData.get("id");
                String contactNo = (String) hiveLeadData.get("contactNo");
                Object isLeadValue = hiveLeadData.get("isLead");

                Log.d(TAG, "📞 [NATIVE_SEARCH] Lead found in Hive native database: " + leadName + " (" + contactNo + ") with ID: " + leadId);

                // Store the current call's lead data to use when call ends
                Map<String, Object> leadDataMap = new HashMap<>();
                leadDataMap.put("id", leadId);
                leadDataMap.put("name", leadName);
                leadDataMap.put("contactNo", contactNo);

                // Include isLead field from Hive database result
                if (isLeadValue != null) {
                    leadDataMap.put("isLead", isLeadValue);
                    Log.d(TAG, "📞 [NATIVE_SEARCH] Including isLead field from Hive database: " + isLeadValue);
                } else {
                    leadDataMap.put("isLead", true); // Default for backward compatibility
                    Log.d(TAG, "📞 [NATIVE_SEARCH] isLead field not found in Hive result, defaulting to true");
                }

                currentCallLeadData = leadDataMap;
                currentCallPhoneNumber = phoneNumber;

                showSimplifiedOverlay(callContext, leadName, phoneNumber, leadId);
                return;
            }

            Log.d(TAG, "📞 [NATIVE_SEARCH] No lead found in Hive native database");

            // SECOND: Try to search in original native SQLite database (fallback)
            Map<String, String> leadData = searchInNativeSQLiteDatabase(phoneNumber, callContext);

            if (leadData != null) {
                String leadName = leadData.get("name");
                String leadId = leadData.get("id");
                String contactNo = leadData.get("contactNo");

                Log.d(TAG, "📞 [NATIVE_SEARCH] Lead found in original native SQLite: " + leadName + " (" + contactNo + ")");

                // Store the current call's lead data to use when call ends
                Map<String, Object> leadDataMap = new HashMap<>();
                leadDataMap.put("id", leadId);
                leadDataMap.put("name", leadName);
                leadDataMap.put("contactNo", contactNo);
                leadDataMap.put("isLead", true); // Default to true for native SQLite fallback
                Log.d(TAG, "📞 [NATIVE_SEARCH] Native SQLite fallback - defaulting isLead to true");
                currentCallLeadData = leadDataMap;
                currentCallPhoneNumber = phoneNumber;

                showSimplifiedOverlay(callContext, leadName, phoneNumber, leadId);
                return;
            }

            Log.d(TAG, "📞 [NATIVE_SEARCH] No lead found in original native SQLite database");

            // THIRD: Search directly in original Hive database files
            Log.d(TAG, "📞 [NATIVE_SEARCH] 🔄 Searching directly in original Hive database files...");

            // Try to get lead data directly from Hive files
            Map<String, String> hiveFileLeadData = searchInOriginalHiveFiles(phoneNumber, callContext);

            if (hiveFileLeadData != null) {
                String leadName = hiveFileLeadData.get("name");
                String leadId = hiveFileLeadData.get("id");
                String contactNo = hiveFileLeadData.get("contactNo");

                Log.d(TAG, "📞 [NATIVE_SEARCH] ✅ Lead found in original Hive files: " + leadName + " (" + contactNo + ") with ID: " + leadId);

                // Store the current call's lead data to use when call ends
                Map<String, Object> leadDataMap = new HashMap<>();
                leadDataMap.put("id", leadId);
                leadDataMap.put("name", leadName);
                leadDataMap.put("contactNo", contactNo);
                leadDataMap.put("isLead", true); // Default to true for Hive files fallback
                Log.d(TAG, "📞 [NATIVE_SEARCH] Hive files fallback - defaulting isLead to true");
                currentCallLeadData = leadDataMap;
                currentCallPhoneNumber = phoneNumber;

                showSimplifiedOverlay(callContext, leadName, phoneNumber, leadId);
                return;
            }

            Log.d(TAG, "📞 [NATIVE_SEARCH] ❌ No lead found in original Hive files either");

            // EMERGENCY FALLBACK: Try to trigger DataSyncService to populate native database
            Log.d(TAG, "📞 [NATIVE_SEARCH] 🚨 Emergency fallback: Triggering DataSyncService to populate native database");
            triggerDataSyncService(callContext);

            // Clear current call data if no lead found
            currentCallLeadData = null;
            currentCallPhoneNumber = null;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error during native search: " + e.getMessage());
        }
    }

    /**
     * Search for lead in Hive native database using ultra-fast lookup (primary source for call detection when app is killed)
     */
    private Map<String, Object> searchInHiveNativeDatabase(String phoneNumber, Context context) {
        long startTime = System.currentTimeMillis();
        Log.d(TAG, "⚡ [ULTRA_FAST_HIVE] Starting ultra-fast search for: " + phoneNumber);

        try {
            // Check if Hive database has data
            SharedPreferences hiveDbPrefs = context.getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            boolean hasData = hiveDbPrefs.getBoolean("has_data", false);
            int totalLeads = hiveDbPrefs.getInt("total_leads", 0);
            long lastUpdateTime = hiveDbPrefs.getLong("last_update_time", 0);

            Log.d(TAG, "⚡ [ULTRA_FAST_HIVE] Database status: hasData=" + hasData + ", totalLeads=" + totalLeads + ", lastUpdate=" + new java.util.Date(lastUpdateTime));

            if (!hasData || totalLeads == 0) {
                Log.d(TAG, "⚡ [ULTRA_FAST_HIVE] Hive native database is empty or has no data");
                return null;
            }

            // Use DataSyncService's static ultra-fast search method
            Map<String, Object> result = DataSyncService.searchLeadInHiveDatabaseStatic(context, phoneNumber);

            long endTime = System.currentTimeMillis();

            if (result != null) {
                String leadName = (String) result.get("name");
                String leadId = (String) result.get("id");
                String contactNo = (String) result.get("contactNo");

                Log.d(TAG, "⚡ [ULTRA_FAST_HIVE] ✅ FOUND in " + (endTime - startTime) + "ms: " + leadName + " (" + contactNo + ") with ID: " + leadId);
                return result;
            } else {
                Log.d(TAG, "⚡ [ULTRA_FAST_HIVE] ❌ NOT FOUND in " + (endTime - startTime) + "ms for: " + phoneNumber);
                return null;
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            Log.e(TAG, "⚡ [ULTRA_FAST_HIVE] Error in " + (endTime - startTime) + "ms: " + e.getMessage());
            return null;
        }
    }

    /**
     * Search for lead in original native SQLite database (fallback when app is killed)
     */
    private Map<String, String> searchInNativeSQLiteDatabase(String phoneNumber, Context context) {
        Log.d(TAG, "📞 [NATIVE_SQLITE] Searching native SQLite database for: " + phoneNumber);

        try {
            // Check if native database has data
            SharedPreferences nativeDbPrefs = context.getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
            boolean hasData = nativeDbPrefs.getBoolean("has_data", false);
            int totalLeads = nativeDbPrefs.getInt("total_leads", 0);
            long lastUpdateTime = nativeDbPrefs.getLong("last_update_time", 0);

            Log.d(TAG, "📞 [NATIVE_SQLITE] Database status: hasData=" + hasData + ", totalLeads=" + totalLeads + ", lastUpdate=" + new java.util.Date(lastUpdateTime));

            if (!hasData || totalLeads == 0) {
                Log.d(TAG, "📞 [NATIVE_SQLITE] Native database is empty or has no data");
                return null;
            }

            Log.d(TAG, "📞 [NATIVE_SQLITE] Native database has " + totalLeads + " leads, searching...");

            // Access the native database directly using the helper class
            DataSyncService.NativeLeadsDbHelper dbHelper = new DataSyncService.NativeLeadsDbHelper(context);
            List<Map<String, Object>> allLeads = getNativeLeadsFromDatabase(dbHelper);

            if (allLeads.isEmpty()) {
                Log.d(TAG, "📞 [NATIVE_SQLITE] No leads retrieved from native database");
                return null;
            }

            Log.d(TAG, "📞 [NATIVE_SQLITE] Retrieved " + allLeads.size() + " leads from native database");

            // Clean the search phone number first
            String cleanSearchNumber = cleanPhoneNumber(phoneNumber);
            Log.d(TAG, "📞 [NATIVE_SQLITE] Cleaned search number: " + cleanSearchNumber);

            // Log first 10 leads to see what data we have
            Log.d(TAG, "📞 [NATIVE_SQLITE] === DATABASE CONTENT SAMPLE ===");
            for (int i = 0; i < Math.min(10, allLeads.size()); i++) {
                Map<String, Object> lead = allLeads.get(i);
                Log.d(TAG, "📞 [NATIVE_SQLITE] Lead " + (i+1) + ": {");
                Log.d(TAG, "📞 [NATIVE_SQLITE]   id: " + lead.get("id"));
                Log.d(TAG, "📞 [NATIVE_SQLITE]   name: " + lead.get("name"));
                Log.d(TAG, "📞 [NATIVE_SQLITE]   contactNo: " + lead.get("contactNo"));
                Log.d(TAG, "📞 [NATIVE_SQLITE]   alternateContactNo: " + lead.get("alternateContactNo"));
                Log.d(TAG, "📞 [NATIVE_SQLITE]   assignTo: " + lead.get("assignTo"));
                Log.d(TAG, "📞 [NATIVE_SQLITE]   isDeleted: " + lead.get("isDeleted"));
                Log.d(TAG, "📞 [NATIVE_SQLITE] }");
            }
            Log.d(TAG, "📞 [NATIVE_SQLITE] === END DATABASE CONTENT ===");

            // Also log any leads that contain the search number for debugging
            Log.d(TAG, "📞 [NATIVE_SQLITE] === LEADS CONTAINING SEARCH NUMBER ===");
            String searchLast10 = cleanSearchNumber.length() >= 10 ? cleanSearchNumber.substring(cleanSearchNumber.length() - 10) : cleanSearchNumber;
            Log.d(TAG, "📞 [NATIVE_SQLITE] Looking for leads containing: '" + searchLast10 + "' (last 10 digits of '" + cleanSearchNumber + "')");

            for (Map<String, Object> lead : allLeads) {
                String contactNo = (String) lead.get("contactNo");
                String alternateContactNo = (String) lead.get("alternateContactNo");

                boolean contactMatch = contactNo != null && contactNo.contains(searchLast10);
                boolean alternateMatch = alternateContactNo != null && alternateContactNo.contains(searchLast10);

                if (contactMatch || alternateMatch) {
                    Log.d(TAG, "📞 [NATIVE_SQLITE] Potential match: {");
                    Log.d(TAG, "📞 [NATIVE_SQLITE]   id: " + lead.get("id"));
                    Log.d(TAG, "📞 [NATIVE_SQLITE]   name: " + lead.get("name"));
                    Log.d(TAG, "📞 [NATIVE_SQLITE]   contactNo: " + lead.get("contactNo") + " (matches: " + contactMatch + ")");
                    Log.d(TAG, "📞 [NATIVE_SQLITE]   alternateContactNo: " + lead.get("alternateContactNo") + " (matches: " + alternateMatch + ")");
                    Log.d(TAG, "📞 [NATIVE_SQLITE] }");
                }
            }
            Log.d(TAG, "📞 [NATIVE_SQLITE] === END POTENTIAL MATCHES ===");

            // Search through the leads
            int checkedCount = 0;
            for (Map<String, Object> lead : allLeads) {
                String contactNo = (String) lead.get("contactNo");
                String alternateContactNo = (String) lead.get("alternateContactNo");
                String leadName = (String) lead.get("name");
                String leadId = (String) lead.get("id");

                checkedCount++;
                if (checkedCount <= 5) { // Log first 5 leads for debugging
                    Log.d(TAG, "📞 [NATIVE_SQLITE] Checking lead " + checkedCount + ": id=" + leadId + ", name=" + leadName + ", contactNo=" + contactNo + ", alternateContactNo=" + alternateContactNo);
                }

                if (contactNo != null) {
                    String cleanContactNo = cleanPhoneNumber(contactNo);
                    boolean matches = phoneNumbersMatch(cleanSearchNumber, cleanContactNo);

                    if (checkedCount <= 5) { // Log detailed matching for first 5 leads
                        Log.d(TAG, "📞 [NATIVE_SQLITE]   contactNo: '" + contactNo + "' → cleaned: '" + cleanContactNo + "' → matches: " + matches);
                    }

                    if (matches) {
                        Log.d(TAG, "📞 [NATIVE_SQLITE] ✅ MATCH FOUND in contactNo: " + contactNo + " (cleaned: " + cleanContactNo + ")");
                        Log.d(TAG, "📞 [NATIVE_SQLITE] ✅ Search: '" + cleanSearchNumber + "' matched with: '" + cleanContactNo + "'");
                        return createLeadResult(lead);
                    }
                }

                if (alternateContactNo != null) {
                    String cleanAlternateNo = cleanPhoneNumber(alternateContactNo);
                    boolean matches = phoneNumbersMatch(cleanSearchNumber, cleanAlternateNo);

                    if (checkedCount <= 5) { // Log detailed matching for first 5 leads
                        Log.d(TAG, "📞 [NATIVE_SQLITE]   alternateContactNo: '" + alternateContactNo + "' → cleaned: '" + cleanAlternateNo + "' → matches: " + matches);
                    }

                    if (matches) {
                        Log.d(TAG, "📞 [NATIVE_SQLITE] ✅ MATCH FOUND in alternateContactNo: " + alternateContactNo + " (cleaned: " + cleanAlternateNo + ")");
                        Log.d(TAG, "📞 [NATIVE_SQLITE] ✅ Search: '" + cleanSearchNumber + "' matched with: '" + cleanAlternateNo + "'");
                        return createLeadResult(lead);
                    }
                }
            }

            Log.d(TAG, "📞 [NATIVE_SQLITE] Checked " + checkedCount + " leads, no matches found for: " + cleanSearchNumber);

            Log.d(TAG, "📞 [NATIVE_SQLITE] No matching phone number found in native database");
            return null;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SQLITE] Error searching native SQLite database: " + e.getMessage());
            return null;
        }
    }

    /**
     * Convert lead data from native database format to search result format
     */
    private Map<String, String> createLeadResult(Map<String, Object> lead) {
        Map<String, String> result = new HashMap<>();

        result.put("id", (String) lead.get("id"));
        result.put("name", (String) lead.get("name"));
        result.put("contactNo", (String) lead.get("contactNo"));

        // Ensure we have a valid name
        String name = result.get("name");
        if (name == null || name.trim().isEmpty()) {
            result.put("name", "Unknown Contact");
        }

        Log.d(TAG, "📞 [NATIVE_SQLITE] Created lead result: " + result);
        return result;
    }

    /**
     * Check if two phone numbers match (handles different formats)
     */
    private boolean phoneNumbersMatch(String number1, String number2) {
        if (number1 == null || number2 == null) {
            Log.d(TAG, "📞 [PHONE_MATCH] One number is null: '" + number1 + "' vs '" + number2 + "'");
            return false;
        }

        // Remove all non-digit characters
        String clean1 = number1.replaceAll("[^\\d]", "");
        String clean2 = number2.replaceAll("[^\\d]", "");

        Log.d(TAG, "📞 [PHONE_MATCH] Comparing: '" + number1 + "' (→'" + clean1 + "') vs '" + number2 + "' (→'" + clean2 + "')");

        // If either is too short, no match
        if (clean1.length() < 7 || clean2.length() < 7) {
            Log.d(TAG, "📞 [PHONE_MATCH] One number too short: " + clean1.length() + " vs " + clean2.length());
            return false;
        }

        // Exact match
        if (clean1.equals(clean2)) {
            Log.d(TAG, "📞 [PHONE_MATCH] ✅ EXACT MATCH: '" + clean1 + "' == '" + clean2 + "'");
            return true;
        }

        // Match last 10 digits (common for Indian numbers)
        if (clean1.length() >= 10 && clean2.length() >= 10) {
            String last10_1 = clean1.substring(clean1.length() - 10);
            String last10_2 = clean2.substring(clean2.length() - 10);
            if (last10_1.equals(last10_2)) {
                Log.d(TAG, "📞 [PHONE_MATCH] ✅ LAST 10 DIGITS MATCH: '" + last10_1 + "' == '" + last10_2 + "'");
                return true;
            }
        }

        // Match without country code
        String withoutCountry1 = clean1.replaceFirst("^(91|\\+91)", "");
        String withoutCountry2 = clean2.replaceFirst("^(91|\\+91)", "");
        if (withoutCountry1.equals(withoutCountry2)) {
            Log.d(TAG, "📞 [PHONE_MATCH] ✅ WITHOUT COUNTRY CODE MATCH: '" + withoutCountry1 + "' == '" + withoutCountry2 + "'");
            return true;
        }

        Log.d(TAG, "📞 [PHONE_MATCH] ❌ NO MATCH: '" + clean1 + "' vs '" + clean2 + "'");
        return false;
    }

    /**
     * Get leads data from native SQLite database
     */
    private List<Map<String, Object>> getNativeLeadsFromDatabase(DataSyncService.NativeLeadsDbHelper dbHelper) {
        List<Map<String, Object>> leadsList = new ArrayList<>();

        try {
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            Cursor cursor = db.query(
                DataSyncService.NativeLeadsDbHelper.TABLE_LEADS,
                null, // Select all columns
                null, // No WHERE clause
                null, // No WHERE args
                null, // No GROUP BY
                null, // No HAVING
                null  // No ORDER BY
            );

            Log.d(TAG, "📞 [NATIVE_SQLITE] Found " + cursor.getCount() + " leads in native database");

            while (cursor.moveToNext()) {
                Map<String, Object> leadMap = new HashMap<>();

                leadMap.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                leadMap.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                leadMap.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                leadMap.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                leadMap.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));
                leadMap.put("isDeleted", cursor.getInt(cursor.getColumnIndexOrThrow("isDeleted")) == 1);
                leadMap.put("lastModifiedOn", cursor.getString(cursor.getColumnIndexOrThrow("lastModifiedOn")));
                leadMap.put("lastSyncedAt", cursor.getString(cursor.getColumnIndexOrThrow("lastSyncedAt")));

                leadsList.add(leadMap);
            }

            cursor.close();
            db.close();

            Log.d(TAG, "📞 [NATIVE_SQLITE] Retrieved " + leadsList.size() + " leads from native database");

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SQLITE] Error reading from native database: " + e.getMessage());
        }

        return leadsList;
    }

    /**
     * Get leads data from Hive native SQLite database
     */
    private List<Map<String, Object>> getHiveLeadsFromDatabase(DataSyncService.HiveLeadsDbHelper dbHelper) {
        List<Map<String, Object>> leadsList = new ArrayList<>();

        try {
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            Cursor cursor = db.query(
                DataSyncService.HiveLeadsDbHelper.TABLE_LEADS,
                null, // Select all columns
                "isDeleted = 0 AND isArchived = 0", // Only non-deleted and non-archived leads
                null, // No WHERE args
                null, // No GROUP BY
                null, // No HAVING
                null  // No ORDER BY
            );

            Log.d(TAG, "📞 [HIVE_DB] Found " + cursor.getCount() + " leads in Hive native database");

            while (cursor.moveToNext()) {
                Map<String, Object> leadMap = new HashMap<>();

                leadMap.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                leadMap.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                leadMap.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                leadMap.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                leadMap.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));
                leadMap.put("isDeleted", cursor.getInt(cursor.getColumnIndexOrThrow("isDeleted")) == 1);
                leadMap.put("lastModifiedOn", cursor.getString(cursor.getColumnIndexOrThrow("lastModifiedOn")));
                leadMap.put("lastSyncedAt", cursor.getString(cursor.getColumnIndexOrThrow("lastSyncedAt")));

                leadsList.add(leadMap);
            }

            cursor.close();
            db.close();

            Log.d(TAG, "📞 [HIVE_DB] Retrieved " + leadsList.size() + " leads from Hive native database");

        } catch (Exception e) {
            Log.e(TAG, "📞 [HIVE_DB] Error reading from Hive native database: " + e.getMessage());
        }

        return leadsList;
    }

    /**
     * Search directly in original Hive database files (THIRD search method)
     */
    private Map<String, String> searchInOriginalHiveFiles(String phoneNumber, Context context) {
        Log.d(TAG, "📞 [HIVE_FILES_SEARCH] Searching original Hive database files for: " + phoneNumber);

        try {
            // Get the Hive database path
            String hivePath = getHiveDatabasePath(context);
            if (hivePath == null) {
                Log.d(TAG, "📞 [HIVE_FILES_SEARCH] Could not determine Hive database path");
                return null;
            }

            Log.d(TAG, "📞 [HIVE_FILES_SEARCH] Using Hive path: " + hivePath);

            // Search in the Hive database file
            Map<String, String> result = searchLeadInHiveDatabase(hivePath, phoneNumber);

            if (result != null) {
                Log.d(TAG, "📞 [HIVE_FILES_SEARCH] ✅ Lead found in Hive files: " + result.get("name") + " (ID: " + result.get("id") + ")");
            } else {
                Log.d(TAG, "📞 [HIVE_FILES_SEARCH] ❌ No lead found in Hive files");
            }

            return result;

        } catch (Exception e) {
            Log.e(TAG, "📞 [HIVE_FILES_SEARCH] Error searching Hive files: " + e.getMessage());
            return null;
        }
    }



    /**
     * Get lead data directly from Flutter using method channel (DEPRECATED - kept for compatibility)
     */
    private Map<String, String> getLeadDataViaFlutter(String phoneNumber) {
        Log.d(TAG, "📞 [FLUTTER_SEARCH] Attempting to get lead data via Flutter for: " + phoneNumber);

        if (channel == null) {
            Log.d(TAG, "📞 [FLUTTER_SEARCH] Method channel not available");
            return null;
        }

        try {
            // Use CountDownLatch to make the async call synchronous
            final CountDownLatch latch = new CountDownLatch(1);
            final Map<String, String>[] result = new Map[1];

            // Create arguments for the method call
            Map<String, Object> arguments = new HashMap<>();
            arguments.put("phoneNumber", phoneNumber);

            Log.d(TAG, "📞 [FLUTTER_SEARCH] Calling Flutter method 'searchLeadByPhone' with: " + phoneNumber);

            // Call Flutter method to search for lead
            channel.invokeMethod("searchLeadByPhone", arguments, new MethodChannel.Result() {
                @Override
                public void success(Object response) {
                    Log.d(TAG, "📞 [FLUTTER_SEARCH] Flutter method call successful");

                    if (response instanceof Map) {
                        Map<String, Object> responseMap = (Map<String, Object>) response;

                        // Convert to String map
                        Map<String, String> leadData = new HashMap<>();
                        leadData.put("id", (String) responseMap.get("id"));
                        leadData.put("name", (String) responseMap.get("name"));
                        leadData.put("contactNo", (String) responseMap.get("contactNo"));
                        leadData.put("alternateContactNo", (String) responseMap.get("alternateContactNo"));
                        leadData.put("assignTo", (String) responseMap.get("assignTo"));

                        result[0] = leadData;
                        Log.d(TAG, "📞 [FLUTTER_SEARCH] Lead found: " + leadData.get("name") + " (ID: " + leadData.get("id") + ")");
                    } else {
                        Log.d(TAG, "📞 [FLUTTER_SEARCH] No lead found in Flutter");
                    }

                    latch.countDown();
                }

                @Override
                public void error(String errorCode, String errorMessage, Object errorDetails) {
                    Log.e(TAG, "📞 [FLUTTER_SEARCH] Flutter method call error: " + errorCode + " - " + errorMessage);
                    latch.countDown();
                }

                @Override
                public void notImplemented() {
                    Log.e(TAG, "📞 [FLUTTER_SEARCH] Flutter method 'searchLeadByPhone' not implemented");
                    latch.countDown();
                }
            });

            // Wait for the result with timeout
            boolean completed = latch.await(5, java.util.concurrent.TimeUnit.SECONDS);

            if (!completed) {
                Log.e(TAG, "📞 [FLUTTER_SEARCH] Timeout waiting for Flutter response");
                return null;
            }

            return result[0];

        } catch (Exception e) {
            Log.e(TAG, "📞 [FLUTTER_SEARCH] Error calling Flutter method: " + e.getMessage());
            return null;
        }
    }

    /**
     * Force sync a missing lead from Hive data to native SQLite database (DEPRECATED)
     * This method is no longer used as the new approach handles sync automatically
     */
    private boolean forceSyncMissingLead(String phoneNumber, Context context) {
        Log.d(TAG, "📞 [FORCE_SYNC] DEPRECATED: This method is no longer used");
        Log.d(TAG, "📞 [FORCE_SYNC] The new approach handles sync automatically via getLeadDataViaFlutter()");

        // Return false to indicate this method should not be used
        return false;
    }

    /**
     * Find exact lead data in Hive file using Flutter method channel (DEPRECATED - no longer used)
     * This method is kept for compatibility but should not be called
     */
    private Map<String, String> findExactLeadInHive(String hivePath, String phoneNumber) {
        Log.d(TAG, "📞 [HIVE_EXACT] DEPRECATED: This method should not be used anymore");
        Log.d(TAG, "📞 [HIVE_EXACT] Use getLeadDataViaFlutter() instead for reliable lead data");

        // Return null to force the caller to use the new approach
        return null;
    }

    /**
     * Trigger DataSyncService to populate native database (emergency fallback)
     */
    private void triggerDataSyncService(Context context) {
        try {
            Log.d(TAG, "🚨 [EMERGENCY_SYNC] Attempting to start DataSyncService to populate native database");

            // Start the DataSyncService to populate native database
            Intent serviceIntent = new Intent(context, DataSyncService.class);
            serviceIntent.setAction("EMERGENCY_SYNC");

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
                Log.d(TAG, "🚨 [EMERGENCY_SYNC] Started DataSyncService as foreground service");
            } else {
                context.startService(serviceIntent);
                Log.d(TAG, "🚨 [EMERGENCY_SYNC] Started DataSyncService as regular service");
            }

        } catch (Exception e) {
            Log.e(TAG, "🚨 [EMERGENCY_SYNC] Error starting DataSyncService: " + e.getMessage());
        }
    }

    /**
     * Insert a lead into the native SQLite database
     */
    private boolean insertLeadToNativeDatabase(Map<String, String> leadData, Context context) {
        Log.d(TAG, "📞 [DB_INSERT] Inserting lead to native database: " + leadData.get("name"));

        try {
            DataSyncService.NativeLeadsDbHelper dbHelper = new DataSyncService.NativeLeadsDbHelper(context);
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            ContentValues values = new ContentValues();
            values.put("id", leadData.get("id"));
            values.put("name", leadData.get("name"));
            values.put("contactNo", leadData.get("contactNo"));
            values.put("alternateContactNo", leadData.get("alternateContactNo"));
            values.put("assignTo", leadData.get("assignTo"));
            values.put("isDeleted", 0); // Assume not deleted
            values.put("lastModifiedOn", new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", java.util.Locale.US).format(new java.util.Date()));
            values.put("lastSyncedAt", new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", java.util.Locale.US).format(new java.util.Date()));

            long result = db.insertWithOnConflict(
                DataSyncService.NativeLeadsDbHelper.TABLE_LEADS,
                null,
                values,
                SQLiteDatabase.CONFLICT_REPLACE
            );

            db.close();

            if (result != -1) {
                Log.d(TAG, "📞 [DB_INSERT] ✅ Lead inserted successfully with row ID: " + result);

                // Update the database stats
                SharedPreferences nativeDbPrefs = context.getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
                int currentCount = nativeDbPrefs.getInt("total_leads", 0);
                nativeDbPrefs.edit()
                    .putInt("total_leads", currentCount + 1)
                    .putLong("last_update_time", System.currentTimeMillis())
                    .putBoolean("has_data", true)
                    .apply();

                return true;
            } else {
                Log.e(TAG, "📞 [DB_INSERT] ❌ Failed to insert lead");
                return false;
            }

        } catch (Exception e) {
            Log.e(TAG, "📞 [DB_INSERT] Error inserting lead: " + e.getMessage());
            return false;
        }
    }

    private String getHiveDatabasePath(Context context) {
        try {
            // Hive databases are typically stored in the app's data directory
            File dataDir = new File(context.getApplicationInfo().dataDir);
            File appFlutterDir = new File(dataDir, "app_flutter");

            if (!appFlutterDir.exists()) {
                Log.d(TAG, "📞 [NATIVE_SEARCH] app_flutter directory not found, trying alternatives");
                // Try other possible locations
                File[] possibleDirs = {
                    new File(dataDir, "flutter"),
                    new File(dataDir, "databases"),
                    dataDir
                };

                for (File dir : possibleDirs) {
                    File hiveFile = findHiveFile(dir, "offline_leads");
                    if (hiveFile != null) {
                        return hiveFile.getAbsolutePath();
                    }
                }
                return null;
            }

            // Look for the offline_leads.hive file
            File hiveFile = findHiveFile(appFlutterDir, "offline_leads");
            return hiveFile != null ? hiveFile.getAbsolutePath() : null;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error finding Hive database: " + e.getMessage());
            return null;
        }
    }

    private File findHiveFile(File directory, String baseName) {
        if (!directory.exists() || !directory.isDirectory()) {
            return null;
        }

        // Look for files with the base name
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                String fileName = file.getName();
                if (fileName.startsWith(baseName) && (fileName.endsWith(".hive") || fileName.endsWith(".db"))) {
                    Log.d(TAG, "📞 [NATIVE_SEARCH] Found Hive file: " + file.getAbsolutePath());
                    return file;
                }
            }
        }

        return null;
    }

    private Map<String, String> searchLeadInHiveDatabase(String hivePath, String phoneNumber) {
        Log.d(TAG, "📞 [NATIVE_SEARCH] Fast searching Hive database: " + hivePath);
        long startTime = System.currentTimeMillis();

        try {
            File hiveFile = new File(hivePath);
            if (!hiveFile.exists()) {
                Log.e(TAG, "📞 [NATIVE_SEARCH] Hive file does not exist: " + hivePath);
                return null;
            }

            // Clean the phone number for comparison
            String cleanPhoneNumber = cleanPhoneNumber(phoneNumber);
            Log.d(TAG, "📞 [NATIVE_SEARCH] Cleaned phone number: " + cleanPhoneNumber);

            // Use optimized streaming search instead of loading entire file
            Map<String, String> result = streamingSearchForPhoneNumber(hiveFile, cleanPhoneNumber, phoneNumber);

            long endTime = System.currentTimeMillis();
            Log.d(TAG, "📞 [NATIVE_SEARCH] Search completed in " + (endTime - startTime) + "ms");

            return result;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error reading Hive database: " + e.getMessage());
            return null;
        }
    }

    private Map<String, String> streamingSearchForPhoneNumber(File hiveFile, String cleanPhoneNumber, String originalPhoneNumber) {
        Log.d(TAG, "📞 [NATIVE_SEARCH] Starting streaming search for: " + cleanPhoneNumber);

        try (FileInputStream fis = new FileInputStream(hiveFile)) {
            byte[] buffer = new byte[8192]; // Larger buffer for better performance with bigger files
            StringBuilder currentChunk = new StringBuilder();
            int bytesRead;
            long totalBytesRead = 0;

            // Create search patterns
            String[] searchPatterns = createSearchPatterns(cleanPhoneNumber, originalPhoneNumber);
            Log.d(TAG, "📞 [NATIVE_SEARCH] Search patterns: " + java.util.Arrays.toString(searchPatterns));

            while ((bytesRead = fis.read(buffer)) != -1) {
                totalBytesRead += bytesRead;

                // Log progress every 2MB
                if (totalBytesRead % 2000000 == 0) {
                    Log.d(TAG, "📞 [NATIVE_SEARCH] Progress: " + (totalBytesRead / 1000000) + "MB processed");
                }

                // Convert current buffer to string
                for (int i = 0; i < bytesRead; i++) {
                    byte b = buffer[i];
                    if (b >= 32 && b <= 126) {
                        currentChunk.append((char) b);
                    } else if (b == 0) {
                        currentChunk.append(" ");
                    }
                }

                // Search in current chunk
                for (String pattern : searchPatterns) {
                    int phoneIndex = currentChunk.indexOf(pattern);
                    if (phoneIndex != -1) {
                        Log.d(TAG, "📞 [NATIVE_SEARCH] Found phone pattern '" + pattern + "' at chunk position: " + phoneIndex + " (total bytes: " + totalBytesRead + ")");

                        // Extract lead data from the surrounding context
                        Map<String, String> leadData = extractLeadDataFromChunk(currentChunk.toString(), phoneIndex, pattern);
                        if (leadData != null) {
                            return leadData;
                        }
                    }
                }

                // Keep only the last 2000 characters to maintain context across buffer boundaries
                if (currentChunk.length() > 3000) {
                    currentChunk.delete(0, currentChunk.length() - 2000);
                }

                // Early exit if we've read enough and found nothing (optimization)
                // Increase limit since we know the phone number can be at 8MB+ position
                if (totalBytesRead > 15000000) { // 15MB limit to ensure we find the data
                    Log.d(TAG, "📞 [NATIVE_SEARCH] Reached 15MB search limit, stopping for performance");
                    break;
                }
            }

            Log.d(TAG, "📞 [NATIVE_SEARCH] No phone number found in streaming search");
            return null;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error in streaming search: " + e.getMessage());
            return null;
        }
    }

    private String[] createSearchPatterns(String cleanPhoneNumber, String originalPhoneNumber) {
        java.util.Set<String> patterns = new java.util.HashSet<>();

        // Add original patterns
        patterns.add(originalPhoneNumber);
        patterns.add(cleanPhoneNumber);

        // Add variations without country code
        if (cleanPhoneNumber.startsWith("+91")) {
            patterns.add(cleanPhoneNumber.substring(3)); // Remove +91
        }
        if (cleanPhoneNumber.startsWith("91")) {
            patterns.add(cleanPhoneNumber.substring(2)); // Remove 91
        }

        // Add last 10 digits
        if (cleanPhoneNumber.length() >= 10) {
            patterns.add(cleanPhoneNumber.substring(cleanPhoneNumber.length() - 10));
        }

        // Add with different prefixes
        String baseNumber = cleanPhoneNumber.replaceFirst("^(\\+91|91)", "");
        patterns.add(baseNumber);
        patterns.add("91" + baseNumber);
        patterns.add("+91" + baseNumber);

        String[] result = patterns.toArray(new String[0]);
        Log.d(TAG, "📞 [NATIVE_SEARCH] Created " + result.length + " search patterns");
        return result;
    }

    private Map<String, String> extractLeadDataFromChunk(String chunk, int phoneIndex, String foundPhone) {
        try {
            // Use smaller window for faster processing
            int windowStart = Math.max(0, phoneIndex - 300);
            int windowEnd = Math.min(chunk.length(), phoneIndex + 300);
            String window = chunk.substring(windowStart, windowEnd);

            Log.d(TAG, "📞 [NATIVE_SEARCH] Extracting from chunk window (size: " + window.length() + ")");

            Map<String, String> leadData = new HashMap<>();
            leadData.put("contactNo", foundPhone);

            // Fast name extraction with limited search
            String name = extractNameFast(window, phoneIndex - windowStart);

            if (name != null && !name.trim().isEmpty()) {
                leadData.put("name", name.trim());
                Log.d(TAG, "📞 [NATIVE_SEARCH] Fast extracted name: " + name);
            } else {
                leadData.put("name", "Unknown Contact");
            }

            // Quick ID extraction - pass the extracted name for better scoring
            String extractedName = name != null ? name.trim() : "";
            String id = extractIdFastWithName(window, extractedName);
            leadData.put("id", id != null ? id : "unknown-" + System.currentTimeMillis());

            return leadData;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error extracting from chunk: " + e.getMessage());
            return null;
        }
    }

    private String cleanPhoneNumber(String phoneNumber) {
        if (phoneNumber == null) return "";
        // Remove all non-digit characters except +
        return phoneNumber.replaceAll("[^\\d+]", "");
    }

    private Map<String, String> findLeadByPhoneNumber(String content, String cleanPhoneNumber, String originalPhoneNumber) {
        Log.d(TAG, "📞 [NATIVE_SEARCH] Searching for phone number in content");

        try {
            // Look for the phone number in various formats
            String[] searchPatterns = {
                cleanPhoneNumber,
                originalPhoneNumber,
                cleanPhoneNumber.substring(Math.max(0, cleanPhoneNumber.length() - 10)), // Last 10 digits
                cleanPhoneNumber.replaceFirst("^\\+91", ""), // Remove +91 country code
                cleanPhoneNumber.replaceFirst("^91", ""), // Remove 91 country code
            };

            for (String pattern : searchPatterns) {
                if (pattern.length() < 7) continue; // Skip very short patterns

                int phoneIndex = content.indexOf(pattern);
                if (phoneIndex != -1) {
                    Log.d(TAG, "📞 [NATIVE_SEARCH] Found phone pattern: " + pattern + " at index: " + phoneIndex);

                    // Try to extract lead data around this phone number
                    Map<String, String> leadData = extractLeadData(content, phoneIndex, pattern);
                    if (leadData != null) {
                        return leadData;
                    }
                }
            }

            Log.d(TAG, "📞 [NATIVE_SEARCH] No matching phone number found in database");
            return null;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error searching for phone number: " + e.getMessage());
            return null;
        }
    }

    private Map<String, String> extractLeadData(String content, int phoneIndex, String foundPhone) {
        try {
            // Define larger search window around the phone number for better context
            int windowStart = Math.max(0, phoneIndex - 1000);
            int windowEnd = Math.min(content.length(), phoneIndex + 1000);
            String window = content.substring(windowStart, windowEnd);

            Log.d(TAG, "📞 [NATIVE_SEARCH] Extracting lead data around phone: " + foundPhone);
            Log.d(TAG, "📞 [NATIVE_SEARCH] Search window size: " + window.length() + " characters");

            // Look for common patterns that might indicate name and ID
            Map<String, String> leadData = new HashMap<>();
            leadData.put("contactNo", foundPhone);

            // Try multiple strategies to find the name
            String name = null;

            // Strategy 1: Look for name in immediate vicinity
            name = extractName(window, phoneIndex - windowStart);

            // Strategy 2: If no name found, try looking for Hive record structure
            if (name == null) {
                name = extractNameFromHiveStructure(window, phoneIndex - windowStart);
            }

            // Strategy 3: If still no name, try broader pattern matching
            if (name == null) {
                name = extractNameWithBroaderSearch(window);
            }

            if (name != null && !name.trim().isEmpty()) {
                leadData.put("name", name.trim());
                Log.d(TAG, "📞 [NATIVE_SEARCH] Extracted name: " + name);
            } else {
                leadData.put("name", "Unknown Contact");
                Log.d(TAG, "📞 [NATIVE_SEARCH] Could not extract name, using default");
            }

            // Try to find an ID (look for UUID-like patterns)
            String id = extractId(window);
            if (id != null && !id.trim().isEmpty()) {
                leadData.put("id", id.trim());
                Log.d(TAG, "📞 [NATIVE_SEARCH] Extracted ID from Hive file: " + id);
                Log.d(TAG, "📞 [NATIVE_SEARCH] ⚠️ WARNING: This ID was extracted using regex from raw Hive file content and may not be accurate!");
            } else {
                String fallbackId = "unknown-" + System.currentTimeMillis();
                leadData.put("id", fallbackId);
                Log.d(TAG, "📞 [NATIVE_SEARCH] No ID found, using fallback: " + fallbackId);
            }

            return leadData;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error extracting lead data: " + e.getMessage());
            return null;
        }
    }

    private String extractNameFromHiveStructure(String window, int phoneRelativeIndex) {
        try {
            Log.d(TAG, "📞 [NATIVE_SEARCH] Trying Hive structure-based name extraction");

            // Hive often stores data in a structured format
            // Look for patterns like: [length][data][length][data]
            // Names often appear before contact numbers in the structure

            // Look backwards from phone number for potential name data
            int searchStart = Math.max(0, phoneRelativeIndex - 200);
            String beforePhone = window.substring(searchStart, phoneRelativeIndex);

            Log.d(TAG, "📞 [NATIVE_SEARCH] Hive structure search text: " + beforePhone);

            // Look for text chunks separated by control characters or length indicators
            String[] delimiters = {
                "[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F-\\xFF]+",
                "\\$+",  // Dollar signs
                "\\s{2,}", // Multiple spaces
                "\\|+"   // Pipes
            };

            for (String delimiter : delimiters) {
                String[] chunks = beforePhone.split(delimiter);

                // Process chunks in reverse order (closest to phone number first)
                for (int i = chunks.length - 1; i >= 0; i--) {
                    String chunk = chunks[i].trim();
                    if (chunk.length() > 0) {
                        // Check both strict and case-insensitive validation
                        if (isLikelyName(chunk)) {
                            Log.d(TAG, "📞 [NATIVE_SEARCH] Found name in Hive structure: " + chunk);
                            return chunk;
                        } else if (isLikelyNameIgnoreCase(chunk)) {
                            String properCase = toProperCase(chunk);
                            Log.d(TAG, "📞 [NATIVE_SEARCH] Found name in Hive structure (case-corrected): " + chunk + " -> " + properCase);
                            return properCase;
                        }
                    }
                }
            }

            return null;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error in Hive structure extraction: " + e.getMessage());
            return null;
        }
    }

    private String extractNameWithBroaderSearch(String window) {
        try {
            Log.d(TAG, "📞 [NATIVE_SEARCH] Trying broader name search");

            // Look for any text that looks like a name in the entire window
            // Enhanced patterns to better capture names like "Sufi", "sufiyan", etc.
            java.util.regex.Pattern[] namePatterns = {
                // Proper case names (like "Sufi", "John Doe")
                java.util.regex.Pattern.compile(
                    "([A-Z][a-z]{1,}(?:\\s+[A-Z][a-z]{0,}){0,3})",
                    java.util.regex.Pattern.MULTILINE
                ),
                // Lowercase names (like "sufi", "sufiyan")
                java.util.regex.Pattern.compile(
                    "([a-z]{2,}(?:\\s+[a-z]{1,}){0,3})",
                    java.util.regex.Pattern.MULTILINE
                ),
                // Mixed case names
                java.util.regex.Pattern.compile(
                    "([a-zA-Z]{2,}(?:\\s+[a-zA-Z]{1,}){0,3})",
                    java.util.regex.Pattern.MULTILINE
                ),
                // Single word names (common Indian names)
                java.util.regex.Pattern.compile(
                    "([a-zA-Z]{3,15})",
                    java.util.regex.Pattern.MULTILINE
                )
            };

            String bestName = null;
            int bestScore = 0;

            for (java.util.regex.Pattern namePattern : namePatterns) {
                java.util.regex.Matcher matcher = namePattern.matcher(window);

                while (matcher.find()) {
                    String candidate = matcher.group(1).trim();

                    // Check both strict and case-insensitive validation
                    boolean isValid = false;
                    String finalName = candidate;

                    if (isLikelyName(candidate)) {
                        isValid = true;
                    } else if (isLikelyNameIgnoreCase(candidate)) {
                        finalName = toProperCase(candidate);
                        isValid = true;
                    }

                    if (isValid) {
                        // Score based on length and position (prefer longer names)
                        int score = finalName.length();
                        if (score > bestScore) {
                            bestScore = score;
                            bestName = finalName;
                        }
                        Log.d(TAG, "📞 [NATIVE_SEARCH] Found name candidate: " + candidate + " -> " + finalName + " (score: " + score + ")");
                    }
                }
            }

            if (bestName != null) {
                Log.d(TAG, "📞 [NATIVE_SEARCH] Selected best name: " + bestName);
            }

            return bestName;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error in broader search: " + e.getMessage());
            return null;
        }
    }

    private String extractName(String window, int phoneRelativeIndex) {
        try {
            Log.d(TAG, "📞 [NATIVE_SEARCH] Extracting name from window around phone number");

            // Look for text patterns that might be names both before and after the phone
            String beforePhone = window.substring(0, Math.min(phoneRelativeIndex, window.length()));
            String afterPhone = window.substring(Math.min(phoneRelativeIndex + 50, window.length()));

            // Try multiple extraction strategies
            String name = null;

            // Strategy 1: Look for names in the immediate vicinity (within 100 chars)
            int searchStart = Math.max(0, phoneRelativeIndex - 100);
            int searchEnd = Math.min(window.length(), phoneRelativeIndex + 100);
            String nearbyText = window.substring(searchStart, searchEnd);

            Log.d(TAG, "📞 [NATIVE_SEARCH] Nearby text sample: " + nearbyText.substring(0, Math.min(200, nearbyText.length())));

            // Strategy 2: Split by various delimiters and look for name patterns
            String[] delimiters = {
                "[\\x00-\\x1F\\x7F-\\xFF]+",  // Control characters
                "\\s{2,}",                     // Multiple spaces
                "[\\x00\\x01\\x02\\x03\\x04\\x05\\x06\\x07\\x08\\x0B\\x0C\\x0E\\x0F\\x10\\x11\\x12\\x13\\x14\\x15\\x16\\x17\\x18\\x19\\x1A\\x1B\\x1C\\x1D\\x1E\\x1F]+", // Specific control chars
                "\\|",                         // Pipe separator
                "\\t",                         // Tab
                "\\$"                          // Dollar sign (common in Hive data)
            };

            for (String delimiter : delimiters) {
                String[] parts = nearbyText.split(delimiter);
                for (String part : parts) {
                    part = part.trim();

                    // Check both original and properly capitalized versions
                    if (isLikelyName(part)) {
                        Log.d(TAG, "📞 [NATIVE_SEARCH] Found potential name: " + part);
                        if (name == null || part.length() > name.length()) {
                            name = part;
                        }
                    } else if (isLikelyNameIgnoreCase(part)) {
                        // Convert to proper case and check again
                        String properCase = toProperCase(part);
                        Log.d(TAG, "📞 [NATIVE_SEARCH] Found potential name (case-corrected): " + part + " -> " + properCase);
                        if (name == null || properCase.length() > name.length()) {
                            name = properCase;
                        }
                    }
                }
            }

            // Strategy 3: Look for common name patterns using regex
            if (name == null) {
                java.util.regex.Pattern namePattern = java.util.regex.Pattern.compile(
                    "([A-Z][a-z]+(?:\\s+[A-Z][a-z]*)*)",
                    java.util.regex.Pattern.MULTILINE
                );
                java.util.regex.Matcher matcher = namePattern.matcher(nearbyText);
                while (matcher.find()) {
                    String candidate = matcher.group(1).trim();
                    if (isLikelyName(candidate)) {
                        Log.d(TAG, "📞 [NATIVE_SEARCH] Found regex name: " + candidate);
                        if (name == null || candidate.length() > name.length()) {
                            name = candidate;
                        }
                    }
                }
            }

            // Strategy 4: Look for text between common field separators
            if (name == null) {
                String[] fieldPatterns = {
                    "name[^a-zA-Z]*([A-Z][a-zA-Z\\s]+)",
                    "([A-Z][a-zA-Z\\s]+)[^a-zA-Z]*contact",
                    "([A-Z][a-zA-Z\\s]+)[^a-zA-Z]*phone",
                    "([A-Z][a-zA-Z\\s]+)[^a-zA-Z]*\\+?\\d"
                };

                for (String pattern : fieldPatterns) {
                    java.util.regex.Pattern fieldPattern = java.util.regex.Pattern.compile(
                        pattern,
                        java.util.regex.Pattern.CASE_INSENSITIVE
                    );
                    java.util.regex.Matcher fieldMatcher = fieldPattern.matcher(nearbyText);
                    if (fieldMatcher.find()) {
                        String candidate = fieldMatcher.group(1).trim();
                        if (isLikelyName(candidate)) {
                            Log.d(TAG, "📞 [NATIVE_SEARCH] Found field-based name: " + candidate);
                            name = candidate;
                            break;
                        }
                    }
                }
            }

            if (name != null) {
                Log.d(TAG, "📞 [NATIVE_SEARCH] Successfully extracted name: " + name);
                return name;
            } else {
                Log.d(TAG, "📞 [NATIVE_SEARCH] Could not extract name from window");
                return null;
            }

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error extracting name: " + e.getMessage());
            return null;
        }
    }

    private boolean isLikelyName(String text) {
        if (text == null || text.length() < 2 || text.length() > 50) {
            return false;
        }

        // Clean the text
        text = text.trim();

        // Reject if empty after trimming
        if (text.isEmpty()) {
            return false;
        }

        // Reject common non-name patterns
        String lowerText = text.toLowerCase();
        String[] rejectPatterns = {
            "null", "true", "false", "undefined", "contact", "phone", "number",
            "lead", "data", "id", "uuid", "string", "int", "bool", "date",
            "created", "updated", "modified", "deleted", "sync", "time",
            "api", "json", "xml", "http", "www", "com", "org", "net"
        };

        for (String pattern : rejectPatterns) {
            if (lowerText.equals(pattern) || lowerText.contains(pattern + " ") || lowerText.contains(" " + pattern)) {
                return false;
            }
        }

        // Reject if contains too many numbers
        if (text.matches(".*\\d{3,}.*")) {
            return false;
        }

        // Reject if contains special characters (except spaces, dots, apostrophes)
        if (text.matches(".*[^a-zA-Z\\s\\.'].*")) {
            return false;
        }

        // Check if it contains mostly letters and spaces
        String lettersOnly = text.replaceAll("[^a-zA-Z]", "");
        if (lettersOnly.length() < text.length() * 0.7) {
            return false;
        }

        // Must start with a capital letter (typical for names)
        if (!Character.isUpperCase(text.charAt(0))) {
            return false;
        }

        // Check for reasonable name patterns
        // Names typically have 1-4 words, each starting with capital letter
        String[] words = text.split("\\s+");
        if (words.length > 4) {
            return false;
        }

        for (String word : words) {
            if (word.length() < 1) continue;
            // Each word should start with capital letter for proper names
            if (!Character.isUpperCase(word.charAt(0))) {
                return false;
            }
            // Word should be mostly letters
            String wordLetters = word.replaceAll("[^a-zA-Z]", "");
            if (wordLetters.length() < word.length() * 0.8) {
                return false;
            }
        }

        Log.d(TAG, "📞 [NATIVE_SEARCH] Text '" + text + "' passed name validation");
        return true;
    }

    private boolean isLikelyNameIgnoreCase(String text) {
        if (text == null || text.length() < 2 || text.length() > 50) {
            return false;
        }

        // Clean the text
        text = text.trim();

        // Reject if empty after trimming
        if (text.isEmpty()) {
            return false;
        }

        // Reject common non-name patterns (case insensitive)
        String lowerText = text.toLowerCase();
        String[] rejectPatterns = {
            "null", "true", "false", "undefined", "contact", "phone", "number",
            "lead", "data", "id", "uuid", "string", "int", "bool", "date",
            "created", "updated", "modified", "deleted", "sync", "time",
            "api", "json", "xml", "http", "www", "com", "org", "net"
        };

        for (String pattern : rejectPatterns) {
            if (lowerText.equals(pattern) || lowerText.contains(pattern + " ") || lowerText.contains(" " + pattern)) {
                return false;
            }
        }

        // Reject if contains too many numbers
        if (text.matches(".*\\d{3,}.*")) {
            return false;
        }

        // Reject if contains special characters (except spaces, dots, apostrophes)
        if (text.matches(".*[^a-zA-Z\\s\\.'].*")) {
            return false;
        }

        // Check if it contains mostly letters and spaces (relaxed for short names)
        String lettersOnly = text.replaceAll("[^a-zA-Z]", "");
        double letterRatio = (double) lettersOnly.length() / text.length();

        // For short names (like "Sufi"), be more lenient
        double requiredRatio = text.length() <= 6 ? 0.6 : 0.7;
        if (letterRatio < requiredRatio) {
            return false;
        }

        // Check for reasonable name patterns (case insensitive)
        // Names typically have 1-4 words
        String[] words = text.split("\\s+");
        if (words.length > 4) {
            return false;
        }

        for (String word : words) {
            if (word.length() < 1) continue;
            // Word should be mostly letters (relaxed for short words)
            String wordLetters = word.replaceAll("[^a-zA-Z]", "");
            double wordLetterRatio = (double) wordLetters.length() / word.length();
            double requiredWordRatio = word.length() <= 4 ? 0.75 : 0.8;
            if (wordLetterRatio < requiredWordRatio) {
                return false;
            }
        }

        Log.d(TAG, "📞 [NATIVE_SEARCH] Text '" + text + "' passed case-insensitive name validation");
        return true;
    }

    private String toProperCase(String text) {
        if (text == null || text.trim().isEmpty()) {
            return text;
        }

        String[] words = text.trim().split("\\s+");
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < words.length; i++) {
            String word = words[i];
            if (word.length() > 0) {
                // Capitalize first letter, lowercase the rest
                String properWord = Character.toUpperCase(word.charAt(0)) +
                                  (word.length() > 1 ? word.substring(1).toLowerCase() : "");
                result.append(properWord);

                if (i < words.length - 1) {
                    result.append(" ");
                }
            }
        }

        return result.toString();
    }

    private String extractId(String window) {
        try {
            Log.d(TAG, "📞 [NATIVE_SEARCH] Extracting ID from window (length: " + window.length() + ")");
            Log.d(TAG, "📞 [NATIVE_SEARCH] Window content: " + window);

            // Look for UUID-like patterns (8-4-4-4-12 format)
            java.util.regex.Pattern uuidPattern = java.util.regex.Pattern.compile(
                "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"
            );

            java.util.regex.Matcher matcher = uuidPattern.matcher(window);
            String bestId = null;
            int bestScore = -1;

            // Find all UUIDs and score them based on proximity to name and phone
            while (matcher.find()) {
                String foundId = matcher.group();
                int position = matcher.start();

                Log.d(TAG, "📞 [NATIVE_SEARCH] Found potential ID: " + foundId + " at position " + position);

                // Calculate score based on multiple factors
                int score = calculateIdScore(window, foundId, position);

                Log.d(TAG, "📞 [NATIVE_SEARCH] ID score for " + foundId + ": " + score);

                if (score > bestScore) {
                    bestScore = score;
                    bestId = foundId;
                    Log.d(TAG, "📞 [NATIVE_SEARCH] New best ID candidate: " + foundId + " (score: " + score + ")");
                }
            }

            if (bestId != null) {
                Log.d(TAG, "📞 [NATIVE_SEARCH] Selected best ID: " + bestId + " (score: " + bestScore + ")");
                return bestId;
            }

            Log.d(TAG, "📞 [NATIVE_SEARCH] No UUID found in window");
            return null;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error extracting ID: " + e.getMessage());
            return null;
        }
    }

    private int calculateIdScore(String window, String id, int position) {
        int score = 0;

        // Look for the ID appearing multiple times (higher score for duplicates)
        int occurrences = 0;
        int index = window.indexOf(id);
        while (index != -1) {
            occurrences++;
            index = window.indexOf(id, index + 1);
        }
        score += occurrences * 100; // 100 points per occurrence (increased weight)

        // Look for proximity to the phone number (which should be around the middle of the window)
        int phonePosition = window.length() / 2; // Assume phone is roughly in the middle
        int distanceFromPhone = Math.abs(position - phonePosition);

        // Prefer IDs closer to the phone number (within 100 characters gets bonus)
        if (distanceFromPhone <= 100) {
            score += 50; // 50 points for being close to phone
        }

        // Look for proximity to name patterns (letters) - this indicates the ID is part of the same record
        String beforeId = window.substring(Math.max(0, position - 50), position);
        String afterId = window.substring(position + id.length(), Math.min(window.length(), position + id.length() + 50));

        // Check if there are alphabetic characters nearby (indicating a name)
        if (beforeId.matches(".*[a-zA-Z]+.*") || afterId.matches(".*[a-zA-Z]+.*")) {
            score += 30; // 30 points for being near text
        }

        // Look for the name "sufi" or "Sufi" near this ID (specific to this case)
        String nearbyText = window.substring(Math.max(0, position - 100), Math.min(window.length(), position + id.length() + 100));
        if (nearbyText.toLowerCase().contains("sufi")) {
            score += 200; // 200 points for being near the correct name
            Log.d(TAG, "📞 [NATIVE_SEARCH] ID " + id + " is near 'sufi' - adding 200 bonus points");
        }

        // Small position bonus (much reduced weight compared to occurrences and proximity)
        score += Math.max(0, 20 - (position / 50)); // Much smaller position bonus

        Log.d(TAG, "📞 [NATIVE_SEARCH] ID " + id + " scoring: occurrences=" + occurrences + ", position=" + position + ", distanceFromPhone=" + distanceFromPhone + ", total=" + score);

        return score;
    }

    private int calculateIdScoreWithName(String window, String id, int position, String extractedName) {
        int score = 0;

        // Look for the ID appearing multiple times (higher score for duplicates)
        int occurrences = 0;
        int index = window.indexOf(id);
        while (index != -1) {
            occurrences++;
            index = window.indexOf(id, index + 1);
        }
        score += occurrences * 100; // 100 points per occurrence (increased weight)

        // Look for proximity to the phone number (which should be around the middle of the window)
        int phonePosition = window.length() / 2; // Assume phone is roughly in the middle
        int distanceFromPhone = Math.abs(position - phonePosition);

        // Prefer IDs closer to the phone number (within 100 characters gets bonus)
        if (distanceFromPhone <= 100) {
            score += 50; // 50 points for being close to phone
        }

        // Look for proximity to name patterns (letters) - this indicates the ID is part of the same record
        String beforeId = window.substring(Math.max(0, position - 50), position);
        String afterId = window.substring(position + id.length(), Math.min(window.length(), position + id.length() + 50));

        // Check if there are alphabetic characters nearby (indicating a name)
        if (beforeId.matches(".*[a-zA-Z]+.*") || afterId.matches(".*[a-zA-Z]+.*")) {
            score += 30; // 30 points for being near text
        }

        // Generic name proximity bonus - look for the extracted name very close to this ID
        if (!extractedName.isEmpty()) {
            // Check in a smaller, more precise window around the ID
            String immediateText = window.substring(Math.max(0, position - 50), Math.min(window.length(), position + id.length() + 50));

            // Look for the name in the immediate vicinity (within 50 characters)
            if (immediateText.toLowerCase().contains(extractedName.toLowerCase())) {
                // Additional check: make sure this is the closest ID to the name
                int namePosition = immediateText.toLowerCase().indexOf(extractedName.toLowerCase());
                int idPositionInText = Math.max(0, position - Math.max(0, position - 50));
                int distanceToName = Math.abs(namePosition - idPositionInText);

                if (distanceToName <= 30) { // Name must be within 30 characters of the ID
                    score += 200; // 200 points for being very close to the extracted name
                    Log.d(TAG, "📞 [NATIVE_SEARCH] ID " + id + " is very close to extracted name '" + extractedName + "' (distance: " + distanceToName + ") - adding 200 bonus points");
                } else {
                    Log.d(TAG, "📞 [NATIVE_SEARCH] ID " + id + " found name '" + extractedName + "' but too far (distance: " + distanceToName + ") - no bonus");
                }
            }
        }

        // Small position bonus (much reduced weight compared to occurrences and proximity)
        score += Math.max(0, 20 - (position / 50)); // Much smaller position bonus

        Log.d(TAG, "📞 [NATIVE_SEARCH] ID " + id + " scoring: occurrences=" + occurrences + ", position=" + position + ", distanceFromPhone=" + distanceFromPhone + ", extractedName='" + extractedName + "', total=" + score);

        return score;
    }

    private String extractNameFast(String window, int phoneRelativeIndex) {
        try {
            // Fast name extraction with improved processing
            int searchStart = Math.max(0, phoneRelativeIndex - 200);
            String beforePhone = window.substring(searchStart, Math.min(phoneRelativeIndex, window.length()));

            Log.d(TAG, "📞 [NATIVE_SEARCH] Fast extraction from text: " + beforePhone.substring(Math.max(0, beforePhone.length() - 100)));

            // Enhanced delimiters for better parsing
            String[] delimiters = {"\\$+", "[\\x00-\\x1F]+", "\\s{2,}", "\\|", "\\}", "\\{", "\\]", "\\["};

            String bestName = null;
            int bestScore = 0;

            for (String delimiter : delimiters) {
                String[] parts = beforePhone.split(delimiter);

                // Check last few parts (closest to phone) with scoring
                for (int i = Math.max(0, parts.length - 5); i < parts.length; i++) {
                    String part = parts[i].trim();
                    if (part.length() >= 2 && part.length() <= 30) {
                        // Enhanced validation
                        String lettersOnly = part.replaceAll("[^a-zA-Z\\s]", "");
                        if (lettersOnly.length() >= part.length() * 0.6) {
                            // Check if it's not a common data value
                            String lower = part.toLowerCase();
                            if (!lower.matches(".*(null|true|false|id|uuid|contact|phone|data|lead|created|updated|modified|deleted|sync|time|api|json|xml|http|www|com|org|net).*")) {

                                // Score the potential name
                                int score = scoreNameCandidate(part);

                                if (score > bestScore) {
                                    String result = isLikelyNameIgnoreCase(part) ? toProperCase(part) :
                                                  (isLikelyName(part) ? part : null);
                                    if (result != null) {
                                        bestName = result;
                                        bestScore = score;
                                        Log.d(TAG, "📞 [NATIVE_SEARCH] Found better name candidate: " + result + " (score: " + score + ")");
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (bestName != null) {
                Log.d(TAG, "📞 [NATIVE_SEARCH] Fast found name: " + bestName);
                return bestName;
            }

            return null;

        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error in fast name extraction: " + e.getMessage());
            return null;
        }
    }

    private int scoreNameCandidate(String candidate) {
        int score = 0;

        // Prefer names with proper capitalization
        if (Character.isUpperCase(candidate.charAt(0))) {
            score += 10;
        }

        // Prefer names with reasonable length
        if (candidate.length() >= 3 && candidate.length() <= 20) {
            score += 5;
        }

        // Prefer names with mostly letters
        String lettersOnly = candidate.replaceAll("[^a-zA-Z]", "");
        double letterRatio = (double) lettersOnly.length() / candidate.length();
        score += (int) (letterRatio * 10);

        // Prefer names without numbers
        if (!candidate.matches(".*\\d.*")) {
            score += 5;
        }

        // Prefer names that look like real names (common patterns)
        if (candidate.matches("^[A-Z][a-z]+(?:\\s+[A-Z][a-z]*)*$")) {
            score += 15;
        }

        // Penalize very short or very long candidates
        if (candidate.length() < 2 || candidate.length() > 25) {
            score -= 10;
        }

        return score;
    }

    private String extractIdFast(String window) {
        return extractIdFastWithName(window, "");
    }

    private String extractIdFastWithName(String window, String extractedName) {
        try {
            Log.d(TAG, "📞 [NATIVE_SEARCH] Fast extracting ID from window (length: " + window.length() + ") with name: '" + extractedName + "'");
            Log.d(TAG, "📞 [NATIVE_SEARCH] Window content: " + window);

            // Enhanced UUID pattern search
            java.util.regex.Pattern uuidPattern = java.util.regex.Pattern.compile(
                "[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}"
            );

            java.util.regex.Matcher matcher = uuidPattern.matcher(window);
            String bestId = null;
            int bestScore = -1;

            // Find all UUIDs and score them based on multiple factors
            while (matcher.find()) {
                String foundId = matcher.group();
                int position = matcher.start();

                Log.d(TAG, "📞 [NATIVE_SEARCH] Found potential ID: " + foundId + " at position " + position);

                // Calculate score based on multiple factors
                int score = calculateIdScoreWithName(window, foundId, position, extractedName);

                Log.d(TAG, "📞 [NATIVE_SEARCH] ID score for " + foundId + ": " + score);

                if (score > bestScore) {
                    bestScore = score;
                    bestId = foundId;
                    Log.d(TAG, "📞 [NATIVE_SEARCH] New best ID candidate: " + foundId + " (score: " + score + ")");
                }
            }

            if (bestId != null) {
                Log.d(TAG, "📞 [NATIVE_SEARCH] Selected best ID: " + bestId + " (score: " + bestScore + ")");
                return bestId;
            }

            Log.d(TAG, "📞 [NATIVE_SEARCH] No valid UUID found in window");
            return null;
        } catch (Exception e) {
            Log.e(TAG, "📞 [NATIVE_SEARCH] Error extracting ID: " + e.getMessage());
            return null;
        }
    }


    private void callFindContactApi(String contactNo, UUID userId) {
        new Thread(() -> {
            try {
                String apiUrl = baseUrl + "v2/leadcalllog/findlead";
                String encodedPhoneNumber = URLEncoder.encode(contactNo, StandardCharsets.UTF_8.toString());
                String query = "?ContactNumber=" + encodedPhoneNumber + "&AssignToUserId=" + userId.toString();
                URL url = new URL(apiUrl + query);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
                String tenantIdString = preferences.getString("TenantId", "");
                connection.setRequestProperty("tenant", tenantIdString);
                connection.setConnectTimeout(30000);
                connection.setReadTimeout(30000);

                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    Scanner scanner = new Scanner(connection.getInputStream());
                    String response = scanner.useDelimiter("\\A").next();
                    scanner.close();

                    JSONObject jsonResponse = new JSONObject(response);
                    Log.d(TAG, "Secondary API Response: " + jsonResponse.toString());

                    JSONObject data = jsonResponse.getJSONObject("data");

                    // Extract the ID from the response
                    String entityId = data.optString("id", "");
                    Log.d(TAG, "Extracted entityId: " + entityId);

                    // Extract all required information
                    String name = data.optString("name", "Unknown Caller");
                    String message = getMessage(name, contactNo, preferences);
                    String description = getDescription(data);
                    String status = getLeadStatus(data);
                    String projects = getProjects(data);

                    if(name.isEmpty() || name.equals("null")){
                        return;
                    }
                    // Show overlay with all information including the ID
                    showOverlayWithDetails(context, message, description, status, projects, entityId);
                } else {
                    Log.e(TAG, "Secondary API call failed: " + responseCode);
                }
            } catch (Exception e) {
                Log.e(TAG, "Exception during secondary API call: " + e.getMessage());
            }
        }).start();
    }

    private String getMessage(String name, String phoneNumber, SharedPreferences preferences) {
        boolean didRing = preferences.getBoolean("Ringing", false);
        boolean callDialed = preferences.getBoolean("CallDialed", false);
        String messageName = (name == null || name.isEmpty()) ? phoneNumber : name;

        if (didRing) {
            return messageName;
        } else if (callDialed) {
            return messageName;
        }
        return "";
    }

    private String getDescription(JSONObject data) {
        try {
            StringBuilder description = new StringBuilder();

            // Get PropertyType
            if (data.has("propertyType") && !data.isNull("propertyType")) {
                JSONObject propertyType = data.getJSONObject("propertyType");
                description.append(propertyType.optString("displayName", ""));
            } else if (data.has("basePropertyType") && !data.isNull("basePropertyType")) {
                JSONObject basePropertyType = data.getJSONObject("basePropertyType");
                description.append(basePropertyType.optString("displayName", ""));
            }

            // Get BHK details
            int noOfBHK = data.optInt("noOfBHK", 0);
            if (noOfBHK > 0) {
                if (description.length() > 0) description.append(" • ");
                description.append(noOfBHK).append(" BHK");
            }

            // Get Budget
            long upperBudget = data.optLong("upperBudget", 0);
            if (upperBudget > 0) {
                if (description.length() > 0) description.append(" • ");
                description.append(formatBudget(upperBudget));
            }

            return description.toString();
        } catch (Exception e) {
            Log.e(TAG, "Error getting description: " + e.getMessage());
            return "";
        }
    }

    private String getLeadStatus(JSONObject data) {
        try {
            if (data.has("status") && !data.isNull("status")) {
                JSONObject status = data.getJSONObject("status");
                String displayName = status.optString("displayName", "");
                String statusType = status.optString("status", "").toLowerCase();
                String scheduledDate = data.optString("scheduledDate", "");

                if (!displayName.isEmpty()) {
                    if (statusType.equals("site_visit_scheduled") ||
                        statusType.equals("meeting_scheduled") ||
                        statusType.equals("callback")) {
                        return displayName + (!scheduledDate.isEmpty() ? " on " + scheduledDate : "");
                    }
                    return displayName;
                }
            }
            return "";
        } catch (Exception e) {
            Log.e(TAG, "Error getting status: " + e.getMessage());
            return "";
        }
    }

    private String getProjects(JSONObject data) {
        try {
            if (data.has("projects") && !data.isNull("projects")) {
                JSONArray projects = data.getJSONArray("projects");
                if (projects.length() > 0) {
                    StringBuilder result = new StringBuilder();
                    if (projects.length() <= 2) {
                        for (int i = 0; i < projects.length(); i++) {
                            if (i > 0) result.append(", ");
                            result.append(projects.getJSONObject(i).optString("name", ""));
                        }
                    } else {
                        result.append(projects.getJSONObject(0).optString("name", ""))
                              .append(", ")
                              .append(projects.getJSONObject(1).optString("name", ""))
                              .append(" + ")
                              .append(projects.length() - 2);
                    }
                    return result.toString();
                }
            }
            return "";
        } catch (Exception e) {
            Log.e(TAG, "Error getting projects: " + e.getMessage());
            return "";
        }
    }

    private String formatBudget(long budget) {
        // Implement budget formatting logic here
        // This is a simple implementation, adjust according to your needs
        if (budget >= 10000000) {
            return (budget / 10000000) + " Cr";
        } else if (budget >= 100000) {
            return (budget / 100000) + " Lac";
        }
        return String.valueOf(budget);
    }

    private void showOverlayWithDetails(Context context, String message, String description,
                                      String status, String projects, String entityId) {
        // Stop existing service first
        context.stopService(new Intent(context, OverlayService.class));

        // Start new service with all details
        Intent overlayIntent = new Intent(context, OverlayService.class);
        overlayIntent.putExtra("name", message);
        overlayIntent.putExtra("propertyDetails", description);
        overlayIntent.putExtra("meetingSchedule", status);
        overlayIntent.putExtra("projects", projects);
        overlayIntent.putExtra("entityId", entityId);
        context.startService(overlayIntent);

        Log.d(TAG, "Starting overlay with details:\n" +
              "Message: " + message + "\n" +
              "Description: " + description + "\n" +
              "Status: " + status + "\n" +
              "Projects: " + projects + "\n" +
              "EntityId: " + entityId);
    }

    private void callFindProspectApi(String contactNo, UUID userId) {
        new Thread(() -> {
            try {
                String apiUrl = baseUrl + "v1/prospectcalllogs/finddata";
                String encodedPhoneNumber = URLEncoder.encode(contactNo, StandardCharsets.UTF_8.toString());
                String query = "?ContactNumber=" + encodedPhoneNumber + "&AssignToUserId=" + userId.toString();
                URL url = new URL(apiUrl + query);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
                String tenantIdString = preferences.getString("TenantId", "");
                connection.setRequestProperty("tenant", tenantIdString);
                connection.setConnectTimeout(30000);
                connection.setReadTimeout(30000);

                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    Scanner scanner = new Scanner(connection.getInputStream());
                    String response = scanner.useDelimiter("\\A").next();
                    scanner.close();

                    JSONObject jsonResponse = new JSONObject(response);
                    Log.d(TAG, "Secondary API Response: " + jsonResponse.toString());

                    JSONObject data = jsonResponse.getJSONObject("data");

                    // Extract the ID from the response
                    String entityId = data.optString("id", "");
                    Log.d(TAG, "Extracted entityId: " + entityId);

                    // Extract all required information
                    String name = data.optString("name", "Unknown Caller");
                    String message = getMessage(name, contactNo, preferences);
                    String description = getDescription(data);
                    String status = getLeadStatus(data);
                    String projects = getProjects(data);

                    if(name.isEmpty() || name.equals("null")){
                        return;
                    }
                    // Show overlay with all information including the ID
                    showOverlayWithDetails(context, message, description, status, projects, entityId);
                } else {
                    Log.e(TAG, "Secondary API call failed: " + responseCode);
                }
            } catch (Exception e) {
                Log.e(TAG, "Exception during secondary API call: " + e.getMessage());
            }
        }).start();
    }

    private void showOverlayWithName(Context context, String name) {
        // Stop existing service first
        context.stopService(new Intent(context, OverlayService.class));

        // Start new service with name
        Intent overlayIntent = new Intent(context, OverlayService.class);
        overlayIntent.putExtra("name", name);
        context.startService(overlayIntent);
        Log.d(TAG, "Starting overlay with name: " + name);
    }

    // Add this method to get call direction
    private String getCallDirection(SharedPreferences preferences) {
        boolean isRinging = preferences.getBoolean("Ringing", false);
        boolean isDialed = preferences.getBoolean("CallDialed", false);

        if (isRinging) {
            return "Incoming";
        } else if (isDialed) {
            return "Outgoing";
        }
        return "Unknown";
    }

    public CallInfo getCallInformation(Context context) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        boolean didRing = preferences.getBoolean("Ringing", false);
        boolean callDialed = preferences.getBoolean("CallDialed", false);
        boolean callPicked = preferences.getBoolean("CallPicked", false);
        boolean missedCall = preferences.getBoolean("MissedCall", false);

        long startTime;
        long endTime;
        String duration = "0";
        CallDirection callDirection = CallDirection.NONE;
        CallStatus callStatus = CallStatus.NONE;

        Log.d(TAG, "📞 [CALL_INFO] Getting call information - didRing: " + didRing + ", callPicked: " + callPicked + ", callDialed: " + callDialed + ", missedCall: " + missedCall);

        try {
            if (didRing && callPicked) {
                startTime = preferences.getLong("RingingStartTime", 0);
                endTime = preferences.getLong("CallEndTime", 0);
                CallLogInfo callInfo = getCallLogs(context, CallType.INCOMING);
                duration = callInfo.phoneDuration;
                callDirection = CallDirection.INCOMING;
                callStatus = CallStatus.ANSWERED;
            } else if (callDialed) {
                startTime = preferences.getLong("CallDialedStartTime", 0);
                endTime = preferences.getLong("CallEndTime", 0);

                long calculatedDurationSeconds = (endTime - startTime) / 1000;
                Log.d(TAG, "📞 [OUTGOING_CALL] Call timestamps - Start: " + new java.util.Date(startTime) + ", End: " + new java.util.Date(endTime) + ", Calculated duration: " + calculatedDurationSeconds + "s");

                // Get call log information with retry mechanism
                CallLogInfo callInfo = getCallLogs(context, CallType.OUTGOING);

                long callLogDuration = 0;
                try {
                    callLogDuration = Long.parseLong(callInfo.phoneDuration);
                } catch (NumberFormatException e) {
                    Log.e(TAG, "📞 [OUTGOING_CALL] Error parsing call log duration: " + e.getMessage());
                }

                Log.d(TAG, "📞 [OUTGOING_CALL] Call log duration: " + callLogDuration + "s, Calculated duration: " + calculatedDurationSeconds + "s");

                callDirection = CallDirection.OUTGOING;

                // Improved call status and duration determination logic
                // Strategy 1: If call log has a valid duration, use it and mark as answered
                if (callLogDuration > 0) {
                    duration = callInfo.phoneDuration;
                    callStatus = CallStatus.ANSWERED;
                    Log.d(TAG, "📞 [OUTGOING_CALL] Using call log duration: " + duration + "s (Status: ANSWERED)");
                }
                // Strategy 2: If call log duration is 0 but calculated duration is significant, trust calculated duration
                else if (calculatedDurationSeconds > 3) {
                    duration = String.valueOf(calculatedDurationSeconds);
                    callStatus = CallStatus.ANSWERED;
                    Log.d(TAG, "📞 [OUTGOING_CALL] Call log duration is 0, but calculated duration is significant (" + calculatedDurationSeconds + "s). Using calculated duration (Status: ANSWERED)");
                }
                // Strategy 3: Both durations are very small - likely a disconnected call
                else {
                    duration = "0";
                    callStatus = CallStatus.DISCONNECTED;
                    Log.d(TAG, "📞 [OUTGOING_CALL] Both call log (" + callLogDuration + "s) and calculated (" + calculatedDurationSeconds + "s) durations are small. Marking as DISCONNECTED");
                }
            } else {
                startTime = preferences.getLong("RingingStartTime", 0);
                endTime = preferences.getLong("CallEndTime", 0);
                CallLogInfo callInfo = getCallLogs(context, CallType.MISSED);

                // For missed calls, always set duration to 0 regardless of what's in the call log
                duration = "0";
                callDirection = didRing ? CallDirection.INCOMING : CallDirection.OUTGOING;
                callStatus = CallStatus.MISSED;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error retrieving call information: " + e.getMessage());
            duration = "0";
            callDirection = CallDirection.NONE;
            callStatus = CallStatus.NONE;
            startTime = 0;
            endTime = 0;
        }

        Log.d(TAG, "📞 [CALL_INFO] Final result - Duration: " + duration + "s, Direction: " + callDirection + ", Status: " + callStatus);
        return new CallInfo(duration, callDirection, startTime, endTime, callStatus);
    }

    public CallLogInfo getCallLogs(Context context, CallType callType) {
        return getCallLogsWithRetry(context, callType, 3, 1000); // Retry 3 times with 1 second delay
    }

    /**
     * Get call logs with retry mechanism to handle timing issues
     */
    private CallLogInfo getCallLogsWithRetry(Context context, CallType callType, int maxRetries, long delayMs) {
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            Log.d(TAG, "📞 [CALL_LOG] Attempt " + attempt + "/" + maxRetries + " to fetch call log");

            CallLogInfo result = getCallLogsInternal(context, callType);

            // If we got a valid duration (not "0"), return it
            if (result != null && !result.phoneDuration.equals("0")) {
                Log.d(TAG, "✅ [CALL_LOG] Found valid duration on attempt " + attempt + ": " + result.phoneDuration);
                return result;
            }

            // If this is not the last attempt, wait before retrying
            if (attempt < maxRetries) {
                Log.d(TAG, "⏳ [CALL_LOG] Duration is 0, waiting " + delayMs + "ms before retry...");
                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        Log.w(TAG, "⚠️ [CALL_LOG] All attempts failed, returning last result");
        return getCallLogsInternal(context, callType);
    }

    /**
     * Internal method to fetch call logs with improved accuracy
     */
    private CallLogInfo getCallLogsInternal(Context context, CallType callType) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        String queryFilter = "";
        switch (callType) {
            case INCOMING:
                queryFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.INCOMING_TYPE;
                break;
            case OUTGOING:
                queryFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.OUTGOING_TYPE;
                break;
            case MISSED:
                queryFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.MISSED_TYPE;
                break;
        }

        // Get the call end time for more accurate matching
        long callEndTime = preferences.getLong("CallEndTime", 0);
        long relevantTimestamp = 0;

        switch (callType) {
            case INCOMING:
                relevantTimestamp = preferences.getLong("RingingStartTime", 0);
                break;
            case OUTGOING:
                relevantTimestamp = preferences.getLong("CallDialedStartTime", 0);
                break;
            case MISSED:
                relevantTimestamp = preferences.getLong("RingingStartTime", 0);
                break;
        }

        // Use a wider time window to ensure we don't miss call logs due to timing differences
        // The system might record call logs at slightly different times than our app detects call end
        long timeWindowStart = Math.max(relevantTimestamp - (5 * 60 * 1000), callEndTime - (5 * 60 * 1000));
        long timeWindowEnd = callEndTime + (5 * 60 * 1000); // 5 minutes after call end to handle delays

        // Add time window to query filter if we have a valid timestamp
        if (relevantTimestamp > 0 && callEndTime > 0) {
            queryFilter += " AND " + CallLog.Calls.DATE + " >= " + timeWindowStart +
                          " AND " + CallLog.Calls.DATE + " <= " + timeWindowEnd;
        }

        String querySorter = CallLog.Calls.DATE + " DESC";
        String phoneNumber = "";
        String phoneDuration = "0";
        long callDate = 0;

        try {
            Log.d(TAG, "📞 [CALL_LOG] Querying call logs with filter: " + queryFilter);
            Log.d(TAG, "📞 [CALL_LOG] Time window: " + new java.util.Date(timeWindowStart) + " to " + new java.util.Date(timeWindowEnd));

            Cursor queryData = context.getContentResolver().query(CallLog.Calls.CONTENT_URI, null, queryFilter, null, querySorter);

            if (queryData != null && queryData.getCount() > 0) {
                Log.d(TAG, "📞 [CALL_LOG] Found " + queryData.getCount() + " matching call logs");

                // Strategy 1: Find the call closest to call end time (most accurate)
                long closestToEndTime = Long.MAX_VALUE;
                int bestEndTimePosition = -1;

                // Strategy 2: Find the call closest to start time (fallback)
                long closestToStartTime = Long.MAX_VALUE;
                int bestStartTimePosition = -1;

                queryData.moveToFirst();
                do {
                    long logCallDate = queryData.getLong(queryData.getColumnIndex(CallLog.Calls.DATE));
                    String logNumber = queryData.getString(queryData.getColumnIndex(CallLog.Calls.NUMBER));
                    String logDuration = queryData.getString(queryData.getColumnIndex(CallLog.Calls.DURATION));

                    Log.d(TAG, "📞 [CALL_LOG] Entry: Number=" + logNumber +
                          ", Duration=" + logDuration +
                          ", Date=" + new java.util.Date(logCallDate));

                    // Check distance to call end time
                    if (callEndTime > 0) {
                        long endTimeDiff = Math.abs(logCallDate - callEndTime);
                        if (endTimeDiff < closestToEndTime) {
                            closestToEndTime = endTimeDiff;
                            bestEndTimePosition = queryData.getPosition();
                        }
                    }

                    // Check distance to start time (fallback)
                    long startTimeDiff = Math.abs(logCallDate - relevantTimestamp);
                    if (startTimeDiff < closestToStartTime) {
                        closestToStartTime = startTimeDiff;
                        bestStartTimePosition = queryData.getPosition();
                    }
                } while (queryData.moveToNext());

                // Choose the best match: prefer end time match if available and reasonable
                int selectedPosition = -1;
                String matchStrategy = "";

                if (bestEndTimePosition >= 0 && closestToEndTime < (60 * 1000)) { // Within 1 minute of end time
                    selectedPosition = bestEndTimePosition;
                    matchStrategy = "end-time (diff: " + (closestToEndTime / 1000) + "s)";
                } else if (bestStartTimePosition >= 0) {
                    selectedPosition = bestStartTimePosition;
                    matchStrategy = "start-time (diff: " + (closestToStartTime / 1000) + "s)";
                }

                // Get the selected call data
                if (selectedPosition >= 0) {
                    queryData.moveToPosition(selectedPosition);
                    phoneNumber = queryData.getString(queryData.getColumnIndex(CallLog.Calls.NUMBER));
                    phoneDuration = queryData.getString(queryData.getColumnIndex(CallLog.Calls.DURATION));
                    callDate = queryData.getLong(queryData.getColumnIndex(CallLog.Calls.DATE));

                    Log.d(TAG, "✅ [CALL_LOG] Selected call using " + matchStrategy +
                          ": Number=" + phoneNumber +
                          ", Duration=" + phoneDuration +
                          ", Date=" + new java.util.Date(callDate));
                } else {
                    Log.w(TAG, "⚠️ [CALL_LOG] No suitable call found within time window");
                }

                queryData.close();
            } else {
                Log.d(TAG, "📞 [CALL_LOG] No matching call logs found");
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ [CALL_LOG] Error querying call logs: " + e.getMessage());
        }

        return new CallLogInfo(phoneNumber, phoneDuration);
    }

    private void addCallLogAsync(String phoneNumber, long startTime, long endTime, double callDuration, CallDirection callDirection, String notes, CallStatus callStatus, String callRecordingBase64, CallLogCallback callback) {
        Log.d(TAG, "🚀 [OFFLINE_QUEUE] addCallLogAsync called for phone: " + phoneNumber + ", direction: " + callDirection + ", status: " + callStatus + ", duration: " + callDuration);
        new Thread(() -> {
            try {
                // Import the OfflineQueueService for offline queue functionality
                com.leadrat.call_detection.call_detection.services.OfflineQueueService offlineQueueService;

                // Check if we should skip sending the recording based on call status
                boolean skipRecording = false;

                // For outgoing disconnected calls, always set duration to 0
                double finalCallDuration = callDuration;
                if (callDirection == CallDirection.OUTGOING && callStatus == CallStatus.DISCONNECTED) {
                    Log.d(TAG, "Outgoing disconnected call - forcing duration to 0 (original: " + callDuration + ")");
                    finalCallDuration = 0;
                    // Also skip recording for disconnected outgoing calls
                    Log.d(TAG, "Skipping recording for disconnected outgoing call");
                    skipRecording = true;
                }

                // Don't send recording for missed incoming calls
                if (callDirection == CallDirection.INCOMING && callStatus == CallStatus.MISSED) {
                    Log.d(TAG, "Skipping recording for missed incoming call");
                    skipRecording = true;
                }

                // If we should skip the recording, set it to null
                final String finalRecording = skipRecording ? null : callRecordingBase64;
                // Use the adjusted call duration
                final double apiCallDuration = finalCallDuration;
                final boolean shouldSkipRecording = skipRecording;

                // Search in local database first
                searchLocalDatabaseForCallLog(phoneNumber, new LocalSearchCallback() {
                    @Override
                    public void onResult(Object result) {
                        if (result != null && result instanceof Map) {
                            @SuppressWarnings("unchecked")
                            Map<String, Object> leadData = (Map<String, Object>) result;

                            String leadId = (String) leadData.get("id");
                            String leadName = (String) leadData.get("name");
                            String contactNo = (String) leadData.get("contactNo");

                            if (leadId != null && !leadId.isEmpty()) {
                                Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] Found entry in local database: " + leadName + " (" + contactNo + ")");
                                Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] Adding call to offline queue: " + leadId);
                                Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] Call details - Direction: " + callDirection + ", Status: " + callStatus + ", Duration: " + apiCallDuration);

                                // Debug: Check and fix isLead field for this contact
                                if (leadName != null && leadName.toLowerCase().contains("rohan")) {
                                    Log.d("CallReceiver", "🔧 [DEBUG] Running debug check for Rohan contact");
                                    try {
                                        DataSyncService.debugCheckAndFixIsLeadField(context, leadName, contactNo);

                                        // Re-search to get updated data
                                        Log.d("CallReceiver", "🔧 [DEBUG] Re-searching for updated data");
                                        leadData = DataSyncService.searchLeadInHiveDatabaseStatic(context, phoneNumber);

                                        // Force trigger Hive to native sync to ensure data is available for future calls
                                        Log.d("CallReceiver", "🔧 [DEBUG] Triggering Hive to native sync to update call detection database");
                                        DataSyncService.forceTriggerHiveToNativeSync(context);

                                    } catch (Exception e) {
                                        Log.e("CallReceiver", "❌ [DEBUG] Error running debug check: " + e.getMessage());
                                    }
                                }

                                // Determine if this is a lead or prospect based on the data from database
                                boolean isLead = true; // Default to true for backward compatibility

                                // Debug: Log all available keys in leadData
                                Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] Available keys in leadData: " + leadData.keySet().toString());

                                if (leadData.containsKey("isLead")) {
                                    Object isLeadValue = leadData.get("isLead");
                                    Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] isLead value from database: " + isLeadValue + " (type: " + (isLeadValue != null ? isLeadValue.getClass().getSimpleName() : "null") + ")");

                                    if (isLeadValue instanceof Boolean) {
                                        isLead = (Boolean) isLeadValue;
                                    } else if (isLeadValue instanceof Integer) {
                                        isLead = ((Integer) isLeadValue) == 1;
                                    } else if (isLeadValue instanceof String) {
                                        isLead = "true".equalsIgnoreCase((String) isLeadValue) || "1".equals(isLeadValue);
                                    }
                                } else {
                                    Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] isLead field not found in database result, defaulting to true");
                                }

                                String entityType = isLead ? "Lead" : "Prospect";
                                Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] Entity type determined: " + entityType + " (isLead=" + isLead + ")");

                                // Check if we have a recording to process
                                if (!shouldSkipRecording && finalRecording != null && !finalRecording.isEmpty()) {
                                    // Save recording to temporary file for offline processing
                                    String recordingFilePath = saveBase64ToTempFile(finalRecording, leadId);

                                    if (recordingFilePath != null) {
                                        // Add recording and call log to offline queue
                                        com.leadrat.call_detection.call_detection.services.OfflineQueueService.addRecordingToQueue(
                                            context, phoneNumber, recordingFilePath, leadId, isLead,
                                            callDirection, callStatus, startTime, endTime, apiCallDuration, notes
                                        );
                                        Log.d("CallReceiver", "✅ [OFFLINE_QUEUE] Call with recording added to queue");
                                    } else {
                                        // If file save failed, add call log without recording
                                        com.leadrat.call_detection.call_detection.services.OfflineQueueService.addCallLogToQueue(
                                            context, phoneNumber, leadId, isLead,
                                            callDirection, callStatus, startTime, endTime, apiCallDuration, notes
                                        );
                                        Log.w("CallReceiver", "⚠️ [OFFLINE_QUEUE] Failed to save recording file, added call log without recording");
                                    }
                                } else {
                                    // Add call log without recording to offline queue
                                    com.leadrat.call_detection.call_detection.services.OfflineQueueService.addCallLogToQueue(
                                        context, phoneNumber, leadId, isLead,
                                        callDirection, callStatus, startTime, endTime, apiCallDuration, notes
                                    );
                                    Log.d("CallReceiver", "✅ [OFFLINE_QUEUE] Call without recording added to queue");
                                }

                                // Check global settings before opening application
                                if (leadName != null && !leadName.trim().isEmpty() && !leadName.equals("Unknown Contact")) {
                                    // Use original ID if valid, otherwise use a fallback ID for app opening
                                    String appOpenId = leadId;
                                    if (appOpenId == null || appOpenId.isEmpty() || appOpenId.startsWith("unknown-")) {
                                        appOpenId = "lead-" + System.currentTimeMillis();
                                        Log.d("CallReceiver", "📱 [APP_OPEN] Generated fallback ID for app opening: " + appOpenId);
                                    }

                                    // Check shouldOpenPopupAfterCall setting before opening application
                                    final String finalAppOpenId = appOpenId;
                                    final boolean finalIsLead = isLead;
                                    isCallRecordingEnabledInBackground((isCallRecordingEnabled, shouldViewOnlyAssigned, shouldOpenPopupAfterCall) -> {
                                        if (shouldOpenPopupAfterCall) {
                                            Log.d("CallReceiver", "📱 [APP_OPEN] Opening application for valid lead: " + leadName + " (ID: " + finalAppOpenId + ") - shouldOpenPopupAfterCall: true");
                                            openApplication(finalIsLead ? 21 : 22, finalAppOpenId);
                                        } else {
                                            Log.d("CallReceiver", "📱 [APP_OPEN] Skipping app open due to shouldOpenPopupAfterCall setting: false");
                                        }
                                    });
                                } else {
                                    Log.d("CallReceiver", "📱 [APP_OPEN] Skipping app open for invalid lead name: " + leadName);
                                }

                                if (callback != null) {
                                    callback.onSuccess();
                                }
                            } else {
                                Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] Invalid lead ID found in local database");
                                if (callback != null) {
                                    callback.onError(new Exception("Invalid lead ID"));
                                }
                            }
                        } else {
                            Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] No entry found in local database for phone number: " + phoneNumber);
                            Log.d("CallReceiver", "📁 [OFFLINE_QUEUE] Skipping call log - not adding to offline queue");
                            if (callback != null) {
                                callback.onSuccess(); // Still call success since this is expected behavior
                            }
                        }
                    }
                });
            } catch (Exception e) {
                Log.e(TAG, "🔥 Error adding call log: " + e.getMessage());
                if (callback != null) {
                    callback.onError(e);
                }
            }
        }).start();
    }


    public void sendBase64ToServer(String base64Data, String leadId, UploadCallback callback) {
        new Thread(() -> {
            try {
                if (base64Data == null || base64Data.isEmpty()) {
                    System.out.println("Cannot upload null or empty base64 data");
                    callback.onFailure("Cannot upload null or empty base64 data");
                    return;
                }

                if (leadId == null || leadId.isEmpty()) {
                    System.out.println("Cannot upload with null or empty leadId");
                    callback.onFailure("Cannot upload with null or empty leadId");
                    return;
                }

                // Construct API URL
                String apiUrlString = "https://prd-lrb-webapi.leadrat.com/api/blobstorage/doc/base64/anonymous/" + bucketName + "/CallRecordings/" + leadId + ".m4a";
                System.out.println("Uploading to URL: " + apiUrlString);

                URL url = new URL(apiUrlString);
                HttpURLConnection conn = (HttpURLConnection) url.openConnection();
                conn.setRequestMethod("POST");
                conn.setRequestProperty("accept", "application/octet-stream");
                conn.setRequestProperty("Content-Type", "application/json");
                conn.setDoOutput(true);
                conn.setConnectTimeout(30000); // 30 seconds timeout
                conn.setReadTimeout(30000);    // 30 seconds timeout

                // JSON body
                String jsonPayload = "[\"" + base64Data + "\"]";
                System.out.println("Sending recording data to server");

                // Send request
                OutputStream os = conn.getOutputStream();
                os.write(jsonPayload.getBytes(StandardCharsets.UTF_8));
                os.flush();
                os.close();

                // Read response
                int responseCode = conn.getResponseCode();
                String responseMessage = conn.getResponseMessage();
                System.out.println("API Response Code: " + responseCode + " - " + responseMessage);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    System.out.println("Received response from server: " + response.toString());

                    try {
                        // Convert response to JSON
                        JSONObject jsonResponse = new JSONObject(response.toString());

                        if (jsonResponse.has("data") && !jsonResponse.isNull("data")) {
                            JSONArray dataArray = jsonResponse.getJSONArray("data"); // Extract "data" array
                            if (dataArray.length() > 0) {
                                String uploadedFilePath = dataArray.getString(0); // Get first element from "data"

                                // Construct final URL
                                String finalUrl = "https://" + bucketName + ".s3.ap-south-1.amazonaws.com/" + uploadedFilePath;
                                System.out.println("Final URL constructed: " + finalUrl);

                                // Local file cleanup is not needed for external recording path

                                callback.onSuccess(finalUrl);
                                System.out.println("File uploaded successfully and local copy deleted");
                                return;
                            }
                        }

                        // If we get here, we couldn't find the data in the response
                        System.out.println("No file path found in API response");
                        callback.onFailure("No file path found in API response");
                    } catch (Exception e) {
                        System.out.println("Error parsing JSON response: " + e.getMessage());
                        callback.onFailure("Error parsing JSON response: " + e.getMessage());
                    }
                } else {
                    // Try to read error response
                    try {
                        BufferedReader reader = new BufferedReader(new InputStreamReader(conn.getErrorStream()));
                        StringBuilder errorResponse = new StringBuilder();
                        String line;
                        while ((line = reader.readLine()) != null) {
                            errorResponse.append(line);
                        }
                        reader.close();
                        System.out.println("Error response: " + errorResponse.toString());
                        callback.onFailure("Upload failed with response code: " + responseCode + " - " + errorResponse.toString());
                    } catch (Exception e) {
                        System.out.println("Upload failed with response code: " + responseCode);
                        callback.onFailure("Upload failed with response code: " + responseCode);
                    }
                }

                conn.disconnect();
            } catch (Exception e) {
                System.out.println("API Call failed: " + e.getMessage());
                callback.onFailure("API Call failed: " + e.getMessage());
            }
        }).start();
    }



    private void addCallLog(LeadCallLogDTO callLogDto) {
        try {
            String apiUrl = baseUrl + "v2/leadcalllog";
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("tenant", PreferenceManager.getDefaultSharedPreferences(context).getString("TenantId", ""));
            connection.setDoOutput(true);

            // Convert timestamps to ISO 8601 format
            SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
            isoFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

            String callStartTime = callLogDto.callStartTime > 0 ? isoFormat.format(new Date(callLogDto.callStartTime)) : null;
            String callEndTime = callLogDto.callEndTime > 0 ? isoFormat.format(new Date(callLogDto.callEndTime)) : null;

            // Use current time as lastModifiedOn (equivalent to createdAt for direct calls)
            String lastModifiedOn = isoFormat.format(new Date(System.currentTimeMillis()));

            JSONObject requestJson = new JSONObject();
            requestJson.put("leadId", callLogDto.leadId);
            requestJson.put("userId", callLogDto.userId.toString());
            requestJson.put("callStartTime", callStartTime);
            requestJson.put("callEndTime", callEndTime);
            requestJson.put("callDuration", callLogDto.callDuration);
            requestJson.put("callDirection", callLogDto.callDirection.ordinal()); // Enum to int
            requestJson.put("notes", callLogDto.notes);
            requestJson.put("callStatus", callLogDto.callStatus.ordinal()); // Enum to int
            requestJson.put("callRecordingUrl", callLogDto.callRecordingUrl); // Enum to int
            requestJson.put("lastModifiedOn", lastModifiedOn);

            OutputStream os = connection.getOutputStream();
            os.write(requestJson.toString().getBytes(StandardCharsets.UTF_8));
            os.close();
            Log.e(TAG, "Add call log json: " + requestJson.toString());

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                Log.d(TAG, "Call log added successfully.");
            } else {
                Log.e(TAG, "Failed to add call log: " + responseCode);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding call log: " + e.getMessage());
        }
    }

    private void addProspectCallLog(ProspectCallLogDTO callLogDto) {
        try {
            String apiUrl = baseUrl + "v1/prospectcalllogs";
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("tenant", PreferenceManager.getDefaultSharedPreferences(context).getString("TenantId", ""));
            connection.setDoOutput(true);

            // Convert timestamps to ISO 8601 format
            SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
            isoFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

            String callStartTime = callLogDto.callStartTime > 0 ? isoFormat.format(new Date(callLogDto.callStartTime)) : null;
            String callEndTime = callLogDto.callEndTime > 0 ? isoFormat.format(new Date(callLogDto.callEndTime)) : null;

            // Use current time as lastModifiedOn (equivalent to createdAt for direct calls)
            String lastModifiedOn = isoFormat.format(new Date(System.currentTimeMillis()));

            JSONObject requestJson = new JSONObject();
            requestJson.put("prospectId", callLogDto.prospectId);
            requestJson.put("userId", callLogDto.userId.toString());
            requestJson.put("callStartTime", callStartTime);
            requestJson.put("callEndTime", callEndTime);
            requestJson.put("callDuration", callLogDto.callDuration);
            requestJson.put("callDirection", callLogDto.callDirection.ordinal()); // Enum to int
            requestJson.put("notes", callLogDto.notes);
            requestJson.put("callStatus", callLogDto.callStatus.ordinal()); // Enum to int
            requestJson.put("callRecordingUrl", callLogDto.callRecordingUrl); // Enum to int
            requestJson.put("lastModifiedOn", lastModifiedOn);

            OutputStream os = connection.getOutputStream();
            os.write(requestJson.toString().getBytes(StandardCharsets.UTF_8));
            os.close();
            Log.e(TAG, "Add call log json prospect: " + requestJson.toString());

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                Log.d(TAG, "Call log added successfully.");
            } else {
                Log.e(TAG, "Failed to add call log: " + responseCode);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error adding call log: " + e.getMessage());
        }
    }

    private void openApplication(int screen, String entityId) {
        try {
            String uri = String.format("app://com.leadrat.black.mobile.droid/main?&screen=%d&id=%s", screen, entityId);
            Intent intent = new Intent(Intent.ACTION_VIEW, android.net.Uri.parse(uri));
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            Log.e(TAG, "Error opening application: " + e.getMessage());
        }
    }

//    private LeadOrDataResult checkLeadOrData(String phoneNumber) {
//        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
//            Log.e(TAG, "Phone number is null or empty, cannot check lead or data");
//            return new LeadOrDataResult(false, false);
//        }
//
//        Log.d(TAG, "Checking lead or data for phone number: " + phoneNumber);
//        try {
//            String apiUrl = baseUrl + "v1/prospectcalllogs/contact";
//            String encodedPhoneNumber = URLEncoder.encode(phoneNumber.trim(), StandardCharsets.UTF_8.toString());
//            Log.d(TAG, "Encoded phone number: " + encodedPhoneNumber);
//
//            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
//            String tenantIdString = preferences.getString("TenantId", "");
//            String userIdString = preferences.getString("UserId", "5170e4fa-e495-491a-abd5-6a65772996eb");
//            Log.d(TAG, "TenantId: " + tenantIdString);
//            Log.d(TAG, "UserId: " + userIdString);
//
//            UUID userId = UUID.fromString(userIdString);
//            URL url = new URL(apiUrl + "?ContactNo=" + encodedPhoneNumber);
//            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//            connection.setRequestMethod("GET");
//            connection.setRequestProperty("tenant", tenantIdString);
//            connection.setConnectTimeout(30000);
//            connection.setReadTimeout(30000);
//
//            int responseCode = connection.getResponseCode();
//            if (responseCode == HttpURLConnection.HTTP_OK) {
//                Scanner scanner = new Scanner(connection.getInputStream());
//                String response = scanner.useDelimiter("\\A").next();
//                scanner.close();
//
//                Log.d(TAG, "hello API Response: " + response);
//
//                JSONObject jsonResponse = new JSONObject(response);
//                JSONObject data = jsonResponse.getJSONObject("data");
//                boolean isLead = data.getBoolean("isLead");
//                boolean isData = data.getBoolean("isData");
//
//                Log.d(TAG, "IsLead: " + isLead + ", IsData: " + isData);
//
//                return new LeadOrDataResult(isLead, isData);
//            } else {
//                Log.e(TAG, "API call failed: " + responseCode);
//            }
//        } catch (Exception e) {
//            Log.e(TAG, "Exception during API call 2: " + e.getMessage(), e);
//        }
//        return new LeadOrDataResult(false, false);
//    }








    public void isCallRecordingEnabledInBackground(CallRecordingCallback callback) {
        isCallRecordingEnabledInBackground(callback, false);
    }

    public void isCallRecordingEnabledInBackground(CallRecordingCallback callback, boolean forceRefresh) {
        new Thread(() -> {
            boolean isEnabled = false;
            boolean shouldViewOnlyAssigned = false;
            boolean shouldOpenPopupAfterCall = true;

            try {
                SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
                String tenantIdString = preferences.getString("TenantId", "");

                if (tenantIdString.isEmpty()) {
                    Log.e(TAG, "TenantId is invalid. Cannot check call recording setting.");
                    callback.onResult(false, false, true);
                    return;
                }

                // Check cache first if not forcing refresh
                if (!forceRefresh) {
                    long lastFetchTime = preferences.getLong("global_settings_last_fetch_" + tenantIdString, 0);
                    long currentTime = System.currentTimeMillis();
                    long cacheValidityDuration = 60 * 1000; // 1 minute in milliseconds

                    if (currentTime - lastFetchTime < cacheValidityDuration) {
                        // Use cached data
                        isEnabled = preferences.getBoolean("cached_isCallRecordingEnabled", false);
                        shouldViewOnlyAssigned = preferences.getBoolean("cached_shouldViewOnlyAssigned", false);
                        shouldOpenPopupAfterCall = preferences.getBoolean("cached_shouldOpenPopupAfterCall", true);

                        Log.d(TAG, "Using cached global settings: isCallRecordingEnabled=" + isEnabled + ", shouldViewOnlyAssigned=" + shouldViewOnlyAssigned + ", shouldOpenPopupAfterCall=" + shouldOpenPopupAfterCall);
                        callback.onResult(isEnabled, shouldViewOnlyAssigned, shouldOpenPopupAfterCall);
                        return;
                    } else {
                        Log.d(TAG, "Cache expired or not found, fetching fresh data from API");
                    }
                } else {
                    Log.d(TAG, "Force refresh requested, fetching fresh data from API");
                }

                // Fetch from API
                String apiUrl = "https://prd-mobile.leadrat.com/api/v1/globalsettings/callsettings/anonymous";
                URL url = new URL(apiUrl);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");

                connection.setRequestProperty("accept", "application/json");
                connection.setRequestProperty("tenant", tenantIdString);

                connection.setConnectTimeout(30000);
                connection.setReadTimeout(30000);

                int responseCode = connection.getResponseCode();
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    Scanner scanner = new Scanner(connection.getInputStream());
                    String response = scanner.useDelimiter("\\A").next();
                    scanner.close();

                    Log.d(TAG, "Call Recording API Response: " + response);

                    JSONObject jsonResponse = new JSONObject(response);
                    JSONObject data = jsonResponse.getJSONObject("data");

                    isEnabled = data.optBoolean("isCallRecordingEnabled", false);
                    shouldViewOnlyAssigned = data.optBoolean("shouldViewOnlyAssigned", false);
                    shouldOpenPopupAfterCall = data.optBoolean("shouldOpenPopupAfterCall", true);

                    Log.d(TAG, "Fresh API data - isCallRecordingEnabled: " + isEnabled + ", shouldViewOnlyAssigned: " + shouldViewOnlyAssigned + ", shouldOpenPopupAfterCall: " + shouldOpenPopupAfterCall);

                    // Cache the fresh data
                    SharedPreferences.Editor editor = preferences.edit();
                    editor.putBoolean("cached_isCallRecordingEnabled", isEnabled);
                    editor.putBoolean("cached_shouldViewOnlyAssigned", shouldViewOnlyAssigned);
                    editor.putBoolean("cached_shouldOpenPopupAfterCall", shouldOpenPopupAfterCall);
                    editor.putBoolean("shouldViewOnlyAssigned", shouldViewOnlyAssigned); // For backward compatibility
                    editor.putLong("global_settings_last_fetch_" + tenantIdString, System.currentTimeMillis());
                    editor.apply();

                    Log.d(TAG, "Global settings cached successfully");
                } else {
                    Log.e(TAG, "Call Recording API call failed: " + responseCode);

                    // Try to use cached data as fallback
                    isEnabled = preferences.getBoolean("cached_isCallRecordingEnabled", false);
                    shouldViewOnlyAssigned = preferences.getBoolean("cached_shouldViewOnlyAssigned", false);
                    shouldOpenPopupAfterCall = preferences.getBoolean("cached_shouldOpenPopupAfterCall", true);
                    Log.d(TAG, "Using cached data as fallback due to API failure");
                }
            } catch (Exception e) {
                Log.e(TAG, "Exception while checking call recording enabled: " + e.getMessage(), e);

                // Try to use cached data as fallback
                try {
                    SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
                    isEnabled = preferences.getBoolean("cached_isCallRecordingEnabled", false);
                    shouldViewOnlyAssigned = preferences.getBoolean("cached_shouldViewOnlyAssigned", false);
                    shouldOpenPopupAfterCall = preferences.getBoolean("cached_shouldOpenPopupAfterCall", true);
                    Log.d(TAG, "Using cached data as fallback due to exception");
                } catch (Exception cacheException) {
                    Log.e(TAG, "Failed to read cached data: " + cacheException.getMessage());
                }
            }

            callback.onResult(isEnabled, shouldViewOnlyAssigned, shouldOpenPopupAfterCall);
        }).start();
    }


    /**
     * Clear cached global settings for a specific tenant
     */
    public void clearGlobalSettingsCache(String tenantId) {
        try {
            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
            SharedPreferences.Editor editor = preferences.edit();

            editor.remove("cached_isCallRecordingEnabled");
            editor.remove("cached_shouldViewOnlyAssigned");
            editor.remove("global_settings_last_fetch_" + tenantId);
            editor.apply();

            Log.d(TAG, "Global settings cache cleared for tenant: " + tenantId);
        } catch (Exception e) {
            Log.e(TAG, "Failed to clear global settings cache: " + e.getMessage());
        }
    }

    /**
     * Force refresh global settings from API
     */
    public void refreshGlobalSettings(CallRecordingCallback callback) {
        isCallRecordingEnabledInBackground(callback, true);
    }

    public interface CallRecordingCallback {
        void onResult(boolean isCallRecordingEnabled, boolean shouldViewOnlyAssigned, boolean shouldOpenPopupAfterCall);
    }
}