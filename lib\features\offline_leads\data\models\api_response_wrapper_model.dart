import 'package:json_annotation/json_annotation.dart';
import 'offline_lead_model.dart';

part 'api_response_wrapper_model.g.dart';

@JsonSerializable()
class ApiResponseWrapperModel {
  final bool succeeded;
  final String? message;
  final List<String>? errors;
  final List<OfflineLeadModel> data;

  ApiResponseWrapperModel({
    required this.succeeded,
    this.message,
    this.errors,
    required this.data,
  });

  factory ApiResponseWrapperModel.fromJson(Map<String, dynamic> json) =>
      _$ApiResponseWrapperModelFromJson(json);

  Map<String, dynamic> toJson() => _$ApiResponseWrapperModelToJson(this);

  @override
  String toString() {
    return 'ApiResponseWrapperModel(succeeded: $succeeded, dataCount: ${data.length}, message: $message)';
  }
}
