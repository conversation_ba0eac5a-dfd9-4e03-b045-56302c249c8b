import 'package:flutter/cupertino.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionItem {
  final IconData? icon;
  final String? title;
  final String? description;
  final Permission? permission;
  final bool? isPermissionAllowed;

  PermissionItem({this.icon, this.title, this.description, this.permission, this.isPermissionAllowed = false});

  PermissionItem copyWith({
    IconData? icon,
    String? title,
    String? description,
    Permission? permission,
    bool? isPermissionAllowed,
  }) {
    return PermissionItem(
      icon: icon ?? this.icon,
      title: title ?? this.title,
      description: description ?? this.description,
      permission: permission ?? this.permission,
      isPermissionAllowed: isPermissionAllowed ?? this.isPermissionAllowed,
    );
  }
}