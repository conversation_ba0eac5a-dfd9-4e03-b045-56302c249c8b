#!/bin/bash

# <PERSON><PERSON>t to update Java 17 path in gradle.properties after installation

echo "Checking for Java 17 installation..."

# Find Java 17 installation
JAVA17_HOME=$(/usr/libexec/java_home -v 17 2>/dev/null)

if [ -z "$JAVA17_HOME" ]; then
    echo "❌ Java 17 not found. Please install Java 17 first."
    echo "Available Java versions:"
    /usr/libexec/java_home -V
    exit 1
fi

echo "✅ Found Java 17 at: $JAVA17_HOME"

# Update gradle.properties
GRADLE_PROPS="android/gradle.properties"

if [ -f "$GRADLE_PROPS" ]; then
    # Remove existing org.gradle.java.home line if it exists
    sed -i '' '/org.gradle.java.home=/d' "$GRADLE_PROPS"
    
    # Add the new Java 17 home path
    echo "" >> "$GRADLE_PROPS"
    echo "# Java 17 configuration" >> "$GRADLE_PROPS"
    echo "org.gradle.java.home=$JAVA17_HOME" >> "$GRADLE_PROPS"
    
    echo "✅ Updated $GRADLE_PROPS with Java 17 path"
else
    echo "❌ gradle.properties not found at $GRADLE_PROPS"
    exit 1
fi

echo "🎉 Java 17 configuration complete!"
echo "You can now run: cd android && ./gradlew --version"
echo "to verify that Gradle is using Java 17"
