import 'package:hive/hive.dart';
import 'package:hive_flutter/hive_flutter.dart';
import '../../constants/hive_model_constants.dart';
import '../../models/base_url_model.dart';
import '../../../features/offline_leads/data/models/offline_lead_model.dart';
import 'local_storage_service.dart';

class HiveServiceImpl implements LocalStorageService {
  final Map<String, Box> _boxes = {};

  @override
  Future<void> initialize() async {
    await Hive.initFlutter();
    _registerHiveAdapters();
    await _openHiveBoxes();
  }

  void _registerHiveAdapters() {
    if (!Hive.isAdapterRegistered(114)) {
      Hive.registerAdapter(OfflineLeadModelAdapter());
    }
  }

  Future<void> _openHiveBoxes() async {
    _boxes[HiveModelConstants.baseUrlModelBoxName] = await Hive.openBox<BaseUrlModel>(HiveModelConstants.baseUrlModelBoxName);
  }

  Box<T> _getBox<T>(String boxName) {
    if (!_boxes.containsKey(boxName)) {
      throw Exception('Box $boxName not found. Make sure it\'s opened in _openHiveBoxes()');
    }
    return _boxes[boxName] as Box<T>;
  }

  @override
  Future<void> clearAllData() async {
    for (var box in _boxes.values) {
      await box.clear();
    }
  }

  @override
  Future<void> addItem<T>(String containerName, T item) async {
    final box = _getBox<T>(containerName);
    await box.add(item);
  }

  @override
  Future<void> addItems<T>(String containerName, List<T> items) async {
    final box = _getBox<T>(containerName);
    await box.addAll(items);
  }

  @override
  List<T> getAllItems<T>(String containerName) {
    final box = _getBox<T>(containerName);
    return box.values.toList();
  }

  @override
  Future<void> removeItem<T>(String containerName, dynamic key) async {
    final box = _getBox<T>(containerName);
    await box.delete(key);
  }

  @override
  Future<void> clearContainer(String containerName) async {
    final box = _getBox(containerName);
    await box.clear();
  }
}