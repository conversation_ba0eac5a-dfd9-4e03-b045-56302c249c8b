import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../services/force_update_service/force_update_service.dart';
import 'force_update_dialog.dart';

class ForceUpdate<PERSON>hecker extends StatefulWidget {
  final Widget child;

  const ForceUpdateChecker({
    super.key,
    required this.child,
  });

  @override
  State<ForceUpdateChecker> createState() => _ForceUpdateCheckerState();
}

class _ForceUpdateCheckerState extends State<ForceUpdateChecker> {
  final ForceUpdateService _forceUpdateService = GetIt.instance<ForceUpdateService>();
  bool _isChecking = true;
  bool _shouldShowUpdateDialog = false;
  String _currentVersion = '';
  String _requiredVersion = '';

  @override
  void initState() {
    super.initState();
    _checkForUpdate();
  }

  Future<void> _checkForUpdate() async {
    try {
      final shouldUpdate = await _forceUpdateService.shouldForceUpdate();
      
      if (shouldUpdate) {
        final currentVersion = await _forceUpdateService.getCurrentAppVersion();
        final requiredVersion = await _forceUpdateService.getForceUpdateVersion();
        
        setState(() {
          _shouldShowUpdateDialog = true;
          _currentVersion = currentVersion;
          _requiredVersion = requiredVersion ?? '';
          _isChecking = false;
        });
      } else {
        setState(() {
          _isChecking = false;
        });
      }
    } catch (e) {
      print('Error checking for force update: $e');
      setState(() {
        _isChecking = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isChecking) {
      return const Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: Colors.white,
              ),
              SizedBox(height: 16),
              Text(
                'Checking for updates...',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_shouldShowUpdateDialog) {
      return Scaffold(
        backgroundColor: Colors.black,
        body: Center(
          child: ForceUpdateDialog(
            currentVersion: _currentVersion,
            requiredVersion: _requiredVersion,
          ),
        ),
      );
    }

    return widget.child;
  }
}
