import 'package:http/http.dart' as http;
import 'background_sync_service.dart';
import 'leadrat_data_manager.dart';
import 'offline_leads_search_service.dart';
import '../repository/offline_leads_repository_impl.dart';
import '../datasources/offline_leads_local_datasource.dart';
import '../datasources/offline_leads_remote_datasource.dart';
import 'package:call_detection/features/call_recordings/data/services/global_settings_cache_service.dart';

class OfflineLeadsServiceInitializer {
  static bool _isInitialized = false;

  /// Initialize the offline leads background sync service
  /// This should be called once during app startup (non-blocking)
  static Future<void> initialize() async {
    if (_isInitialized) {
      print('🔧 [SERVICE_INIT] Service already initialized, skipping...');
      return;
    }

    try {
      print(
          '🚀 [SERVICE_INIT] Initializing offline leads foreground sync service...');

      // Initialize the background sync service (now using foreground service)
      await BackgroundSyncService.initialize();
      print('✅ [SERVICE_INIT] Background sync service initialized');

      // Start periodic sync automatically (now using foreground service)
      await BackgroundSyncService.startPeriodicSync();
      print('✅ [SERVICE_INIT] Foreground sync service started');

      // Check if service is running
      final isRunning = await BackgroundSyncService.isServiceRunning();
      print('🔍 [SERVICE_INIT] Service running status: $isRunning');

      // Get sync stats
      final syncStats = await BackgroundSyncService.getSyncStats();
      print('📊 [SERVICE_INIT] Sync stats: $syncStats');

      // Initialize LeadratDataManager (non-blocking)
      print('🔧 [SERVICE_INIT] Initializing LeadratDataManager...');
      await LeadratDataManager.instance.initialize();

      // Initialize global settings before starting sync to ensure shouldViewOnlyAssigned is available
      await _initializeGlobalSettings();

      // Initialize search service in background to avoid blocking app startup
      _initializeSearchServiceInBackground();

      _isInitialized = true;
      print(
          '🎉 [SERVICE_INIT] Offline leads foreground sync service initialized successfully');
    } catch (e) {
      print(
          '❌ [SERVICE_INIT] Failed to initialize offline leads foreground sync service: $e');
      // Don't rethrow - allow app to continue even if sync service fails
      _isInitialized = true; // Mark as initialized to prevent retry loops
    }
  }

  /// Initialize search service in background without blocking app startup
  static void _initializeSearchServiceInBackground() {
    // Run in background without blocking
    Future.delayed(Duration.zero, () async {
      try {
        print('🔧 [SERVICE_INIT] Initializing search service in background...');
        final localDataSource = OfflineLeadsLocalDataSourceImpl();

        // Try to get data with a very short timeout to avoid blocking
        try {
          final leadratData = await LeadratDataManager.instance.waitForData(
            timeout: const Duration(seconds: 2),
          );

          final tenantId = leadratData['tenantId'] ?? 'default-tenant';
          final userId = leadratData['userId'];

          if (tenantId != 'default-tenant' && userId != null) {
            // Only initialize with real data
            await _initializeSearchServiceWithData(
                localDataSource, tenantId, userId);
          } else {
            print(
                '🔧 [SERVICE_INIT] No valid tenant/user data yet, initializing with minimal setup');
            // Initialize with minimal setup for now
            await _initializeSearchServiceMinimal(localDataSource);
          }
        } catch (e) {
          print(
              '🔧 [SERVICE_INIT] Data not available yet, initializing minimal search service: $e');
          // Initialize with minimal setup
          await _initializeSearchServiceMinimal(localDataSource);
        }
      } catch (e) {
        print(
            '❌ [SERVICE_INIT] Failed to initialize search service in background: $e');
      }
    });
  }

  /// Initialize search service with actual tenant and user data
  static Future<void> _initializeSearchServiceWithData(
    OfflineLeadsLocalDataSourceImpl localDataSource,
    String tenantId,
    String? userId,
  ) async {
    final remoteDataSource = OfflineLeadsRemoteDataSourceImpl(
      httpClient: http.Client(),
      baseUrl: 'https://prd-mobile.leadrat.com',
      tenantId: tenantId,
      userId: userId,
    );

    final repository = OfflineLeadsRepositoryImpl(
      localDataSource: localDataSource,
      remoteDataSource: remoteDataSource,
    );

    OfflineLeadsSearchService.initialize(repository);
    print(
        '🔍 [SERVICE_INIT] Search service initialized with tenantId: $tenantId, userId: $userId');
  }

  /// Initialize search service with minimal setup (local only)
  static Future<void> _initializeSearchServiceMinimal(
    OfflineLeadsLocalDataSourceImpl localDataSource,
  ) async {
    final remoteDataSource = OfflineLeadsRemoteDataSourceImpl(
      httpClient: http.Client(),
      baseUrl: 'https://prd-mobile.leadrat.com',
      tenantId: 'default-tenant',
      userId: null,
    );

    final repository = OfflineLeadsRepositoryImpl(
      localDataSource: localDataSource,
      remoteDataSource: remoteDataSource,
    );

    OfflineLeadsSearchService.initialize(repository);
    print('🔍 [SERVICE_INIT] Search service initialized with minimal setup');
  }

  /// Initialize global settings to ensure shouldViewOnlyAssigned is available
  static Future<void> _initializeGlobalSettings() async {
    try {
      print('🔧 [SERVICE_INIT] Initializing global settings...');

      // Get tenant ID from LeadratDataManager
      final tenantId = await LeadratDataManager.instance.getTenantId();

      if (tenantId != null && tenantId.isNotEmpty && tenantId != 'null') {
        print(
            '🔧 [SERVICE_INIT] Fetching global settings for tenant: $tenantId');

        // Fetch global settings to ensure shouldViewOnlyAssigned is cached
        final settings =
            await GlobalSettingsCacheService.getCallRecordingSettings(
          tenantId: tenantId,
          forceRefresh: false, // Use cache if available, otherwise fetch fresh
        );

        print('✅ [SERVICE_INIT] Global settings initialized: $settings');
      } else {
        print(
            '⚠️ [SERVICE_INIT] No valid tenant ID available, skipping global settings initialization');
      }
    } catch (e) {
      print('❌ [SERVICE_INIT] Failed to initialize global settings: $e');
      // Don't rethrow - allow app to continue even if global settings fail
    }
  }

  /// Stop the background sync service
  /// This should be called when the app is being disposed
  static Future<void> dispose() async {
    if (!_isInitialized) {
      print('🔧 [SERVICE_INIT] Service not initialized, nothing to dispose');
      return;
    }

    try {
      print(
          '🛑 [SERVICE_INIT] Stopping offline leads background sync service...');
      await BackgroundSyncService.stopPeriodicSync();
      _isInitialized = false;

      print('✅ [SERVICE_INIT] Offline leads background sync service stopped');
    } catch (e) {
      print(
          '❌ [SERVICE_INIT] Failed to stop offline leads background sync service: $e');
    }
  }

  /// Check if the service is initialized
  static bool get isInitialized => _isInitialized;

  /// Perform a one-time sync
  static Future<void> performOneTimeSync() async {
    try {
      print('🔄 [SERVICE_INIT] Triggering one-time sync...');
      await BackgroundSyncService.performOneTimeSync();

      print('✅ [SERVICE_INIT] One-time sync triggered successfully');
    } catch (e) {
      print('❌ [SERVICE_INIT] Failed to trigger one-time sync: $e');
      rethrow;
    }
  }
}
