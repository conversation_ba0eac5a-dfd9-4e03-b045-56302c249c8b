part of 'injection_container.dart';

Future<void> initDependencies() async {
  await init();
  try {
    await getIt<LocalStorageService>().initialize();
  } catch (e) {
    // Handle initialization error
  }
}

registerBlocProviders(BuildContext context) {
  return [
    BlocProvider(create: (context) => getIt<PermissionsBloc>()),
    BlocProvider(create: (context) => getIt<CallRecordingsBloc>()),
    BlocProvider(create: (context) => getIt<HomeBloc>()),
    BlocProvider(create: (context) => getIt<OfflineLeadsBloc>()),
  ];
}


