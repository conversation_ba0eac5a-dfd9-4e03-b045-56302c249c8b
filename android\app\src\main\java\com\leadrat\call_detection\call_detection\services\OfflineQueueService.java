package com.leadrat.call_detection.call_detection.services;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.leadrat.call_detection.call_detection.CallReceiver;
import com.leadrat.call_detection.call_detection.R;
import com.leadrat.call_detection.call_detection.database.OfflineQueueDatabaseHelper;
import com.leadrat.call_detection.call_detection.enums.CallDirection;
import com.leadrat.call_detection.call_detection.enums.CallStatus;
import com.leadrat.call_detection.call_detection.models.CallLogQueueItem;
import com.leadrat.call_detection.call_detection.models.LeadCallLogDTO;
import com.leadrat.call_detection.call_detection.models.ProspectCallLogDTO;
import com.leadrat.call_detection.call_detection.models.RecordingQueueItem;

import org.json.JSONObject;

import java.io.File;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Background service for processing offline recording and call log queues
 */
public class OfflineQueueService extends Service {
    private static final String TAG = "OfflineQueueService";
    private static final String CHANNEL_ID = "OfflineQueueServiceChannel";
    private static final int NOTIFICATION_ID = 2;
    private static final long PROCESSING_INTERVAL = 30000; // 30 seconds

    private Handler processingHandler;
    private OfflineQueueDatabaseHelper databaseHelper;
    private ExecutorService executorService;
    private CallReceiver callReceiver;
    private boolean isProcessing = false;

    private final Runnable processingRunnable = new Runnable() {
        @Override
        public void run() {
            if (!isProcessing && isNetworkAvailable()) {
                processQueues();
            }

            // Schedule next processing
            if (processingHandler != null) {
                processingHandler.postDelayed(this, PROCESSING_INTERVAL);
            }
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "🔄 [OFFLINE_QUEUE] OfflineQueueService onCreate");

        databaseHelper = OfflineQueueDatabaseHelper.getInstance(this);
        executorService = Executors.newFixedThreadPool(3); // Max 3 concurrent operations
        callReceiver = new CallReceiver();
        processingHandler = new Handler(Looper.getMainLooper());

        createNotificationChannel();
        startForeground(NOTIFICATION_ID, createNotification("Offline queue service started"));

        // Start processing
        processingHandler.post(processingRunnable);

        Log.d(TAG, "✅ [OFFLINE_QUEUE] OfflineQueueService started successfully");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "🔄 [OFFLINE_QUEUE] OfflineQueueService onStartCommand");
        return START_STICKY; // Restart if killed
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "🔄 [OFFLINE_QUEUE] OfflineQueueService onDestroy");
        super.onDestroy();

        if (processingHandler != null) {
            processingHandler.removeCallbacks(processingRunnable);
        }

        if (executorService != null) {
            executorService.shutdown();
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    /**
     * Process both recording and call log queues
     */
    private void processQueues() {
        if (isProcessing) {
            Log.d(TAG, "🔄 [OFFLINE_QUEUE] Already processing, skipping this cycle");
            return;
        }

        isProcessing = true;

        executorService.execute(() -> {
            try {
                Log.d(TAG, "🔄 [OFFLINE_QUEUE] Starting queue processing cycle");

                // Process recording uploads first
                processRecordingQueue();

                // Then process call log syncing
                processCallLogQueue();

                // Clean up completed items
                cleanupCompletedItems();

                // Log statistics with detailed table counts
                String stats = databaseHelper.getQueueStatistics();
                Log.d(TAG, "📊 [OFFLINE_QUEUE] Queue Statistics:\n" + stats);

                // Log detailed table information
                logDetailedTableInfo();

                updateNotification("Last updated: " + new SimpleDateFormat("HH:mm").format(new Date()));

            } catch (Exception e) {
                Log.e(TAG, "❌ [OFFLINE_QUEUE] Error during queue processing: " + e.getMessage(), e);
            } finally {
                isProcessing = false;
            }
        });
    }

    /**
     * Process recording upload queue
     */
    private void processRecordingQueue() {
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Processing recording upload queue");

        List<RecordingQueueItem> pendingItems = databaseHelper.getPendingRecordingQueueItems();
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Found " + pendingItems.size() + " pending recording uploads");

        for (RecordingQueueItem item : pendingItems) {
            if (!isNetworkAvailable()) {
                Log.d(TAG, "📁 [OFFLINE_QUEUE] Network not available, stopping recording processing");
                break;
            }

            processRecordingItem(item);
        }
    }

    /**
     * Process call log sync queue
     */
    private void processCallLogQueue() {
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Processing call log sync queue");

        List<CallLogQueueItem> pendingItems = databaseHelper.getPendingCallLogQueueItems();
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Found " + pendingItems.size() + " pending call log syncs");

        for (CallLogQueueItem item : pendingItems) {
            if (!isNetworkAvailable()) {
                Log.d(TAG, "📞 [OFFLINE_QUEUE] Network not available, stopping call log processing");
                break;
            }

            processCallLogItem(item);
        }
    }

    /**
     * Process individual recording item
     */
    private void processRecordingItem(RecordingQueueItem item) {
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Processing recording item: " + item.id);

        try {
            // Mark as processing
            item.markAsProcessing();
            databaseHelper.updateRecordingQueueItem(item);

            // Prepare base64 data if not already done
            if (item.base64Data == null || item.base64Data.isEmpty()) {
                if (item.recordingFilePath != null && !item.recordingFilePath.isEmpty()) {
                    File recordingFile = new File(item.recordingFilePath);
                    if (recordingFile.exists()) {
                        item.base64Data = callReceiver.encodeFileToBase64(item.recordingFilePath);
                        if (item.base64Data == null) {
                            throw new Exception("Failed to encode recording file to base64");
                        }
                    } else {
                        throw new Exception("Recording file not found: " + item.recordingFilePath);
                    }
                } else {
                    throw new Exception("No recording file path provided");
                }
            }

            // Upload to S3
            uploadRecordingToS3(item);

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Failed to process recording item " + item.id + ": " + e.getMessage());
            item.markAsFailed(e.getMessage());
            databaseHelper.updateRecordingQueueItem(item);
        }
    }

    /**
     * Upload recording to S3
     */
    private void uploadRecordingToS3(RecordingQueueItem item) {
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Uploading recording to S3: " + item.id);

        callReceiver.sendBase64ToServer(item.base64Data, item.entityId, new CallReceiver.UploadCallback() {
            @Override
            public void onSuccess(String fileUrl) {
                Log.d(TAG, "✅ [OFFLINE_QUEUE] Recording uploaded successfully: " + item.id + " -> " + fileUrl);

                // Mark as completed
                item.markAsCompleted();
                databaseHelper.updateRecordingQueueItem(item);

                // Update related call log items
                databaseHelper.updateCallLogQueueItemsForCompletedRecording(item.id, fileUrl);

                // Delete local file after successful upload
                if (item.recordingFilePath != null && !item.recordingFilePath.isEmpty()) {
                    try {
                        File localFile = new File(item.recordingFilePath);
                        if (localFile.exists() && localFile.delete()) {
                            Log.d(TAG, "🗑️ [OFFLINE_QUEUE] Deleted local recording file: " + item.recordingFilePath);
                        }
                    } catch (Exception e) {
                        Log.w(TAG, "⚠️ [OFFLINE_QUEUE] Failed to delete local recording file: " + e.getMessage());
                    }
                }
            }

            @Override
            public void onFailure(String error) {
                Log.e(TAG, "❌ [OFFLINE_QUEUE] Recording upload failed: " + item.id + " - " + error);
                item.markAsFailed(error);
                databaseHelper.updateRecordingQueueItem(item);
            }
        });
    }

    /**
     * Process individual call log item
     */
    private void processCallLogItem(CallLogQueueItem item) {
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Processing call log item: " + item.id);
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Item details - EntityId: " + item.entityId + ", IsLead: " + item.isLead + ", PhoneNumber: " + item.phoneNumber);

        try {
            // Mark as processing
            item.markAsProcessing();
            databaseHelper.updateCallLogQueueItem(item);

            // Create appropriate DTO and sync to server
            if (item.isLead) {
                Log.d(TAG, "📞 [OFFLINE_QUEUE] Syncing as LEAD (isLead=true) - calling syncLeadCallLog");
                syncLeadCallLog(item);
            } else {
                Log.d(TAG, "📞 [OFFLINE_QUEUE] Syncing as PROSPECT (isLead=false) - calling syncProspectCallLog");
                syncProspectCallLog(item);
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Failed to process call log item " + item.id + ": " + e.getMessage());
            item.markAsFailed(e.getMessage());
            databaseHelper.updateCallLogQueueItem(item);
        }
    }

    /**
     * Sync lead call log to server
     */
    private void syncLeadCallLog(CallLogQueueItem item) {
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Syncing lead call log: " + item.id);

        try {
            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(this);
            String userIdString = preferences.getString("UserId", "5170e4fa-e495-491a-abd5-6a65772996eb");
            UUID userId = UUID.fromString(userIdString);

            LeadCallLogDTO callInfo = new LeadCallLogDTO();
            callInfo.leadId = item.entityId;
            callInfo.userId = userId;
            callInfo.callStartTime = item.callStartTime;
            callInfo.callEndTime = item.callEndTime;
            callInfo.callDuration = item.callDuration;
            callInfo.callDirection = item.callDirection;
            callInfo.notes = item.notes;
            callInfo.callStatus = item.callStatus;
            callInfo.callRecordingUrl = item.callRecordingUrl != null ? item.callRecordingUrl : "";
            callInfo.callTimestamp = item.createdAt; // Use createdAt for lastModifiedOn

            // Make API call
            String apiUrl = CallReceiver.baseUrl + "v2/leadcalllog";
            boolean success = makeCallLogApiCall(apiUrl, createLeadCallLogJson(callInfo));

            if (success) {
                Log.d(TAG, "✅ [OFFLINE_QUEUE] Lead call log synced successfully: " + item.id);
                item.markAsCompleted();
            } else {
                throw new Exception("API call failed");
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Failed to sync lead call log: " + e.getMessage());
            item.markAsFailed(e.getMessage());
        }

        databaseHelper.updateCallLogQueueItem(item);
    }

    /**
     * Sync prospect call log to server
     */
    private void syncProspectCallLog(CallLogQueueItem item) {
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Syncing prospect call log: " + item.id);

        try {
            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(this);
            String userIdString = preferences.getString("UserId", "5170e4fa-e495-491a-abd5-6a65772996eb");
            UUID userId = UUID.fromString(userIdString);

            ProspectCallLogDTO callInfo = new ProspectCallLogDTO();
            callInfo.prospectId = item.entityId;
            callInfo.userId = userId;
            callInfo.callStartTime = item.callStartTime;
            callInfo.callEndTime = item.callEndTime;
            callInfo.callDuration = item.callDuration;
            callInfo.callDirection = item.callDirection;
            callInfo.notes = item.notes;
            callInfo.callStatus = item.callStatus;
            callInfo.callRecordingUrl = item.callRecordingUrl != null ? item.callRecordingUrl : "";
            callInfo.callTimestamp = item.createdAt; // Use createdAt for lastModifiedOn

            // Make API call
            String apiUrl = CallReceiver.baseUrl + "v1/prospectcalllogs";
            boolean success = makeCallLogApiCall(apiUrl, createProspectCallLogJson(callInfo));

            if (success) {
                Log.d(TAG, "✅ [OFFLINE_QUEUE] Prospect call log synced successfully: " + item.id);
                item.markAsCompleted();
            } else {
                throw new Exception("API call failed");
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Failed to sync prospect call log: " + e.getMessage());
            item.markAsFailed(e.getMessage());
        }

        databaseHelper.updateCallLogQueueItem(item);
    }

    /**
     * Make call log API call
     */
    private boolean makeCallLogApiCall(String apiUrl, JSONObject requestJson) {
        try {
            Log.d(TAG, "📞 [OFFLINE_QUEUE] API URL CALLED: " + apiUrl);
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");

            SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(this);
            String tenantId = preferences.getString("TenantId", "");
            connection.setRequestProperty("tenant", tenantId);
            connection.setDoOutput(true);
            connection.setConnectTimeout(30000);
            connection.setReadTimeout(30000);

            // Send request
            connection.getOutputStream().write(requestJson.toString().getBytes("UTF-8"));
            connection.getOutputStream().close();

            int responseCode = connection.getResponseCode();
            Log.d(TAG, "📞 [OFFLINE_QUEUE] Call log API response code: " + responseCode);

            return responseCode == HttpURLConnection.HTTP_OK;

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Call log API call failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Create JSON for lead call log
     */
    private JSONObject createLeadCallLogJson(LeadCallLogDTO callLogDto) throws Exception {
        SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        isoFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

        String callStartTime = callLogDto.callStartTime > 0 ? isoFormat.format(new Date(callLogDto.callStartTime)) : null;
        String callEndTime = callLogDto.callEndTime > 0 ? isoFormat.format(new Date(callLogDto.callEndTime)) : null;
        String lastModifiedOn = callLogDto.callTimestamp > 0 ? isoFormat.format(new Date(callLogDto.callTimestamp)) : null;

        Log.d(TAG, "📞 [OFFLINE_QUEUE] Creating lead call log JSON with lastModifiedOn: " + lastModifiedOn +
                  " (createdAt timestamp: " + callLogDto.callTimestamp + ")");

        JSONObject requestJson = new JSONObject();
        requestJson.put("leadId", callLogDto.leadId);
        requestJson.put("userId", callLogDto.userId.toString());
        requestJson.put("callStartTime", callStartTime);
        requestJson.put("callEndTime", callEndTime);
        requestJson.put("callDuration", callLogDto.callDuration);
        requestJson.put("callDirection", callLogDto.callDirection.ordinal());
        requestJson.put("notes", callLogDto.notes);
        requestJson.put("callStatus", callLogDto.callStatus.ordinal());
        requestJson.put("callRecordingUrl", callLogDto.callRecordingUrl);
        requestJson.put("lastModifiedOn", lastModifiedOn);

        return requestJson;
    }

    /**
     * Create JSON for prospect call log
     */
    private JSONObject createProspectCallLogJson(ProspectCallLogDTO callLogDto) throws Exception {
        SimpleDateFormat isoFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.getDefault());
        isoFormat.setTimeZone(TimeZone.getTimeZone("UTC"));

        String callStartTime = callLogDto.callStartTime > 0 ? isoFormat.format(new Date(callLogDto.callStartTime)) : null;
        String callEndTime = callLogDto.callEndTime > 0 ? isoFormat.format(new Date(callLogDto.callEndTime)) : null;
        String lastModifiedOn = callLogDto.callTimestamp > 0 ? isoFormat.format(new Date(callLogDto.callTimestamp)) : null;
        String createdOn = callLogDto.callTimestamp > 0 ? isoFormat.format(new Date(callLogDto.callTimestamp)) : null;


        // Get user ID for createdBy and lastModifiedBy fields
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(this);
        String userIdString = preferences.getString("UserId", "");

        JSONObject requestJson = new JSONObject();
        requestJson.put("prospectId", callLogDto.prospectId);
        requestJson.put("userId", callLogDto.userId.toString());
        requestJson.put("callStartTime", callStartTime);
        requestJson.put("callEndTime", callEndTime);
        requestJson.put("callDuration", callLogDto.callDuration);
        requestJson.put("callDirection", callLogDto.callDirection.ordinal());
        requestJson.put("notes", callLogDto.notes != null ? callLogDto.notes : "");
        requestJson.put("callStatus", callLogDto.callStatus.ordinal());
        requestJson.put("callRecordingUrl", callLogDto.callRecordingUrl); // Add missing recording URL field
        requestJson.put("lastModifiedOn", lastModifiedOn);
        requestJson.put("createdBy", userIdString);
        requestJson.put("createdOn", createdOn);
        requestJson.put("lastModifiedBy", userIdString);
        requestJson.put("updatedCallStatus", callLogDto.callStatus.name());
        requestJson.put("updatedCallDirection", callLogDto.callDirection.name());

        Log.d(TAG, "📞 [OFFLINE_QUEUE] Creating prospect call log JSON with lastModifiedOn: " + requestJson.toString());
        return requestJson;
    }

    /**
     * Clean up completed items from database
     */
    private void cleanupCompletedItems() {
        try {
            int deletedRecordings = databaseHelper.deleteCompletedRecordingQueueItems();
            int deletedCallLogs = databaseHelper.deleteCompletedCallLogQueueItems();

            if (deletedRecordings > 0 || deletedCallLogs > 0) {
                Log.d(TAG, "🗑️ [OFFLINE_QUEUE] Cleaned up " + deletedRecordings + " recording items and " + deletedCallLogs + " call log items");
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Error during cleanup: " + e.getMessage());
        }
    }

    /**
     * Check if network is available
     */
    private boolean isNetworkAvailable() {
        try {
            ConnectivityManager connectivityManager = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
            if (connectivityManager != null) {
                NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                return activeNetworkInfo != null && activeNetworkInfo.isConnected();
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Error checking network availability: " + e.getMessage());
        }
        return false;
    }

    /**
     * Log detailed table information for debugging
     */
    private void logDetailedTableInfo() {
        try {
            // Get all recording queue items
            List<RecordingQueueItem> allRecordings = databaseHelper.getAllRecordingQueueItems();
            Log.d(TAG, "📁 [OFFLINE_QUEUE] Total recording queue items: " + allRecordings.size());

            for (int i = 0; i < Math.min(allRecordings.size(), 5); i++) {
                RecordingQueueItem item = allRecordings.get(i);
                Log.d(TAG, "📁 [OFFLINE_QUEUE] Recording " + (i+1) + ": ID=" + item.id +
                      ", Status=" + item.uploadStatus +
                      ", EntityId=" + item.entityId +
                      ", IsLead=" + item.isLead +
                      ", HasFile=" + (item.recordingFilePath != null && !item.recordingFilePath.isEmpty()) +
                      ", HasBase64=" + (item.base64Data != null && !item.base64Data.isEmpty()));
            }

            // Get all call log queue items
            List<CallLogQueueItem> allCallLogs = databaseHelper.getAllCallLogQueueItems();
            Log.d(TAG, "📞 [OFFLINE_QUEUE] Total call log queue items: " + allCallLogs.size());

            for (int i = 0; i < Math.min(allCallLogs.size(), 5); i++) {
                CallLogQueueItem item = allCallLogs.get(i);
                Log.d(TAG, "📞 [OFFLINE_QUEUE] CallLog " + (i+1) + ": ID=" + item.id +
                      ", Status=" + item.syncStatus +
                      ", EntityId=" + item.entityId +
                      ", IsLead=" + item.isLead +
                      ", HasRecordingUrl=" + (item.callRecordingUrl != null && !item.callRecordingUrl.isEmpty()) +
                      ", Duration=" + item.callDuration);
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Error logging table info: " + e.getMessage());
        }
    }

    /**
     * Create notification channel
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "Offline Queue Service",
                    NotificationManager.IMPORTANCE_LOW
            );
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
            }
        }
    }

    /**
     * Create notification
     */
    private Notification createNotification(String contentText) {
        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("Call Recording Sync")
                .setContentText(contentText)
                .setSmallIcon(R.mipmap.ic_launcher)
                .setPriority(NotificationCompat.PRIORITY_LOW)
                .build();
    }

    /**
     * Update notification
     */
    private void updateNotification(String contentText) {
        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
        if (notificationManager != null) {
            notificationManager.notify(NOTIFICATION_ID, createNotification(contentText));
        }
    }

    /**
     * Add recording to offline queue
     */
    public static void addRecordingToQueue(Context context, String phoneNumber, String recordingFilePath,
                                         String entityId, boolean isLead, CallDirection callDirection,
                                         CallStatus callStatus, long callStartTime, long callEndTime,
                                         double callDuration, String notes) {
        try {
            OfflineQueueDatabaseHelper databaseHelper = OfflineQueueDatabaseHelper.getInstance(context);

            RecordingQueueItem recordingItem = new RecordingQueueItem(
                phoneNumber, recordingFilePath, entityId, isLead, callDirection,
                callStatus, callStartTime, callEndTime, callDuration, notes
            );

            long result = databaseHelper.insertRecordingQueueItem(recordingItem);
            Log.d(TAG, "📁 [OFFLINE_QUEUE] Added recording to queue: " + recordingItem.id + " (result: " + result + ")");

            // Also add call log item that waits for recording upload
            CallLogQueueItem callLogItem = new CallLogQueueItem(
                phoneNumber, entityId, isLead, callDirection, callStatus,
                callStartTime, callEndTime, callDuration, notes, recordingItem.id
            );

            long callLogResult = databaseHelper.insertCallLogQueueItem(callLogItem);
            Log.d(TAG, "📞 [OFFLINE_QUEUE] Added call log to queue: " + callLogItem.id + " (result: " + callLogResult + ")");

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Failed to add recording to queue: " + e.getMessage());
        }
    }

    /**
     * Add call log to offline queue (for calls without recording)
     */
    public static void addCallLogToQueue(Context context, String phoneNumber, String entityId,
                                       boolean isLead, CallDirection callDirection, CallStatus callStatus,
                                       long callStartTime, long callEndTime, double callDuration, String notes) {
        try {
            OfflineQueueDatabaseHelper databaseHelper = OfflineQueueDatabaseHelper.getInstance(context);

            CallLogQueueItem callLogItem = new CallLogQueueItem(
                phoneNumber, entityId, isLead, callDirection, callStatus,
                callStartTime, callEndTime, callDuration, notes, null
            );

            long result = databaseHelper.insertCallLogQueueItem(callLogItem);
            Log.d(TAG, "📞 [OFFLINE_QUEUE] Added call log to queue: " + callLogItem.id + " (result: " + result + ")");

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Failed to add call log to queue: " + e.getMessage());
        }
    }
}