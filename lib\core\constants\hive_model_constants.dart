class HiveModelConstants {
  // Type IDs
  static const int masterAreaUnitModelTypeId = 0;
  static const int anotherModelTypeId = 1;
  static const int masterProjectTypeId = 2;
  static const int entityTypeEnumTypeId = 3;
  static const int modifiedDatesModelTypeId = 4;
  static const int globalSettingsModelTypeId = 5;
  static const int notificationSettingsModelTypeId = 6;
  static const int baseNotificationSettingsModelTypeId = 7;
  static const int callSettingsModelTypeId = 8;
  static const int contactInfoModelTypeId = 9;
  static const int leadNotesSettingModelTypeId = 10;
  static const int leadProjectSettingModelTypeId = 11;
  static const int otpSettingModelTypeId = 12;
  static const int templateModelTypeId = 13;
  static const int whatsappTemplateModelTypeId = 14;
  static const int callTypeEnumTypeId = 15;
  static const int contactTypeEnumTypeId = 16;
  static const int moduleNameEnumTypeId = 17;
  static const int otpReceiverEnumTypeId = 18;
  static const int currenciesTypeId = 19;
  static const int countryInfoModelTypeId = 20;
  static const int eventEnumTypeId = 21;
  static const int masterPropertyTypeId = 22;
  static const int leadSourceEnumTypeId = 23;
  static const int masterLeadSourceModelTypeId = 24;
  static const int masterLeadStatusModelTypeId = 25;
  static const int masterPropertyAmenitiesModelTypeId = 26;
  static const int propertyTypeEnumTypeId = 27;
  static const int masterPropertyAttributesModelTypeId = 28;
  static const int propertyAttributeFieldTypeTypeId = 29;
  static const int basePropertyTypeTypeId = 30;
  static const int masterProjectAttributeModelTypeId = 31;
  static const int masterProjectAmenititesModelTypeId = 32;
  static const int masterAssociatedBankModelTypeId = 33;
  static const int masterProjectAmenititesMapModelTypeId = 34;
  static const int masterPropertyAmenititesMapModelTypeId = 35;
  static const int getTokenModelTypeId = 36;
  static const int getTenantModelTypeId = 37;
  static const int masterUserServicesTypeId = 38;
  static const int masterCustomStatusModelTypeId = 39;
  static const int customFieldModelTypeId = 40;
  static const int viewFieldModelTypeId = 41;
  static const int viewFlagModelTypeId = 42;

  static const int dateTimeAdapterTypeId = 99;
  static const int genericTypeAdapterTypeId = 100;
  static const int getAllUsersTypeId = 101;
  static const int bloodGroupEnumTypeId = 102;
  static const int genderEnumTypeId = 103;
  static const int documentEnumTypeId = 104;
  static const int getUserDetailsTypeId = 105;
  static const int baseUserModelTypeId = 106;
  static const int dTOWithNameModelTypeId = 107;
  static const int userDocumentModelTypeId = 108;
  static const int rolesAndPermissionsTypeId = 109;
  static const int userTimeZoneInfoTypeId = 110;
  static const int duplicateLeadFeatureInfoId = 113;
  static const int baseUrlModelTypeId = 111;
  static const int keyValueObjTypeId = 112;
  static const int offlineLeadModelTypeId = 114;

  // Box Names
  static const String masterAreaUnitBoxName = 'masterAreaUnitBox';
  static const String masterProjectTypeBoxName = 'masterPropertyTypeBox';
  static const String modifiedDatesBoxName = 'modifiedDatesBoxName';
  static const String globalSettingsBoxName = 'globalSettingsBoxName';
  static const String notificationSettingsBoxName = 'notificationSettingsBoxName';
  static const String baseNotificationSettingsBoxName = 'baseNotificationSettingsBoxName';
  static const String callSettingsBoxName = 'callSettingsBoxName';
  static const String contactInfoBoxName = 'contactInfoBoxName';
  static const String leadNotesSettingBoxName = 'leadNotesSettingBoxName';
  static const String leadProjectSettingBoxName = 'leadProjectSettingBoxName';
  static const String otpSettingBoxName = 'otpSettingBoxName';
  static const String templateBoxName = 'templateBoxName';
  static const String whatsappTemplateBoxName = 'whatsappTemplateBoxName';
  static const String currenciesBoxName = 'currenciesBoxName';
  static const String countryInfoBoxName = 'countryInfoBoxName';
  static const String masterPropertyTypeBoxName = 'masterPropertyTypeBoxName';
  static const String masterPropertyListingTypeBoxName = 'masterPropertyListingTypeBoxName';
  static const String masterLeadSourceBoxName = 'masterLeadSourceBoxName';
  static const String masterLeadStatusBoxName = 'masterLeadStatusBoxName';
  static const String masterPropertyAmenitiesBoxName = 'masterPropertyAmenitiesBoxName';
  static const String masterPropertyAttributesBoxName = 'masterPropertyAttributesBoxName';
  static const String masterProjectAttributeBoxName = 'masterProjectAttributeBoxName';
  static const String masterProjectAmenititesBoxName = 'masterProjectAmenititesBoxName';
  static const String mapMasterProjectAmenititesBoxName = 'mapMasterProjectAmenititesBoxName';
  static const String mapMasterPropertyAmenititesBoxName = 'mapMasterPropertyAmenititesBoxName';
  static const String masterAssociatedBankBoxName = 'masterAssociatedBankBoxName';
  static const String masterProjectAmenititesMapBoxName = 'masterProjectAmenititesMapBoxName';
  static const String masterPropertyAmenititesMapBoxName = 'masterPropertyAmenititesMapBoxName';
  static const String masterUSerServicesBoxName = 'masterUSerServicesBoxName';
  static const String getTokenBoxName = 'getTokenBoxName';
  static const String getTenantBoxName = 'getTenantBoxName';
  static const String getAllUsersBoxName = 'getAllUsersBoxName';
  static const String getAllAdminsAndReporteesBoxName = 'getAllAdminsAndReporteesBoxName';
  static const String getAllReporteesBoxName = 'getAllReporteesBoxName';
  static const String getUserBoxName = 'getUserBoxName';
  static const String baseUserModelBoxName = 'baseUserModelBoxName';
  static const String dTOWithNameModelBoxName = 'dTOWithNameModelBoxName';
  static const String userDocumentModelBoxName = 'userDocumentModelBoxName';
  static const String rolesAndPermissionsBoxName = 'rolesAndPermissionsBoxName';
  static const String userTimeZoneInfoBoxName = 'userTimeZoneInfoBoxName';

  static const String masterCustomStatusBoxName = 'masterCustomStatusBoxName';
  static const String customFieldBoxName = 'customFieldBoxName';
  static const String viewFieldBoxName = 'viewFieldBoxName';
  static const String viewFlagsBoxName = 'viewFlagsBoxName';
  static const String getAgencyBoxName = 'getAgencyBoxName';
  static const String leadAddressesBoxName = 'leadAddressesBoxName';
  static const String mapMasterPropertyListingAmenititesBoxName = 'mapMasterPropertyListingAmenititesBoxName';
  static const String duplicateLeadFeatureInfoBoxName = 'duplicateLeadFeatureInfoBoxName';
  static const String baseUrlModelBoxName = 'baseUrlModelBoxName';
  static const String keyValueObjBoxName = 'keyValueObjBoxName';
  static const String offlineLeadsBoxName = 'offlineLeadsBoxName';
}