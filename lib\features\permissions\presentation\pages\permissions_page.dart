
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/constants/color_pallete.dart';
import '../../../../core/constants/text_styles.dart';
import '../../../../core/enums/page_state_enum.dart';
import '../../../../core/injection_container.dart';
import '../../../../core/utils/custom_snackbar.dart';
import '../../../call_recordings/presentation/pages/call_recordings_path_setters_page.dart';
import '../bloc/permissions_bloc.dart';
import '../widgets/permission_card.dart';

class PermissionsPage extends StatefulWidget {
  const PermissionsPage({super.key});

  @override
  _PermissionsPageState createState() => _PermissionsPageState();
}

class _PermissionsPageState extends State<PermissionsPage> {
  @override
  void initState() {
    getIt<PermissionsBloc>().add(InitPermissionsEvent());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        backgroundColor: const Color(0xFF0D0D0D),
        body: Padding(
          padding: const EdgeInsets.fromLTRB(24, 40, 14, 0),
          child: BlocConsumer<PermissionsBloc, PermissionsState>(
            listener: (context, state) {
              if (state.pageState == PageState.success) {
                LeadratCustomSnackbar.show(context: context, type: SnackbarType.success, message: state.message ?? '');
              } else if (state.pageState == PageState.failure) {
                LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: state.message ?? '');
              }  else if (state.pageState == PageState.error) {
                LeadratCustomSnackbar.show(context: context, type: SnackbarType.error, message: state.message ?? '');
              }   else if (state.pageState == PageState.loading) {
                Navigator.push(context, MaterialPageRoute(builder: (context) => CallRecordingsPathSetPage()));
              }
            },
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Essential Permission \nRequired',
                    style: LexendTextStyles.lexend13SemiBold.copyWith(color: ColorPalette.white, fontSize: 20),
                  ),
                  const SizedBox(height: 10),
                  Text(
                    'We need phone state permission to get started. Other permissions will be requested when needed.',
                    style: LexendTextStyles.lexend12Light.copyWith(color: ColorPalette.gray500),
                  ),
                  const SizedBox(height: 20),
                  Expanded(
                    child: ListView.builder(
                      itemCount: state.permissionsList?.length,
                      itemBuilder: (context, index) {
                        final permission = state.permissionsList?[index];
                        return buildPermissionCard(
                          permissionItem: permission,
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 10),
                  Align(
                    alignment: Alignment.centerRight, // Moves button to the right
                    child: Container(
                      width: 150, // Fixed button width
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(40),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.4),
                            blurRadius: 10,
                            spreadRadius: 2,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: ElevatedButton(
                        onPressed: () {
                          getIt<PermissionsBloc>().add(NavigateToCallDetectionPageEvent());
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.black,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(40),
                            side: BorderSide(color: Colors.white.withOpacity(0.3), width: 1),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              "continue",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.white,
                              ),
                            ),
                            Container(
                              width: 40,
                              height: 40,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.black54,
                              ),
                              child: const Icon(Icons.arrow_forward, color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
