<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginStart="10dp"
    android:layout_marginEnd="10dp"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:id="@+id/leadrat_logo"
            android:layout_width="95dp"
            android:layout_height="95dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/leadrat_icon"
            android:contentDescription="Leadrat Logo"
            android:tint="#F5F5F5" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="12dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="18sp"
                android:textStyle="bold"
                android:textColor="#F5F5F5" />

            <TextView
                android:id="@+id/tv_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#CFD0D0"
                android:layout_marginTop="2dp" />

            <TextView
                android:id="@+id/tv_property_details"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:textColor="#CFD0D0"
                android:layout_marginTop="12dp" />

            <TextView
                android:id="@+id/tv_meeting_schedule"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:textColor="#50BEA7"
                android:layout_marginTop="2dp" />
        </LinearLayout>

        <ImageView
            android:id="@+id/iv_cancel"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="top"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="#CFD0D0"
            android:padding="2dp" />
    </LinearLayout>
</LinearLayout>
