import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/enums/page_state_enum.dart';
import '../bloc/offline_leads_bloc.dart';
import '../../data/models/offline_lead_model.dart';

class OfflineLeadsPage extends StatefulWidget {
  const OfflineLeadsPage({Key? key}) : super(key: key);

  @override
  State<OfflineLeadsPage> createState() => _OfflineLeadsPageState();
}

class _OfflineLeadsPageState extends State<OfflineLeadsPage> {
  @override
  void initState() {
    super.initState();
    context.read<OfflineLeadsBloc>().add(InitializeOfflineLeadsEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.sync),
            onPressed: () {
              context.read<OfflineLeadsBloc>().add(SyncLeadsEvent());
            },
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'start_sync':
                  context
                      .read<OfflineLeadsBloc>()
                      .add(StartBackgroundSyncEvent());
                  break;
                case 'stop_sync':
                  context
                      .read<OfflineLeadsBloc>()
                      .add(StopBackgroundSyncEvent());
                  break;
                case 'refresh':
                  context.read<OfflineLeadsBloc>().add(RefreshLeadsEvent());
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'start_sync',
                child: Text('Start Background Sync'),
              ),
              const PopupMenuItem(
                value: 'stop_sync',
                child: Text('Stop Background Sync'),
              ),
              const PopupMenuItem(
                value: 'refresh',
                child: Text('Refresh'),
              ),
            ],
          ),
        ],
      ),
      body: BlocConsumer<OfflineLeadsBloc, OfflineLeadsState>(
        listener: (context, state) {
          if (state.errorMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage!),
                backgroundColor: Colors.red,
              ),
            );
          }
          if (state.successMessage != null) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.successMessage!),
                backgroundColor: Colors.green,
              ),
            );
          }
        },
        builder: (context, state) {
          return Column(
            children: [
              _buildSyncStatus(state),
              _buildSearchBar(),
              _buildFilterChips(state),
              Expanded(
                child: _buildLeadsList(state),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSyncStatus(OfflineLeadsState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey[100],
      child: Row(
        children: [
          Icon(
            state.isBackgroundSyncRunning ? Icons.sync : Icons.sync_disabled,
            color: state.isBackgroundSyncRunning ? Colors.green : Colors.grey,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Background Sync: ${state.isBackgroundSyncRunning ? "Running" : "Stopped"}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                if (state.lastSyncTime != null)
                  Text(
                    'Last sync: ${_formatDateTime(state.lastSyncTime!)}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
              ],
            ),
          ),
          Text(
            '${state.totalLeadsCount} leads',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          if (state.isSyncing)
            const Padding(
              padding: EdgeInsets.only(left: 8),
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TextField(
        decoration: const InputDecoration(
          hintText: 'Search leads...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(),
        ),
        onChanged: (query) {
          context.read<OfflineLeadsBloc>().add(SearchLeadsEvent(query));
        },
      ),
    );
  }

  Widget _buildFilterChips(OfflineLeadsState state) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          _buildFilterChip('All', null, state.assignedToFilter),
          _buildFilterChip('Unassigned', '00000000-0000-0000-0000-000000000000',
              state.assignedToFilter),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String? value, String? currentFilter) {
    final isSelected = currentFilter == value;
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          context.read<OfflineLeadsBloc>().add(
                FilterLeadsEvent(assignedTo: selected ? value : null),
              );
        },
      ),
    );
  }

  Widget _buildLeadsList(OfflineLeadsState state) {
    if (state.pageState == PageState.loading && state.leads.isEmpty) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.pageState == PageState.failure && state.leads.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.grey),
            const SizedBox(height: 16),
            Text(
              state.errorMessage ?? 'Failed to load leads',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<OfflineLeadsBloc>().add(GetAllLeadsEvent());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.filteredLeads.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No leads found'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: state.filteredLeads.length,
      itemBuilder: (context, index) {
        final lead = state.filteredLeads[index];
        return _buildLeadCard(lead);
      },
    );
  }

  Widget _buildLeadCard(OfflineLeadModel lead) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getAssignmentColor(lead.assignTo),
          child: Text(
            (lead.name?.isNotEmpty == true ? lead.name![0] : '?').toUpperCase(),
            style: const TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold),
          ),
        ),
        title: Text(lead.name ?? 'Unknown'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (lead.contactNo != null) Text(lead.contactNo!),
            if (lead.alternateContactNo != null)
              Text('Alt: ${lead.alternateContactNo!}'),
            Row(
              children: [
                _buildAssignmentChip(lead.assignTo),
                const SizedBox(width: 8),
                if (lead.lastModifiedOn != null)
                  Text(
                    'Modified: ${_formatDateTime(lead.lastModifiedOn!)}',
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (lead.lastSyncedAt != null)
              Text(
                _formatDateTime(lead.lastSyncedAt!),
                style: const TextStyle(fontSize: 10, color: Colors.grey),
              ),
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'delete') {
                  context
                      .read<OfflineLeadsBloc>()
                      .add(DeleteLeadEvent(lead.id));
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'delete',
                  child: Text('Delete'),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          context.read<OfflineLeadsBloc>().add(GetLeadByIdEvent(lead.id));
        },
      ),
    );
  }

  Widget _buildAssignmentChip(String? assignTo) {
    if (assignTo == null) return const SizedBox.shrink();

    final isUnassigned = assignTo == '00000000-0000-0000-0000-000000000000';

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: _getAssignmentColor(assignTo),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        isUnassigned ? 'UNASSIGNED' : 'ASSIGNED',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getAssignmentColor(String? assignTo) {
    if (assignTo == null ||
        assignTo == '00000000-0000-0000-0000-000000000000') {
      return Colors.red; // Unassigned
    } else {
      return Colors.green; // Assigned
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
