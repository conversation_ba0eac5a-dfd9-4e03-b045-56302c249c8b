import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

import '../constants/hive_model_constants.dart';

part 'base_url_model.g.dart';

@JsonSerializable(explicitToJson: true)
@HiveType(typeId: HiveModelConstants.keyValueObjTypeId)
class KeyValueObj {
  final String? key;
  final String? value;

  KeyValueObj({
    this.key,
    this.value,
  });

  factory KeyValueObj.fromJson(Map<String, dynamic> json) {
    return KeyValueObj(
      key: json['Key'] as String? ?? '',
      value: json['Value'] as String? ?? '',
    );
  }

  Map<String, dynamic> toJson() => _$KeyValueObjToJson(this);
}

@HiveType(typeId: HiveModelConstants.baseUrlModelTypeId)
class BaseUrlModel {
  final Map<String, List<KeyValueObj>>? environments;

  BaseUrlModel({this.environments});

  factory BaseUrlModel.fromJson(List<dynamic> json) {
    final Map<String, List<KeyValueObj>> groupedMap = {};

    for (var item in json) {
      final environment = item['Item1'] as String? ?? '';
      final keyValue = KeyValueObj.fromJson(item['Item2'] as Map<String, dynamic>);

      if (environment.isNotEmpty) {
        groupedMap.putIfAbsent(environment, () => []).add(keyValue);
      }
    }

    return BaseUrlModel(environments: groupedMap);
  }

  List<Map<String, dynamic>> toJson() {
    if (environments == null) return [];
    return environments!.entries
        .expand((entry) => entry.value.map((kv) => {
      "Item1": entry.key,
      "Item2": kv.toJson(),
    }))
        .toList();
  }
}