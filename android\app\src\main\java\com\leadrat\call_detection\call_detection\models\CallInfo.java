package com.leadrat.call_detection.call_detection.models;

import com.leadrat.call_detection.call_detection.enums.CallDirection;
import com.leadrat.call_detection.call_detection.enums.CallStatus;

public class CallInfo {
    public String duration;
    public CallDirection direction;
    public long startTime;
    public long endTime;
    public CallStatus status;

    public CallInfo(String duration, CallDirection direction, long startTime, long endTime, CallStatus status) {
        this.duration = duration;
        this.direction = direction;
        this.startTime = startTime;
        this.endTime = endTime;
        this.status = status;
    }
}