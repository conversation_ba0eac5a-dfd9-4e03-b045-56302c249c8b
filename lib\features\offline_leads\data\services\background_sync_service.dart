import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';
import '../datasources/offline_leads_local_datasource.dart';
import '../datasources/offline_leads_remote_datasource.dart';
import '../models/offline_lead_model.dart';
import '../repository/offline_leads_repository_impl.dart';
import 'leadrat_data_manager.dart';

class BackgroundSyncService {
  static const String syncTaskName = 'offline_leads_sync';
  static const String uniqueTaskName = 'offline_leads_sync_unique';

  // Method channel for communicating with native Android service
  static const MethodChannel _methodChannel =
      MethodChannel('call_detection_service');
  static const MethodChannel _dataSyncChannel =
      MethodChannel('data_sync_service');

  static Future<void> initialize() async {
    print('🚀 [BACKGROUND_SYNC] Initializing foreground data sync service...');

    // Initialize WorkManager as fallback (but we'll primarily use foreground service)
    await Workmanager().initialize(
      callbackDispatcher,
      isInDebugMode: kDebugMode,
    );

    // Set up method channel handler for background sync calls from native service
    _dataSyncChannel.setMethodCallHandler(_handleMethodCall);

    print(
        '✅ [BACKGROUND_SYNC] Foreground data sync service initialized successfully');
  }

  static Future<void> startPeriodicSync() async {
    print(
        '⏰ [BACKGROUND_SYNC] Starting foreground data sync service (every 15 minutes)...');

    try {
      // Start the native foreground service
      await _methodChannel.invokeMethod('startDataSyncService');
      print(
          '✅ [BACKGROUND_SYNC] Foreground data sync service started successfully');
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Failed to start foreground service: $e');

      // Fallback to WorkManager if foreground service fails
      print('🔄 [BACKGROUND_SYNC] Falling back to WorkManager...');
      await Workmanager().registerPeriodicTask(
        uniqueTaskName,
        syncTaskName,
        frequency: const Duration(minutes: 15),
        constraints: Constraints(
          networkType: NetworkType.connected,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false,
          requiresStorageNotLow: false,
        ),
        backoffPolicy: BackoffPolicy.exponential,
        backoffPolicyDelay: const Duration(seconds: 30),
      );
      print('✅ [BACKGROUND_SYNC] WorkManager fallback registered successfully');
    }
  }

  static Future<void> stopPeriodicSync() async {
    print('🛑 [BACKGROUND_SYNC] Stopping data sync service...');

    try {
      // Stop the native foreground service
      await _methodChannel.invokeMethod('stopDataSyncService');
      print('✅ [BACKGROUND_SYNC] Foreground data sync service stopped');
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Failed to stop foreground service: $e');
    }

    // Also stop WorkManager tasks
    await Workmanager().cancelByUniqueName(uniqueTaskName);
    print('✅ [BACKGROUND_SYNC] WorkManager tasks stopped');
  }

  static Future<bool> isServiceRunning() async {
    try {
      final result =
          await _methodChannel.invokeMethod('isDataSyncServiceRunning');
      return result as bool? ?? false;
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Failed to check service status: $e');
      return false;
    }
  }

  static Future<String> getSyncStats() async {
    try {
      final result = await _methodChannel.invokeMethod('getDataSyncStats');
      return result as String? ?? 'Stats unavailable';
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Failed to get sync stats: $e');
      return 'Stats unavailable';
    }
  }

  // Handle method calls from native Android service
  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'performBackgroundSync':
        print('🔄 [BACKGROUND_SYNC] Received sync request from native service');
        final success = await performSync();
        return success;
      case 'clearDatabase':
        print(
            '🗑️ [BACKGROUND_SYNC] Received clear database request from native service');
        final success = await clearDatabase();
        return success;
      case 'performFullSync':
        print(
            '🔄 [BACKGROUND_SYNC] Received full sync request from native service');
        final success = await performFullSync();
        return success;
      case 'saveLeadsToDatabase':
        print(
            '💾 [BACKGROUND_SYNC] Received save leads request from native service');
        final leadsData = call.arguments as List<dynamic>;
        final success = await saveLeadsToDatabase(leadsData);
        return success;
      case 'syncNativeDatabase':
        print('🔄 [BACKGROUND_SYNC] Received sync native database request');
        final success = await syncNativeDatabaseToHive();
        return success;
      case 'syncHiveToNative':
        print(
            '🔄 [BACKGROUND_SYNC] Received sync Hive to native database request');
        final success = await syncHiveToNativeDatabase();
        return success;
      case 'processIncrementalChanges':
        print(
            '🔄 [BACKGROUND_SYNC] Received incremental changes request from native service');
        final arguments = call.arguments as Map<String, dynamic>;
        final changesData = arguments['changes'] as List<dynamic>;
        final type = arguments['type'] as String;
        final success = await processIncrementalChanges(changesData, type);
        return success;
      default:
        print('❌ [BACKGROUND_SYNC] Unknown method call: ${call.method}');
        throw PlatformException(
          code: 'UNIMPLEMENTED',
          message: 'Method ${call.method} not implemented',
        );
    }
  }

  static Future<void> performOneTimeSync() async {
    final taskId = 'one_time_sync_${DateTime.now().millisecondsSinceEpoch}';
    print(
        '🔄 [BACKGROUND_SYNC] Triggering one-time sync (Task ID: $taskId)...');
    await Workmanager().registerOneOffTask(
      taskId,
      syncTaskName,
      constraints: Constraints(
        networkType: NetworkType.connected,
      ),
    );
    print('✅ [BACKGROUND_SYNC] One-time sync task registered');
  }

  static Future<bool> performSync() async {
    try {
      print('🔄 [BACKGROUND_SYNC] Starting background sync operation...');
      final startTime = DateTime.now();

      // Initialize Hive for background isolate
      print('🔄 [BACKGROUND_SYNC] Initializing Hive for background isolate...');
      await Hive.initFlutter();

      print('🔄 [BACKGROUND_SYNC] Registering Hive adapters...');
      if (!Hive.isAdapterRegistered(114)) {
        Hive.registerAdapter(OfflineLeadModelAdapter());
        print(
            '🔄 [BACKGROUND_SYNC] OfflineLeadModel adapter registered with typeId 114');
      }

      // Initialize LeadratDataManager and wait for data
      print('🔄 [BACKGROUND_SYNC] Initializing LeadratDataManager...');
      await LeadratDataManager.instance.initialize();

      print('⏳ [BACKGROUND_SYNC] Waiting for tenant ID and user ID...');
      final leadratData = await LeadratDataManager.instance.waitForData(
        timeout: const Duration(seconds: 30),
      );

      final tenantId = leadratData['tenantId'];
      final userId = leadratData['userId'];

      if (tenantId == null || userId == null) {
        print(
            '❌ [BACKGROUND_SYNC] Failed to get tenant ID or user ID after waiting');
        print('❌ [BACKGROUND_SYNC] TenantId: $tenantId, UserId: $userId');
        print(
            '⚠️ [BACKGROUND_SYNC] Skipping sync operation - will retry in next cycle');
        return false; // Don't throw exception, just skip this sync cycle
      }

      print(
          '✅ [BACKGROUND_SYNC] Got required data - TenantId: $tenantId, UserId: $userId');

      // Create data sources
      print('🔄 [BACKGROUND_SYNC] Creating data sources...');
      final localDataSource = OfflineLeadsLocalDataSourceImpl();
      final remoteDataSource = OfflineLeadsRemoteDataSourceImpl(
        httpClient: http.Client(),
        baseUrl: await _getBaseUrl(),
        tenantId: tenantId,
        userId: userId,
      );

      // Create repository
      print('🔄 [BACKGROUND_SYNC] Creating repository...');
      final repository = OfflineLeadsRepositoryImpl(
        localDataSource: localDataSource,
        remoteDataSource: remoteDataSource,
      );

      // Perform sync (now includes both leads and prospects)
      print(
          '🔄 [BACKGROUND_SYNC] Executing sync operation for leads and prospects...');
      final result = await repository.syncLeads();

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return result.fold(
        (error) {
          print(
              '❌ [BACKGROUND_SYNC] Sync failed after ${duration.inSeconds}s: $error');
          return false;
        },
        (leads) {
          print(
              '✅ [BACKGROUND_SYNC] Sync successful after ${duration.inSeconds}s: ${leads.length} leads and prospects in database');
          return true;
        },
      );
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Sync error: $e');
      return false;
    }
  }

  /// Clear the local database
  static Future<bool> clearDatabase() async {
    try {
      print('🗑️ [BACKGROUND_SYNC] Starting database clear operation...');

      // Initialize Hive for background isolate
      await Hive.initFlutter();

      if (!Hive.isAdapterRegistered(114)) {
        Hive.registerAdapter(OfflineLeadModelAdapter());
      }

      // Create local data source and clear database
      final localDataSource = OfflineLeadsLocalDataSourceImpl();
      await localDataSource.deleteAllLeads();

      print('✅ [BACKGROUND_SYNC] Database cleared successfully');
      return true;
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Database clear error: $e');
      return false;
    }
  }

  /// Clear sync time to force full sync
  static Future<bool> clearSyncTime() async {
    try {
      print('🔄 [BACKGROUND_SYNC] Clearing sync time...');

      // Initialize Hive for background isolate
      await Hive.initFlutter();

      if (!Hive.isAdapterRegistered(114)) {
        Hive.registerAdapter(OfflineLeadModelAdapter());
      }

      // Create local data source and clear sync time
      final localDataSource = OfflineLeadsLocalDataSourceImpl();
      await localDataSource
          .updateLastSyncTime(DateTime.fromMillisecondsSinceEpoch(0));

      print('✅ [BACKGROUND_SYNC] Sync time cleared successfully');
      return true;
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Clear sync time error: $e');
      return false;
    }
  }

  /// Perform full sync (clear and reload all leads and prospects data)
  static Future<bool> performFullSync() async {
    try {
      print(
          '🔄 [BACKGROUND_SYNC] Starting full sync operation for leads and prospects...');
      final startTime = DateTime.now();

      // Initialize Hive for background isolate
      await Hive.initFlutter();

      if (!Hive.isAdapterRegistered(114)) {
        Hive.registerAdapter(OfflineLeadModelAdapter());
      }

      // Initialize LeadratDataManager and wait for data
      await LeadratDataManager.instance.initialize();

      final leadratData = await LeadratDataManager.instance.waitForData(
        timeout: const Duration(seconds: 30),
      );

      final tenantId = leadratData['tenantId'];
      final userId = leadratData['userId'];

      if (tenantId == null || userId == null) {
        print(
            '❌ [BACKGROUND_SYNC] Failed to get tenant ID or user ID for full sync');
        return false;
      }

      print(
          '✅ [BACKGROUND_SYNC] Got required data for full sync - TenantId: $tenantId, UserId: $userId');

      // Create data sources
      final localDataSource = OfflineLeadsLocalDataSourceImpl();
      final remoteDataSource = OfflineLeadsRemoteDataSourceImpl(
        httpClient: http.Client(),
        baseUrl: await _getBaseUrl(),
        tenantId: tenantId,
        userId: userId,
      );

      // Clear existing data first
      await localDataSource.deleteAllLeads();
      print('🗑️ [BACKGROUND_SYNC] Cleared existing database for full sync');

      // Clear last sync time to force full reload
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('last_sync_time');
      print('🔄 [BACKGROUND_SYNC] Reset sync time for full reload');

      // Create repository and perform sync
      final repository = OfflineLeadsRepositoryImpl(
        localDataSource: localDataSource,
        remoteDataSource: remoteDataSource,
      );

      final result = await repository.syncLeads();
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      return result.fold(
        (error) {
          print(
              '❌ [BACKGROUND_SYNC] Full sync failed after ${duration.inSeconds}s: $error');
          return false;
        },
        (leads) {
          print(
              '✅ [BACKGROUND_SYNC] Full sync successful after ${duration.inSeconds}s: ${leads.length} leads and prospects in database');
          return true;
        },
      );
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Full sync error: $e');
      return false;
    }
  }

  /// Save leads to database from native Android service
  static Future<bool> saveLeadsToDatabase(List<dynamic> leadsData) async {
    try {
      print(
          '💾 [BACKGROUND_SYNC] Starting save leads operation with ${leadsData.length} leads...');

      // Initialize Hive for background isolate
      await Hive.initFlutter();

      if (!Hive.isAdapterRegistered(114)) {
        Hive.registerAdapter(OfflineLeadModelAdapter());
      }

      // Convert the leads data to OfflineLeadModel objects
      final leads = <OfflineLeadModel>[];

      for (final leadData in leadsData) {
        try {
          final leadMap = Map<String, dynamic>.from(leadData as Map);

          // Parse lastModifiedOn if present
          DateTime? lastModifiedOn;
          if (leadMap['lastModifiedOn'] != null &&
              leadMap['lastModifiedOn'].toString().isNotEmpty) {
            try {
              lastModifiedOn =
                  DateTime.parse(leadMap['lastModifiedOn'].toString());
            } catch (e) {
              print(
                  '⚠️ [BACKGROUND_SYNC] Failed to parse lastModifiedOn for lead ${leadMap['id']}: $e');
            }
          }

          // Extract isLead field from the data, default to true for backward compatibility
          final isLead = leadMap['isLead'] as bool? ?? true;

          final lead = OfflineLeadModel(
            id: leadMap['id']?.toString() ?? '',
            name: leadMap['name']?.toString(),
            contactNo: leadMap['contactNo']?.toString(),
            alternateContactNo: leadMap['alternateContactNo']?.toString(),
            assignTo: leadMap['assignTo']?.toString(),
            isDeleted: leadMap['isDeleted'] as bool? ?? false,
            isArchived: leadMap['isArchived'] as bool? ?? false,
            lastModifiedOn: lastModifiedOn,
            isLead: isLead, // Use the isLead field from the data
          );

          leads.add(lead);
          print(
              '💾 [BACKGROUND_SYNC] Converted lead: {id: ${lead.id}, name: ${lead.name}, contactNo: ${lead.contactNo}, isLead: ${lead.isLead}}');
        } catch (e) {
          print('❌ [BACKGROUND_SYNC] Error converting lead data: $e');
          print('❌ [BACKGROUND_SYNC] Lead data: $leadData');
        }
      }

      if (leads.isEmpty) {
        print('⚠️ [BACKGROUND_SYNC] No valid leads to save');
        return false;
      }

      // Create local data source and save leads
      final localDataSource = OfflineLeadsLocalDataSourceImpl();
      await localDataSource.insertOrUpdateLeads(leads);

      print(
          '✅ [BACKGROUND_SYNC] Successfully saved ${leads.length} leads to database');
      return true;
    } catch (e) {
      print('❌ [BACKGROUND_SYNC] Save leads error: $e');
      return false;
    }
  }

  /// Process incremental changes from native service
  static Future<bool> processIncrementalChanges(
      List<dynamic> changesData, String type) async {
    try {
      print(
          '🔄 [INCREMENTAL_SYNC] Processing ${changesData.length} incremental changes for type: $type...');

      // Initialize Hive for background isolate
      await Hive.initFlutter();
      if (!Hive.isAdapterRegistered(114)) {
        Hive.registerAdapter(OfflineLeadModelAdapter());
      }

      // Create local data source
      final localDataSource = OfflineLeadsLocalDataSourceImpl();

      // Convert changes data to OfflineLeadModel objects
      final List<OfflineLeadModel> changes = [];
      for (final changeData in changesData) {
        try {
          final changeMap = changeData as Map<String, dynamic>;

          DateTime? lastModifiedOn;
          if (changeMap['lastModifiedOn'] != null) {
            try {
              lastModifiedOn =
                  DateTime.parse(changeMap['lastModifiedOn'].toString());
            } catch (e) {
              print(
                  '⚠️ [INCREMENTAL_SYNC] Failed to parse lastModifiedOn for change ${changeMap['id']}: $e');
            }
          }

          // Set isLead based on the API endpoint type
          final isLead =
              type == 'lead'; // true for lead API, false for prospect API

          final change = OfflineLeadModel(
            id: changeMap['id']?.toString() ?? '',
            name: changeMap['name']?.toString() ?? '',
            contactNo: changeMap['contactNo']?.toString() ?? '',
            alternateContactNo: changeMap['alternateContactNo']?.toString(),
            assignTo: changeMap['assignTo']?.toString(),
            isDeleted:
                changeMap['isDeleted'] == true || changeMap['isDeleted'] == 1,
            isArchived:
                changeMap['isArchived'] == true || changeMap['isArchived'] == 1,
            lastModifiedOn: lastModifiedOn,
            isLead: isLead, // Set based on API endpoint type
          );

          changes.add(change);
          print(
              '🔄 [INCREMENTAL_SYNC] Converted change: {id: ${change.id}, name: ${change.name}, isDeleted: ${change.isDeleted}, isArchived: ${change.isArchived}, isLead: ${change.isLead}}');
        } catch (e) {
          print('❌ [INCREMENTAL_SYNC] Error converting change data: $e');
          print('❌ [INCREMENTAL_SYNC] Change data: $changeData');
        }
      }

      if (changes.isEmpty) {
        print('⚠️ [INCREMENTAL_SYNC] No valid changes to process');
        return true; // Not an error, just no changes
      }

      // Process incremental changes (insert/update/delete)
      await localDataSource.processIncrementalChanges(changes);

      print(
          '✅ [INCREMENTAL_SYNC] Successfully processed ${changes.length} incremental changes');
      return true;
    } catch (e) {
      print('❌ [INCREMENTAL_SYNC] Process incremental changes error: $e');
      return false;
    }
  }

  /// Sync Hive database to native SQLite when app is available
  static Future<bool> syncHiveToNativeDatabase() async {
    try {
      print('🔄 [HIVE_TO_NATIVE] Starting Hive to native database sync...');
      print(
          '🔄 [HIVE_TO_NATIVE] Current time: ${DateTime.now().toIso8601String()}');

      // Initialize Hive
      await Hive.initFlutter();
      if (!Hive.isAdapterRegistered(114)) {
        Hive.registerAdapter(OfflineLeadModelAdapter());
      }

      // Get all data from Hive
      final localDataSource = OfflineLeadsLocalDataSourceImpl();
      final hiveLeads = await localDataSource.getAllLeads();

      if (hiveLeads.isEmpty) {
        print('⚠️ [HIVE_TO_NATIVE] Hive database is empty - nothing to sync');
        return true; // Not an error, just no data
      }

      print(
          '🔄 [HIVE_TO_NATIVE] Found ${hiveLeads.length} leads in Hive database');

      // Log breakdown of data
      final leadsCount = hiveLeads.where((lead) => lead.isLead).length;
      final prospectsCount = hiveLeads.where((lead) => !lead.isLead).length;
      print(
          '🔄 [HIVE_TO_NATIVE] Data breakdown: $leadsCount leads, $prospectsCount prospects');

      // Convert Hive leads to format expected by native database
      final leadsData = hiveLeads
          .map((lead) => {
                'id': lead.id,
                'name': lead.name ?? '',
                'contactNo': lead.contactNo ?? '',
                'alternateContactNo': lead.alternateContactNo ?? '',
                'assignTo': lead.assignTo ?? '',
                'isDeleted': lead.isDeleted,
                'isArchived': lead.isArchived,
                'isLead':
                    lead.isLead, // Include isLead field for proper API routing
                'lastModifiedOn': lead.lastModifiedOn?.toIso8601String() ?? '',
              })
          .toList();

      // Use platform channel to send data to native SQLite database
      const platform = MethodChannel('data_sync_service');

      try {
        final result =
            await platform.invokeMethod('saveHiveDataToNative', leadsData);

        if (result == true) {
          print(
              '✅ [HIVE_TO_NATIVE] Successfully synced ${hiveLeads.length} leads from Hive to native database');
          return true;
        } else {
          print(
              '❌ [HIVE_TO_NATIVE] Failed to sync Hive data to native database');
          return false;
        }
      } catch (e) {
        print('❌ [HIVE_TO_NATIVE] Error calling native method: $e');
        return false;
      }
    } catch (e) {
      print('❌ [HIVE_TO_NATIVE] Error in syncHiveToNativeDatabase: $e');
      return false;
    }
  }

  /// Sync native SQLite database to Hive when app becomes available
  static Future<bool> syncNativeDatabaseToHive() async {
    try {
      print('🔄 [NATIVE_SYNC] Starting native database to Hive sync...');

      // Use platform channel to get data from native SQLite database
      const platform = MethodChannel('data_sync_service');

      try {
        final result = await platform.invokeMethod('getNativeLeadsData');

        if (result == null) {
          print('⚠️ [NATIVE_SYNC] No data received from native database');
          return false;
        }

        final leadsData = result as List<dynamic>;
        print(
            '🔄 [NATIVE_SYNC] Received ${leadsData.length} leads from native database');

        if (leadsData.isEmpty) {
          print('⚠️ [NATIVE_SYNC] Native database is empty');
          return true; // Not an error, just no data
        }

        // Initialize Hive
        await Hive.initFlutter();
        if (!Hive.isAdapterRegistered(114)) {
          Hive.registerAdapter(OfflineLeadModelAdapter());
        }

        // Check if shouldViewOnlyAssigned has changed to determine if we need to clear Hive database
        final prefs = await SharedPreferences.getInstance();
        final shouldClearHive =
            prefs.getBool('should_clear_hive_for_settings_change') ?? false;

        bool success = false;

        if (shouldClearHive) {
          print(
              '🗑️ [NATIVE_SYNC] shouldViewOnlyAssigned changed - clearing Hive database before syncing native data...');
          final clearSuccess = await clearDatabase();

          if (!clearSuccess) {
            print('❌ [NATIVE_SYNC] Failed to clear Hive database');
            return false;
          }

          print(
              '✅ [NATIVE_SYNC] Hive database cleared due to shouldViewOnlyAssigned change');

          // Clear the flag since we've processed the change
          await prefs.remove('should_clear_hive_for_settings_change');

          // Convert and save to Hive (full sync)
          success = await saveLeadsToDatabase(leadsData);

          if (success) {
            print(
                '✅ [NATIVE_SYNC] Successfully synced ${leadsData.length} leads from native database to Hive (full sync due to settings change)');
          }
        } else {
          print(
              '🔄 [NATIVE_SYNC] No shouldViewOnlyAssigned change detected - performing incremental sync to Hive...');

          // Convert native data to OfflineLeadModel objects for incremental processing
          final leads = <OfflineLeadModel>[];
          for (final leadData in leadsData) {
            try {
              final lead = OfflineLeadModel.fromJson(
                  Map<String, dynamic>.from(leadData));
              leads.add(lead);
            } catch (e) {
              print('⚠️ [NATIVE_SYNC] Error parsing lead data: $e');
              continue;
            }
          }

          if (leads.isNotEmpty) {
            // Use incremental processing to add/update leads without clearing existing data
            final localDataSource = OfflineLeadsLocalDataSourceImpl();
            await localDataSource.processIncrementalChanges(leads);
            print(
                '✅ [NATIVE_SYNC] Successfully processed ${leads.length} incremental changes from native database to Hive');
            success = true;
          } else {
            print('⚠️ [NATIVE_SYNC] No valid leads to process');
            success = true; // Not an error, just no valid data
          }
        }

        if (success) {
          // Check final database count
          final localDataSource = OfflineLeadsLocalDataSourceImpl();
          final finalCount = await localDataSource.getLeadsCount();

          print('✅ [NATIVE_SYNC] Successfully synced native database to Hive');
          print(
              '📊 [NATIVE_SYNC] Final Hive database count: $finalCount leads');
          print(
              '🔄 [NATIVE_SYNC] Native database had: ${leadsData.length} leads');

          // Mark that sync is complete
          await prefs.setBool('native_db_synced', true);
          await prefs.setInt(
              'native_db_sync_time', DateTime.now().millisecondsSinceEpoch);
        }

        return success;
      } catch (e) {
        print('❌ [NATIVE_SYNC] Error calling native method: $e');
        return false;
      }
    } catch (e) {
      print('❌ [NATIVE_SYNC] Error in syncNativeDatabaseToHive: $e');
      return false;
    }
  }

  static Future<String> _getBaseUrl() async {
    // Get base URL from shared preferences or use default
    // This should match your app's base URL configuration
    print(
        '🔧 [BACKGROUND_SYNC] Using base URL: https://prd-mobile.leadrat.com');
    return 'https://prd-mobile.leadrat.com';
  }
}

// This is the callback dispatcher that runs in the background isolate
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      print('🚀 [BACKGROUND_TASK] Background task started: $task');
      print('🚀 [BACKGROUND_TASK] Input data: $inputData');
      print(
          '🚀 [BACKGROUND_TASK] Task execution time: ${DateTime.now().toIso8601String()}');

      if (task == BackgroundSyncService.syncTaskName) {
        print('🚀 [BACKGROUND_TASK] Executing sync task...');
        final success = await BackgroundSyncService.performSync();
        print(
            '🚀 [BACKGROUND_TASK] Sync task completed: ${success ? "SUCCESS" : "FAILED"}');
        return success;
      }

      print('❌ [BACKGROUND_TASK] Unknown task: $task');
      return false;
    } catch (e) {
      print('❌ [BACKGROUND_TASK] Background task error: $e');
      return false;
    }
  });
}

// Extension to handle background sync status
class SyncStatus {
  final bool isRunning;
  final DateTime? lastSyncTime;
  final String? lastError;
  final int syncCount;

  SyncStatus({
    required this.isRunning,
    this.lastSyncTime,
    this.lastError,
    required this.syncCount,
  });

  SyncStatus copyWith({
    bool? isRunning,
    DateTime? lastSyncTime,
    String? lastError,
    int? syncCount,
  }) {
    return SyncStatus(
      isRunning: isRunning ?? this.isRunning,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      lastError: lastError ?? this.lastError,
      syncCount: syncCount ?? this.syncCount,
    );
  }
}
