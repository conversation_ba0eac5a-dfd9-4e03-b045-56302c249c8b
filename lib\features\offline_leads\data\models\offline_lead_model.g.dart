// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'offline_lead_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OfflineLeadModelAdapter extends TypeAdapter<OfflineLeadModel> {
  @override
  final int typeId = 114;

  @override
  OfflineLeadModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return OfflineLeadModel(
      id: fields[0] as String,
      name: fields[1] as String?,
      contactNo: fields[2] as String?,
      alternateContactNo: fields[3] as String?,
      assignTo: fields[4] as String?,
      isDeleted: fields[5] as bool,
      isArchived: fields[6] as bool,
      lastModifiedOn: fields[7] as DateTime?,
      lastSyncedAt: fields[8] as DateTime?,
      isLead: fields[9] as bool,
    );
  }

  @override
  void write(BinaryWriter writer, OfflineLeadModel obj) {
    writer
      ..writeByte(10)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.contactNo)
      ..writeByte(3)
      ..write(obj.alternateContactNo)
      ..writeByte(4)
      ..write(obj.assignTo)
      ..writeByte(5)
      ..write(obj.isDeleted)
      ..writeByte(6)
      ..write(obj.isArchived)
      ..writeByte(7)
      ..write(obj.lastModifiedOn)
      ..writeByte(8)
      ..write(obj.lastSyncedAt)
      ..writeByte(9)
      ..write(obj.isLead);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OfflineLeadModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OfflineLeadModel _$OfflineLeadModelFromJson(Map<String, dynamic> json) =>
    OfflineLeadModel(
      id: json['id'] as String,
      name: json['name'] as String?,
      contactNo: json['contactNo'] as String?,
      alternateContactNo: json['alternateContactNo'] as String?,
      assignTo: json['assignTo'] as String?,
      isDeleted: json['isDeleted'] as bool? ?? false,
      isArchived: json['isArchived'] as bool? ?? false,
      lastModifiedOn: json['lastModifiedOn'] == null
          ? null
          : DateTime.parse(json['lastModifiedOn'] as String),
      lastSyncedAt: json['lastSyncedAt'] == null
          ? null
          : DateTime.parse(json['lastSyncedAt'] as String),
      isLead: json['isLead'] as bool? ?? false,
    );

Map<String, dynamic> _$OfflineLeadModelToJson(OfflineLeadModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'contactNo': instance.contactNo,
      'alternateContactNo': instance.alternateContactNo,
      'assignTo': instance.assignTo,
      'isDeleted': instance.isDeleted,
      'isArchived': instance.isArchived,
      'lastModifiedOn': instance.lastModifiedOn?.toIso8601String(),
      'lastSyncedAt': instance.lastSyncedAt?.toIso8601String(),
      'isLead': instance.isLead,
    };
