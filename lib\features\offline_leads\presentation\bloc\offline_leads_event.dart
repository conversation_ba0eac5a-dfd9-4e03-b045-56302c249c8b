part of 'offline_leads_bloc.dart';

@immutable
abstract class OfflineLeadsEvent {}

class InitializeOfflineLeadsEvent extends OfflineLeadsEvent {}

class SyncLeadsEvent extends OfflineLeadsEvent {}

class GetAllLeadsEvent extends OfflineLeadsEvent {}

class GetLeadByIdEvent extends OfflineLeadsEvent {
  final String id;
  
  GetLeadByIdEvent(this.id);
}

class DeleteLeadEvent extends OfflineLeadsEvent {
  final String id;
  
  DeleteLeadEvent(this.id);
}

class DeleteAllLeadsEvent extends OfflineLeadsEvent {}

class StartBackgroundSyncEvent extends OfflineLeadsEvent {}

class StopBackgroundSyncEvent extends OfflineLeadsEvent {}

class GetSyncStatusEvent extends OfflineLeadsEvent {}

class RefreshLeadsEvent extends OfflineLeadsEvent {}

class SearchLeadsEvent extends OfflineLeadsEvent {
  final String query;
  
  SearchLeadsEvent(this.query);
}

class FilterLeadsEvent extends OfflineLeadsEvent {
  final String? status;
  final String? source;
  final String? assignedTo;

  FilterLeadsEvent({
    this.status,
    this.source,
    this.assignedTo,
  });
}

class CompleteDatabaseRefreshEvent extends OfflineLeadsEvent {}
