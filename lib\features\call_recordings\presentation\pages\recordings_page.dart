import 'package:call_detection/core/utils/custom_snackbar.dart';
import 'package:call_detection/features/call_recordings/presentation/bloc/call_recordings_bloc/call_recordoings_bloc.dart';
import 'package:call_detection/features/call_recordings/presentation/widgets/path_popup.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/injection_container.dart';

class CallRecordingsPage extends StatefulWidget {
  const CallRecordingsPage({super.key});

  @override
  _CallRecordingsPageState createState() => _CallRecordingsPageState();
}

class _CallRecordingsPageState extends State<CallRecordingsPage> {
  late TextEditingController _controller;

  @override
  void initState() {
    _controller = TextEditingController();
    getIt<CallRecordingsBloc>().add(CheckTheCallDetectionIsActiveEvent());
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CallRecordingsBloc, CallRecordingsState>(
      listener: (context, state) {
        if (state.message != null) {
          LeadratCustomSnackbar.show(
              context: context,
              type: SnackbarType.error,
              message: state.message ?? 'Something went wrong');
        }
      },
      builder: (context, state) {
        return SafeArea(
          child: Scaffold(
            appBar: AppBar(title: const Text("Call Recordings")),
            body: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8),
                          width: MediaQuery.of(context).size.width * 0.6,
                          decoration: BoxDecoration(
                              color: Colors.black12,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Color(0XFF252525))),
                          child: Text(
                            state.directoryPath ?? '',
                            style: TextStyle(
                              color: Colors.black,
                              fontSize: 16,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        SizedBox(width: 5),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              showDialog(
                                context: context,
                                builder: (context) => PathDialog(
                                    title: "Enter Path",
                                    isSetupMode: false,
                                    controller:
                                        getIt<CallRecordingsBloc>().controller,
                                    onCancel: () => Navigator.pop(context),
                                    onConfirm: () {
                                      getIt<CallRecordingsBloc>().add(
                                          GetCallRecordingsEvent(
                                              recordingsPath:
                                                  getIt<CallRecordingsBloc>()
                                                      .controller
                                                      .text));
                                      Navigator.pop(context);
                                    }),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              padding: EdgeInsets.all(15),
                              backgroundColor: Color(0XFF50BFA8),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            child: Text(
                              'Change path',
                              style: TextStyle(
                                color: Color(0XFFFFFFFF),
                                fontSize: 10,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    state.recordingsList.isEmpty
                        ? Center(child: Text("No recordings found"))
                        : SizedBox(
                            height: MediaQuery.of(context).size.height * 0.7,
                            child: ListView.builder(
                              scrollDirection: Axis.vertical,
                              itemCount: state.recordingsList.length,
                              itemBuilder: (context, index) {
                                String file = state.recordingsList[index];
                                String fileName = file.split('/').last;
                                return Card(
                                  margin: EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 5),
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(10)),
                                  elevation: 3,
                                  child: ListTile(
                                    leading: Icon(Icons.music_note,
                                        color: Colors.blueAccent, size: 30),
                                    title: Text(fileName,
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold)),
                                    subtitle: Text("Tap to play/pause"),
                                    trailing: IconButton(
                                      icon: Icon(
                                        // currentPlayingFile == file.path ? Icons.pause_circle_filled :
                                        Icons.play_circle_fill,
                                        size: 30,
                                        color: Colors.green,
                                      ),
                                      onPressed: () {},
                                    ),
                                  ),
                                );
                              },
                            )),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
