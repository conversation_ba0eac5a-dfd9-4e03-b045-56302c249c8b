part of 'offline_leads_bloc.dart';

@immutable
class OfflineLeadsState {
  final PageState pageState;
  final List<OfflineLeadModel> leads;
  final List<OfflineLeadModel> filteredLeads;
  final OfflineLeadModel? selectedLead;
  final String? errorMessage;
  final String? successMessage;
  final bool isBackgroundSyncRunning;
  final DateTime? lastSyncTime;
  final int totalLeadsCount;
  final int hiveNativeDbCount;
  final bool isLoading;
  final bool isSyncing;
  final String? searchQuery;
  final String? statusFilter;
  final String? sourceFilter;
  final String? assignedToFilter;
  final int currentSyncPage;
  final int totalSyncedLeads;
  final bool isCompleteRefreshing;
  final String? refreshProgress;

  const OfflineLeadsState({
    this.pageState = PageState.initial,
    this.leads = const [],
    this.filteredLeads = const [],
    this.selectedLead,
    this.errorMessage,
    this.successMessage,
    this.isBackgroundSyncRunning = false,
    this.lastSyncTime,
    this.totalLeadsCount = 0,
    this.hiveNativeDbCount = 0,
    this.isLoading = false,
    this.isSyncing = false,
    this.searchQuery,
    this.statusFilter,
    this.sourceFilter,
    this.assignedToFilter,
    this.currentSyncPage = 0,
    this.totalSyncedLeads = 0,
    this.isCompleteRefreshing = false,
    this.refreshProgress,
  });

  OfflineLeadsState copyWith({
    PageState? pageState,
    List<OfflineLeadModel>? leads,
    List<OfflineLeadModel>? filteredLeads,
    OfflineLeadModel? selectedLead,
    String? errorMessage,
    String? successMessage,
    bool? isBackgroundSyncRunning,
    DateTime? lastSyncTime,
    int? totalLeadsCount,
    int? hiveNativeDbCount,
    bool? isLoading,
    bool? isSyncing,
    String? searchQuery,
    String? statusFilter,
    String? sourceFilter,
    String? assignedToFilter,
    int? currentSyncPage,
    int? totalSyncedLeads,
    bool? isCompleteRefreshing,
    String? refreshProgress,
  }) {
    return OfflineLeadsState(
      pageState: pageState ?? this.pageState,
      leads: leads ?? this.leads,
      filteredLeads: filteredLeads ?? this.filteredLeads,
      selectedLead: selectedLead ?? this.selectedLead,
      errorMessage: errorMessage,
      successMessage: successMessage,
      isBackgroundSyncRunning: isBackgroundSyncRunning ?? this.isBackgroundSyncRunning,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      totalLeadsCount: totalLeadsCount ?? this.totalLeadsCount,
      hiveNativeDbCount: hiveNativeDbCount ?? this.hiveNativeDbCount,
      isLoading: isLoading ?? this.isLoading,
      isSyncing: isSyncing ?? this.isSyncing,
      searchQuery: searchQuery ?? this.searchQuery,
      statusFilter: statusFilter ?? this.statusFilter,
      sourceFilter: sourceFilter ?? this.sourceFilter,
      assignedToFilter: assignedToFilter ?? this.assignedToFilter,
      currentSyncPage: currentSyncPage ?? this.currentSyncPage,
      totalSyncedLeads: totalSyncedLeads ?? this.totalSyncedLeads,
      isCompleteRefreshing: isCompleteRefreshing ?? this.isCompleteRefreshing,
      refreshProgress: refreshProgress ?? this.refreshProgress,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OfflineLeadsState &&
        other.pageState == pageState &&
        other.leads == leads &&
        other.filteredLeads == filteredLeads &&
        other.selectedLead == selectedLead &&
        other.errorMessage == errorMessage &&
        other.successMessage == successMessage &&
        other.isBackgroundSyncRunning == isBackgroundSyncRunning &&
        other.lastSyncTime == lastSyncTime &&
        other.totalLeadsCount == totalLeadsCount &&
        other.hiveNativeDbCount == hiveNativeDbCount &&
        other.isLoading == isLoading &&
        other.isSyncing == isSyncing &&
        other.searchQuery == searchQuery &&
        other.statusFilter == statusFilter &&
        other.sourceFilter == sourceFilter &&
        other.assignedToFilter == assignedToFilter;
  }

  @override
  int get hashCode {
    return Object.hash(
      pageState,
      leads,
      filteredLeads,
      selectedLead,
      errorMessage,
      successMessage,
      isBackgroundSyncRunning,
      lastSyncTime,
      totalLeadsCount,
      hiveNativeDbCount,
      isLoading,
      isSyncing,
      searchQuery,
      statusFilter,
      sourceFilter,
      assignedToFilter,
    );
  }
}
