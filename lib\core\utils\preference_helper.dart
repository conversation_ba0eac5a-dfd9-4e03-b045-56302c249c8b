import 'package:shared_preferences/shared_preferences.dart';

class PreferencesHelper {
  static Future<void> saveString(String key, String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    await prefs.setString(key, value);
  }

  static Future<String?> getString(String key) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(key);
  }

  static Future<void> saveBool(String key, bool value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    await prefs.setBool(key, value);
  }

  static Future<bool?> getBool(String key) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getBool(key);
  }

  static Future<void> clearAllData() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  // Terms and Conditions related methods
  static const String _termsAcceptedKey = 'terms_and_conditions_accepted';

  static Future<void> setTermsAccepted(bool accepted) async {
    await saveBool(_termsAcceptedKey, accepted);
  }

  static Future<bool> getTermsAccepted() async {
    return await getBool(_termsAcceptedKey) ?? false;
  }
}
