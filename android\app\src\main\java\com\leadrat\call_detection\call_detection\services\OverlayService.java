package com.leadrat.call_detection.call_detection.services;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.graphics.Typeface;
import android.os.Build;
import android.os.IBinder;
import android.provider.Settings;
import android.util.Log;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.TextView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.util.DisplayMetrics;
import android.view.ViewGroup;
import android.graphics.drawable.GradientDrawable;
import java.net.HttpURLConnection;
import java.util.UUID;
import java.util.Scanner;
import org.json.JSONObject;
import androidx.annotation.RequiresApi;
import com.leadrat.call_detection.call_detection.R;

public class OverlayService extends Service {
    private WindowManager windowManager;
    private View overlayView;
    private TextView nameText;
    private TextView phoneText;
    private TextView propertyDetails;
    private TextView meetingSchedule;
    private LinearLayout containerLayout;
    private String entityId;

    private static final int COLOR_TEXT_PRIMARY = Color.parseColor("#F5F5F5");
    private static final int COLOR_TEXT_SECONDARY = Color.parseColor("#CFD0D0");
    private static final int COLOR_ACCENT_GREEN = Color.parseColor("#50BEA7");
    private static final int COLOR_GRADIENT_START = Color.parseColor("#2F3742");
    private static final int COLOR_GRADIENT_END = Color.parseColor("#0B1218");

    // Notification constants for foreground service
    private static final String CHANNEL_ID = "overlay_service_channel";
    private static final int NOTIFICATION_ID = 2;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d("OverlayService", "📱 [OVERLAY] OverlayService onCreate");

        // Create notification channel and start as foreground service
        createNotificationChannel();

        // For Android 14+ (API 34), specify the foreground service type
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
            startForeground(NOTIFICATION_ID, createNotification(),
                android.content.pm.ServiceInfo.FOREGROUND_SERVICE_TYPE_PHONE_CALL);
        } else {
            startForeground(NOTIFICATION_ID, createNotification());
        }

        windowManager = (WindowManager) getSystemService(Context.WINDOW_SERVICE);

        LayoutInflater inflater = (LayoutInflater) getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        overlayView = inflater.inflate(R.layout.overlay_layout, null);

        initializeViews();
        setupWindowParams();
        setupClickListener();
        applyStyles();

        Log.d("OverlayService", "✅ [OVERLAY] OverlayService created successfully as foreground service");
    }

    private void initializeViews() {
        containerLayout = overlayView.findViewById(R.id.container_layout);
        nameText = overlayView.findViewById(R.id.tv_name);
        phoneText = overlayView.findViewById(R.id.tv_phone);
        propertyDetails = overlayView.findViewById(R.id.tv_property_details);
        meetingSchedule = overlayView.findViewById(R.id.tv_meeting_schedule);
        ImageView cancelIcon = overlayView.findViewById(R.id.iv_cancel);

        // Set up click listener for cancel button
        cancelIcon.setOnClickListener(v -> {
            stopSelf();
            windowManager.removeView(overlayView);
        });
    }

    private void setupWindowParams() {
        try {
            Log.d("OverlayService", "📱 [OVERLAY] Setting up window parameters");

            DisplayMetrics metrics = new DisplayMetrics();
            windowManager.getDefaultDisplay().getMetrics(metrics);
            int screenWidth = metrics.widthPixels;

            // Use TYPE_APPLICATION_OVERLAY for Android 8.0+ to ensure overlay works when app is killed
            int flag = Build.VERSION.SDK_INT >= Build.VERSION_CODES.O
                ? WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
                : WindowManager.LayoutParams.TYPE_PHONE;

            Log.d("OverlayService", "📱 [OVERLAY] Using window type: " +
                  (flag == WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY ? "TYPE_APPLICATION_OVERLAY" : "TYPE_PHONE"));

            WindowManager.LayoutParams params = new WindowManager.LayoutParams(
                    (int) (screenWidth * 0.95),
                    WindowManager.LayoutParams.WRAP_CONTENT,
                    flag,
                    WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL |
                    WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH |
                    WindowManager.LayoutParams.FLAG_SHOW_WHEN_LOCKED |
                    WindowManager.LayoutParams.FLAG_TURN_SCREEN_ON,
                    PixelFormat.TRANSLUCENT
            );
            params.gravity = Gravity.CENTER;

            // Check if we have overlay permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                Log.e("OverlayService", "❌ [OVERLAY] No overlay permission - cannot show overlay");
                stopSelf();
                return;
            }

            windowManager.addView(overlayView, params);
            Log.d("OverlayService", "✅ [OVERLAY] Overlay view added to window manager");

        } catch (Exception e) {
            Log.e("OverlayService", "❌ [OVERLAY] Error setting up window parameters: " + e.getMessage());
            stopSelf();
        }
    }

    private void applyStyles() {
        // Create gradient background
        GradientDrawable gradient = new GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            new int[] { COLOR_GRADIENT_START, COLOR_GRADIENT_END }
        );
        gradient.setCornerRadius(dpToPx(16));

        // Apply gradient to container
        LinearLayout contentContainer = overlayView.findViewById(R.id.content_container);
        contentContainer.setBackground(gradient);

        // Style text views
        nameText.setTypeface(Typeface.create("sans-serif-medium", Typeface.BOLD));
        nameText.setTextColor(COLOR_TEXT_PRIMARY);

        phoneText.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
        phoneText.setTextColor(COLOR_TEXT_SECONDARY);

        propertyDetails.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
        propertyDetails.setTextColor(COLOR_ACCENT_GREEN);

        meetingSchedule.setTypeface(Typeface.create("sans-serif", Typeface.NORMAL));
        meetingSchedule.setTextColor(COLOR_TEXT_SECONDARY);

        // Add elevation for depth
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            contentContainer.setElevation(dpToPx(8));
        }
    }

    private void setupClickListener() {
        View contentContainer = overlayView.findViewById(R.id.content_container);

        if (entityId != null && !entityId.isEmpty()) {
            contentContainer.setClickable(true);
            contentContainer.setFocusable(true);

            contentContainer.setOnClickListener(v -> {
                try {
                    String uri = String.format("app://com.leadrat.black.mobile.droid/main?&screen=21&id=%s", entityId);
                    Intent intent = new Intent(Intent.ACTION_VIEW, android.net.Uri.parse(uri));
                    intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                    startActivity(intent);

                    // Close the overlay after navigation
                    stopSelf();
                    windowManager.removeView(overlayView);
                } catch (Exception e) {
                    Log.e("OverlayService", "Error navigating to app: " + e.getMessage());
                }
            });

            // Add visual feedback for clickability
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                contentContainer.setBackground(getRippleDrawable());
            }
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d("OverlayService", "📱 [OVERLAY] onStartCommand called");

        if (intent != null) {
            String name = intent.getStringExtra("name");
            String phoneNumber = intent.getStringExtra("phoneNumber");
            String propertyDetails = intent.getStringExtra("propertyDetails");
            String meetingSchedule = intent.getStringExtra("meetingSchedule");
            String projects = intent.getStringExtra("projects");
            entityId = intent.getStringExtra("entityId");

            Log.d("OverlayService", "📱 [OVERLAY] Intent data - Name: " + name + ", Phone: " + phoneNumber);

            // Check if this is a simplified overlay (only name and phone)
            if (phoneNumber != null && propertyDetails == null && meetingSchedule == null && projects == null) {
                Log.d("OverlayService", "📞 [OVERLAY] Showing simplified overlay: " + name + " (" + phoneNumber + ")");
                updateSimplifiedOverlayData(name, phoneNumber);
            } else {
                Log.d("OverlayService", "📞 [OVERLAY] Showing full overlay with all details");
                updateOverlayData(name, propertyDetails, meetingSchedule, projects);
            }

            setupClickListener();
        } else {
            Log.w("OverlayService", "⚠️ [OVERLAY] Intent is null in onStartCommand");
        }

        // Return START_STICKY to ensure service restarts if killed
        // This is important for overlay to work when app is killed
        return START_STICKY;
    }

    public void updateOverlayData(String name, String phone, String property, String meeting) {
        if (nameText != null) {
            nameText.setText(name);
        }

        if (phoneText != null) {
            phoneText.setText(phone);
        }

        if (propertyDetails != null) {
            propertyDetails.setText(property);
        }

        if (meetingSchedule != null) {
            meetingSchedule.setText(meeting);
        }
    }

    public void updateSimplifiedOverlayData(String name, String phoneNumber) {
        Log.d("OverlayService", "📞 [OVERLAY] Updating simplified overlay data");

        if (nameText != null) {
            nameText.setText(name != null ? name : "Unknown Caller");
        }

        if (phoneText != null) {
            phoneText.setText(phoneNumber != null ? phoneNumber : "");
        }

        // Hide property details and meeting schedule for simplified overlay
        if (propertyDetails != null) {
            propertyDetails.setVisibility(View.GONE);
        }

        if (meetingSchedule != null) {
            meetingSchedule.setVisibility(View.GONE);
        }

        Log.d("OverlayService", "📞 [OVERLAY] Simplified overlay updated: " + name + " (" + phoneNumber + ")");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d("OverlayService", "📱 [OVERLAY] OverlayService onDestroy");

        try {
            if (overlayView != null && windowManager != null) {
                windowManager.removeView(overlayView);
                Log.d("OverlayService", "📱 [OVERLAY] Overlay view removed");
            }
        } catch (Exception e) {
            Log.e("OverlayService", "❌ [OVERLAY] Error removing overlay view: " + e.getMessage());
        }
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    private android.graphics.drawable.RippleDrawable getRippleDrawable() {
        // Create ripple effect that works with your gradient background
        GradientDrawable gradient = new GradientDrawable(
            GradientDrawable.Orientation.TOP_BOTTOM,
            new int[] { COLOR_GRADIENT_START, COLOR_GRADIENT_END }
        );
        gradient.setCornerRadius(dpToPx(16));

        android.content.res.ColorStateList rippleColor =
            android.content.res.ColorStateList.valueOf(Color.parseColor("#33FFFFFF"));

        return new android.graphics.drawable.RippleDrawable(
            rippleColor,
            gradient,
            null
        );
    }

    private int dpToPx(int dp) {
        return (int) (dp * getResources().getDisplayMetrics().density);
    }

    /**
     * Create notification channel for Android 8.0+
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel serviceChannel = new NotificationChannel(
                    CHANNEL_ID,
                    "Overlay Service",
                    NotificationManager.IMPORTANCE_LOW
            );
            serviceChannel.setDescription("Shows caller information overlay");
            serviceChannel.setShowBadge(false);

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(serviceChannel);
                Log.d("OverlayService", "📱 [OVERLAY] Notification channel created");
            }
        }
    }

    /**
     * Create notification for foreground service
     */
    private Notification createNotification() {
        return new Notification.Builder(this, CHANNEL_ID)
                .setContentTitle("Caller Information")
                .setContentText("Showing caller overlay")
                .setSmallIcon(R.mipmap.ic_launcher)
                .setOngoing(true)
                .build();
    }
}
