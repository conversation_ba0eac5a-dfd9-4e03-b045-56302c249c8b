import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../core/constants/color_pallete.dart';
import '../../../../core/constants/text_styles.dart';
import '../../../../core/injection_container.dart';
import '../bloc/permissions_bloc.dart';
import '../items/permission_item.dart';

Widget buildPermissionCard({
  PermissionItem? permissionItem,
}) {
  return GestureDetector(
    onTap: () => getIt<PermissionsBloc>().add(RequestPermissionsEvent(permission: permissionItem?.permission)),
    child: Container(
      margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      padding: const EdgeInsets.all(0),
      decoration: BoxDecoration(
        color: ColorPalette.primaryDarkColor,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(permissionItem?.icon ?? Icons.phone_android_rounded, color: ColorPalette.primaryGreen, size: 30),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      permissionItem?.title ?? '',
                      style: LexendTextStyles.lexend12SemiBold.copyWith(color: permissionItem?.isPermissionAllowed ?? false ? ColorPalette.lightGrey : ColorPalette.white),
                    ),
                    const SizedBox(width: 15,),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 8),
                      decoration: BoxDecoration(
                        color: Colors.grey[800],
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        "mandatory",
                        style: LexendTextStyles.lexend10Medium.copyWith(color: permissionItem?.isPermissionAllowed ?? false ? ColorPalette.mediumGrey : ColorPalette.white),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5),
                Text(
                  permissionItem?.description ?? '',
                  style: LexendTextStyles.lexend11Medium.copyWith(color: permissionItem?.isPermissionAllowed ?? false ? ColorPalette.darkGrey : ColorPalette.white),
                ),
              ],
            ),
          ),
        ],
      ),
    ),
  );
}