import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/leads_api_response_model.dart';
import '../models/api_response_wrapper_model.dart';
import '../models/offline_lead_model.dart';
import 'package:call_detection/features/call_recordings/data/services/global_settings_cache_service.dart';

abstract class OfflineLeadsRemoteDataSource {
  Future<LeadsApiResponseModel> fetchLeads({
    int page = 1,
    int pageSize = 100,
    DateTime? lastSyncTime,
    bool shouldViewOnlyAssigned = false,
  });

  Future<LeadsApiResponseModel> fetchIncrementalChanges({
    required DateTime dateRangeFrom,
    required DateTime dateRangeTo,
    required String userId,
    int page = 1,
    int pageSize = 500,
  });

  Future<LeadsApiResponseModel> fetchProspects({
    int page = 1,
    int pageSize = 100,
    DateTime? lastSyncTime,
  });

  Future<LeadsApiResponseModel> fetchProspectIncrementalChanges({
    required DateTime dateRangeFrom,
    required DateTime dateRangeTo,
    required String userId,
    int page = 1,
    int pageSize = 500,
  });

  Future<Map<String, dynamic>> fetchGlobalSettings();
}

class OfflineLeadsRemoteDataSourceImpl implements OfflineLeadsRemoteDataSource {
  final http.Client httpClient;
  final String baseUrl;
  final String tenantId;
  final String? userId;

  OfflineLeadsRemoteDataSourceImpl({
    required this.httpClient,
    required this.baseUrl,
    required this.tenantId,
    this.userId,
  });

  @override
  Future<LeadsApiResponseModel> fetchLeads({
    int page = 1,
    int pageSize = 100,
    DateTime? lastSyncTime,
    bool shouldViewOnlyAssigned = false,
  }) async {
    try {
      // Validate that userId is available
      if (userId == null || userId!.isEmpty) {
        throw Exception(
            'User ID is required for API calls but was not provided');
      }

      // Use the shouldViewOnlyAssigned parameter passed from repository
      print(
          '🔧 [API CALL] Using shouldViewOnlyAssigned parameter: $shouldViewOnlyAssigned');

      // Construct the API URL with the actual endpoint format
      final uri = Uri.parse('$baseUrl/api/v1/lead/offline').replace(
        queryParameters: {
          'UserId': userId,
          'PageNumber': page.toString(),
          'PageSize': pageSize.toString(),
          'SendOnlyAssignedLeads': shouldViewOnlyAssigned.toString(),
        },
      );

      print('🌐 [API CALL] Starting API request...');
      print('🌐 [API CALL] URL: $uri');
      print(
          '🌐 [API CALL] Headers: {accept: application/json, tenant: $tenantId}');
      print(
          '🌐 [API CALL] Parameters: {UserId: $userId, PageNumber: $page, PageSize: $pageSize, SendOnlyAssignedLeads: $shouldViewOnlyAssigned}');

      final startTime = DateTime.now();

      // Make the HTTP request
      final response = await httpClient.get(
        uri,
        headers: {
          'accept': 'application/json',
          'tenant': tenantId,
        },
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('❌ [API CALL] Request timeout after 30 seconds');
          throw const SocketException('Request timeout');
        },
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      print('🌐 [API CALL] Response received in ${duration.inMilliseconds}ms');
      print('🌐 [API CALL] Status Code: ${response.statusCode}');
      print('🌐 [API CALL] Response Headers: ${response.headers}');

      // Handle the response
      if (response.statusCode == 200) {
        print('✅ [API CALL] Success! Processing response...');
        print(
            '🌐 [API CALL] Response Body Length: ${response.body.length} characters');

        // Log first 500 characters of response for debugging
        final responsePreview = response.body.length > 500
            ? '${response.body.substring(0, 500)}...'
            : response.body;
        print('🌐 [API CALL] Response Preview: $responsePreview');

        final Map<String, dynamic> jsonData = json.decode(response.body);
        print('✅ [API CALL] JSON parsed successfully');

        // Parse the API response wrapper
        final apiWrapper = ApiResponseWrapperModel.fromJson(jsonData);
        print(
            '✅ [API CALL] API wrapper parsed: succeeded=${apiWrapper.succeeded}, dataCount=${apiWrapper.data.length}');

        if (!apiWrapper.succeeded) {
          throw Exception(
              'API returned failure: ${apiWrapper.message ?? "Unknown error"}');
        }

        // Mark all leads with isLead = true
        final leadsWithFlag =
            apiWrapper.data.map((lead) => lead.copyWith(isLead: true)).toList();

        // Create the response model with the leads marked as leads
        final apiResponse = LeadsApiResponseModel(data: leadsWithFlag);
        print(
            '✅ [API CALL] API response model created with ${apiResponse.data.length} leads');

        // Log details of first few leads
        for (int i = 0;
            i < (apiResponse.data.length > 3 ? 3 : apiResponse.data.length);
            i++) {
          final lead = apiResponse.data[i];
          print(
              '📋 [API CALL] Lead ${i + 1}: {id: ${lead.id}, name: ${lead.name}, contactNo: ${lead.contactNo}, assignTo: ${lead.assignTo}, isDeleted: ${lead.isDeleted}, isLead: ${lead.isLead}}');
        }

        return apiResponse;
      } else if (response.statusCode == 401) {
        print('❌ [API CALL] Unauthorized: Invalid credentials');
        throw Exception('Unauthorized: Invalid credentials');
      } else if (response.statusCode == 403) {
        print('❌ [API CALL] Forbidden: Access denied');
        throw Exception('Forbidden: Access denied');
      } else if (response.statusCode == 404) {
        print('❌ [API CALL] Not found: API endpoint not available');
        throw Exception('Not found: API endpoint not available');
      } else if (response.statusCode >= 500) {
        print('❌ [API CALL] Server error: ${response.statusCode}');
        print('❌ [API CALL] Server error response: ${response.body}');
        throw Exception('Server error: ${response.statusCode}');
      } else {
        print('❌ [API CALL] Failed with status ${response.statusCode}');
        print('❌ [API CALL] Error response: ${response.body}');
        throw Exception(
            'Failed to fetch leads: ${response.statusCode} - ${response.body}');
      }
    } on SocketException catch (e) {
      print('❌ [API CALL] Network error: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } on FormatException catch (e) {
      print('❌ [API CALL] Invalid response format: ${e.message}');
      throw Exception('Invalid response format: ${e.message}');
    } on http.ClientException catch (e) {
      print('❌ [API CALL] HTTP client error: ${e.message}');
      throw Exception('HTTP client error: ${e.message}');
    } catch (e) {
      print('❌ [API CALL] Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  @override
  Future<LeadsApiResponseModel> fetchIncrementalChanges({
    required DateTime dateRangeFrom,
    required DateTime dateRangeTo,
    required String userId,
    int page = 1,
    int pageSize = 500,
  }) async {
    try {
      // Format dates to UTC ISO 8601 format (simple format without microseconds)
      final fromDateString = _formatDateForApi(dateRangeFrom.toUtc());
      final toDateString = _formatDateForApi(dateRangeTo.toUtc());

      // Get shouldViewOnlyAssigned from SharedPreferences with fallback to global settings
      bool shouldViewOnlyAssigned = false;
      try {
        final prefs = await SharedPreferences.getInstance();
        shouldViewOnlyAssigned =
            prefs.getBool('shouldViewOnlyAssigned') ?? false;
        print(
            '🔧 [INCREMENTAL_SYNC] Retrieved shouldViewOnlyAssigned from SharedPreferences: $shouldViewOnlyAssigned');

        // If not found in SharedPreferences, try to fetch from global settings
        if (!shouldViewOnlyAssigned) {
          shouldViewOnlyAssigned = await _ensureGlobalSettingsLoaded();
          print(
              '🔧 [INCREMENTAL_SYNC] Retrieved shouldViewOnlyAssigned from global settings: $shouldViewOnlyAssigned');
        }
      } catch (e) {
        print('⚠️ [INCREMENTAL_SYNC] Failed to get shouldViewOnlyAssigned: $e');
        // Try to fetch from global settings as fallback
        try {
          shouldViewOnlyAssigned = await _ensureGlobalSettingsLoaded();
          print(
              '🔧 [INCREMENTAL_SYNC] Fallback - Retrieved shouldViewOnlyAssigned from global settings: $shouldViewOnlyAssigned');
        } catch (fallbackError) {
          print('⚠️ [INCREMENTAL_SYNC] Fallback also failed: $fallbackError');
        }
      }

      // Construct the incremental changes API URL
      final uri =
          Uri.parse('$baseUrl/api/v1/lead/changesbasedonlastmodified').replace(
        queryParameters: {
          'DateRangeFrom': fromDateString,
          'DateRangeTo': toDateString,
          'UserId': userId,
          'PageNumber': page.toString(),
          'PageSize': pageSize.toString(),
          'SendOnlyAssignedLeads': shouldViewOnlyAssigned.toString(),
        },
      );

      print('🔄 [INCREMENTAL_SYNC] Starting incremental sync API request...');
      print('🔄 [INCREMENTAL_SYNC] URL: $uri');
      print(
          '🔄 [INCREMENTAL_SYNC] Headers: {accept: application/json, tenant: $tenantId}');
      print(
          '🔄 [INCREMENTAL_SYNC] Parameters: {DateRangeFrom: $fromDateString, DateRangeTo: $toDateString, UserId: $userId, PageNumber: $page, PageSize: $pageSize, SendOnlyAssignedLeads: $shouldViewOnlyAssigned}');
      print(
          '🔄 [INCREMENTAL_SYNC] Date format validation: From=$fromDateString, To=$toDateString');

      final startTime = DateTime.now();

      // Make the HTTP request
      final response = await httpClient.get(
        uri,
        headers: {
          'accept': 'application/json',
          'tenant': tenantId,
        },
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('❌ [INCREMENTAL_SYNC] Request timeout after 30 seconds');
          throw const SocketException('Request timeout');
        },
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      print(
          '🔄 [INCREMENTAL_SYNC] Response received in ${duration.inMilliseconds}ms');
      print('🔄 [INCREMENTAL_SYNC] Status Code: ${response.statusCode}');
      print('🔄 [INCREMENTAL_SYNC] Response Headers: ${response.headers}');

      // Handle the response
      if (response.statusCode == 200) {
        print('✅ [INCREMENTAL_SYNC] Success! Processing response...');
        print(
            '🔄 [INCREMENTAL_SYNC] Response Body Length: ${response.body.length} characters');

        // Log first 500 characters of response for debugging
        final responsePreview = response.body.length > 500
            ? '${response.body.substring(0, 500)}...'
            : response.body;
        print('🔄 [INCREMENTAL_SYNC] Response Preview: $responsePreview');

        final Map<String, dynamic> jsonData = json.decode(response.body);
        print('✅ [INCREMENTAL_SYNC] JSON parsed successfully');

        // Parse the API response wrapper
        final apiWrapper = ApiResponseWrapperModel.fromJson(jsonData);
        print(
            '✅ [INCREMENTAL_SYNC] API wrapper parsed: succeeded=${apiWrapper.succeeded}, dataCount=${apiWrapper.data.length}');

        if (!apiWrapper.succeeded) {
          throw Exception(
              'API returned failure: ${apiWrapper.message ?? "Unknown error"}');
        }

        // Mark all leads with isLead = true
        final leadsWithFlag =
            apiWrapper.data.map((lead) => lead.copyWith(isLead: true)).toList();

        // Create the response model with the leads marked as leads
        final apiResponse = LeadsApiResponseModel(data: leadsWithFlag);
        print(
            '✅ [INCREMENTAL_SYNC] API response model created with ${apiResponse.data.length} leads');

        // Log details of leads with their modification status
        for (int i = 0;
            i < (apiResponse.data.length > 5 ? 5 : apiResponse.data.length);
            i++) {
          final lead = apiResponse.data[i];
          print(
              '📋 [INCREMENTAL_SYNC] Lead ${i + 1}: {id: ${lead.id}, name: ${lead.name}, contactNo: ${lead.contactNo}, isDeleted: ${lead.isDeleted}, isArchived: ${lead.isArchived}, isLead: ${lead.isLead}, lastModifiedOn: ${lead.lastModifiedOn?.toIso8601String()}}');
        }

        return apiResponse;
      } else if (response.statusCode == 401) {
        print('❌ [INCREMENTAL_SYNC] Unauthorized: Invalid credentials');
        throw Exception('Unauthorized: Invalid credentials');
      } else if (response.statusCode == 403) {
        print('❌ [INCREMENTAL_SYNC] Forbidden: Access denied');
        throw Exception('Forbidden: Access denied');
      } else if (response.statusCode == 404) {
        print('❌ [INCREMENTAL_SYNC] Not found: API endpoint not available');
        throw Exception('Not found: API endpoint not available');
      } else if (response.statusCode >= 500) {
        print('❌ [INCREMENTAL_SYNC] Server error: ${response.statusCode}');
        print('❌ [INCREMENTAL_SYNC] Server error response: ${response.body}');
        throw Exception('Server error: ${response.statusCode}');
      } else {
        print('❌ [INCREMENTAL_SYNC] Failed with status ${response.statusCode}');
        print('❌ [INCREMENTAL_SYNC] Error response: ${response.body}');
        throw Exception(
            'Failed to fetch incremental changes: ${response.statusCode} - ${response.body}');
      }
    } on SocketException catch (e) {
      print('❌ [INCREMENTAL_SYNC] Network error: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } on FormatException catch (e) {
      print('❌ [INCREMENTAL_SYNC] Invalid response format: ${e.message}');
      throw Exception('Invalid response format: ${e.message}');
    } on http.ClientException catch (e) {
      print('❌ [INCREMENTAL_SYNC] HTTP client error: ${e.message}');
      throw Exception('HTTP client error: ${e.message}');
    } catch (e) {
      print('❌ [INCREMENTAL_SYNC] Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  /// Format DateTime for API in the expected format: 2025-05-28T15:46:31Z
  /// Removes microseconds and ensures proper UTC format
  String _formatDateForApi(DateTime dateTime) {
    // Ensure the date is in UTC
    final utcDate = dateTime.toUtc();

    // Format as ISO 8601 without microseconds
    final isoString = utcDate.toIso8601String();

    // Remove microseconds if present (everything after the dot before Z)
    final dotIndex = isoString.indexOf('.');
    if (dotIndex != -1) {
      final beforeDot = isoString.substring(0, dotIndex);
      return '${beforeDot}Z';
    }

    return isoString;
  }

  /// Ensure global settings are loaded and return shouldViewOnlyAssigned value
  Future<bool> _ensureGlobalSettingsLoaded() async {
    try {
      print('🔧 [GLOBAL_SETTINGS] Ensuring global settings are loaded...');

      if (tenantId.isEmpty || tenantId == 'default-tenant') {
        print('⚠️ [GLOBAL_SETTINGS] No valid tenant ID available');
        return false;
      }

      // Fetch global settings using the cache service
      final settings =
          await GlobalSettingsCacheService.getCallRecordingSettings(
        tenantId: tenantId,
        forceRefresh: false, // Use cache if available
      );

      print(
          '✅ [GLOBAL_SETTINGS] Global settings loaded: shouldViewOnlyAssigned=${settings.shouldViewOnlyAssigned}');
      return settings.shouldViewOnlyAssigned;
    } catch (e) {
      print('❌ [GLOBAL_SETTINGS] Failed to load global settings: $e');
      return false; // Default to false if unable to load
    }
  }

  @override
  Future<LeadsApiResponseModel> fetchProspects({
    int page = 1,
    int pageSize = 100,
    DateTime? lastSyncTime,
  }) async {
    print(
        '🌐 [PROSPECT_API] ========== FETCH PROSPECTS METHOD CALLED ==========');
    print('🌐 [PROSPECT_API] fetchProspects called with parameters:');
    print('🌐 [PROSPECT_API] - page: $page');
    print('🌐 [PROSPECT_API] - pageSize: $pageSize');
    print('🌐 [PROSPECT_API] - lastSyncTime: $lastSyncTime');
    print('🌐 [PROSPECT_API] - userId: $userId');
    print('🌐 [PROSPECT_API] - tenantId: $tenantId');
    print('🌐 [PROSPECT_API] - baseUrl: $baseUrl');

    try {
      // Validate that userId is available
      if (userId == null || userId!.isEmpty) {
        print(
            '❌ [PROSPECT_API] User ID validation failed: userId is null or empty');
        throw Exception(
            'User ID is required for API calls but was not provided');
      }

      print('✅ [PROSPECT_API] User ID validation passed: $userId');

      // Get shouldViewOnlyAssigned from SharedPreferences first
      bool shouldViewOnlyAssigned = false;
      try {
        final prefs = await SharedPreferences.getInstance();
        shouldViewOnlyAssigned =
            prefs.getBool('shouldViewOnlyAssigned') ?? false;

        // If not found in SharedPreferences, try to fetch from global settings
        if (!shouldViewOnlyAssigned) {
          shouldViewOnlyAssigned = await _ensureGlobalSettingsLoaded();
          print(
              '🔧 [PROSPECT_API] Retrieved shouldViewOnlyAssigned from global settings: $shouldViewOnlyAssigned');
        }
      } catch (e) {
        print('⚠️ [PROSPECT_API] Failed to get shouldViewOnlyAssigned: $e');
        // Try to fetch from global settings as fallback
        try {
          shouldViewOnlyAssigned = await _ensureGlobalSettingsLoaded();
          print(
              '🔧 [PROSPECT_API] Fallback - Retrieved shouldViewOnlyAssigned from global settings: $shouldViewOnlyAssigned');
        } catch (fallbackError) {
          print('⚠️ [PROSPECT_API] Fallback also failed: $fallbackError');
        }
      }

      // Construct the API URL with the prospect endpoint
      final uri = Uri.parse('$baseUrl/api/v1/prospect/offline').replace(
        queryParameters: {
          'UserId': userId,
          'PageNumber': page.toString(),
          'PageSize': pageSize.toString(),
          'SendOnlyAssignedLeads': shouldViewOnlyAssigned.toString(),
        },
      );

      print('🌐 [PROSPECT_API] Starting prospect API request...');
      print('🌐 [PROSPECT_API] URL: $uri');
      print(
          '🌐 [PROSPECT_API] Headers: {accept: application/json, tenant: $tenantId}');
      print(
          '🌐 [PROSPECT_API] Parameters: {UserId: $userId, PageNumber: $page, PageSize: $pageSize, SendOnlyAssignedLeads: $shouldViewOnlyAssigned}');

      final startTime = DateTime.now();

      // Make the HTTP request
      final response = await httpClient.get(
        uri,
        headers: {
          'accept': 'application/json',
          'tenant': tenantId,
        },
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('❌ [PROSPECT_API] Request timeout after 30 seconds');
          throw const SocketException('Request timeout');
        },
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      print(
          '🌐 [PROSPECT_API] Response received in ${duration.inMilliseconds}ms');
      print('🌐 [PROSPECT_API] Status Code: ${response.statusCode}');
      print('🌐 [PROSPECT_API] Response Headers: ${response.headers}');

      // Handle the response
      if (response.statusCode == 200) {
        print('✅ [PROSPECT_API] Success! Processing response...');
        print(
            '🌐 [PROSPECT_API] Response Body Length: ${response.body.length} characters');

        // Log first 500 characters of response for debugging
        final responsePreview = response.body.length > 500
            ? '${response.body.substring(0, 500)}...'
            : response.body;
        print('🌐 [PROSPECT_API] Response Preview: $responsePreview');

        final Map<String, dynamic> jsonData = json.decode(response.body);
        print('✅ [PROSPECT_API] JSON parsed successfully');

        // Parse the API response wrapper
        final apiWrapper = ApiResponseWrapperModel.fromJson(jsonData);
        print(
            '✅ [PROSPECT_API] API wrapper parsed: succeeded=${apiWrapper.succeeded}, dataCount=${apiWrapper.data.length}');

        if (!apiWrapper.succeeded) {
          throw Exception(
              'API returned failure: ${apiWrapper.message ?? "Unknown error"}');
        }

        // Mark all prospects with isLead = false
        final prospectsWithFlag = apiWrapper.data
            .map((prospect) => prospect.copyWith(isLead: false))
            .toList();

        // Create the response model with the prospects marked as non-leads
        final apiResponse = LeadsApiResponseModel(data: prospectsWithFlag);
        print(
            '✅ [PROSPECT_API] API response model created with ${apiResponse.data.length} prospects');

        // Log details of first few prospects
        for (int i = 0;
            i < (apiResponse.data.length > 3 ? 3 : apiResponse.data.length);
            i++) {
          final prospect = apiResponse.data[i];
          print(
              '📋 [PROSPECT_API] Prospect ${i + 1}: {id: ${prospect.id}, name: ${prospect.name}, contactNo: ${prospect.contactNo}, assignTo: ${prospect.assignTo}, isDeleted: ${prospect.isDeleted}, isLead: ${prospect.isLead}}');
        }

        return apiResponse;
      } else if (response.statusCode == 401) {
        print('❌ [PROSPECT_API] Unauthorized: Invalid credentials');
        throw Exception('Unauthorized: Invalid credentials');
      } else if (response.statusCode == 403) {
        print('❌ [PROSPECT_API] Forbidden: Access denied');
        throw Exception('Forbidden: Access denied');
      } else if (response.statusCode == 404) {
        print('❌ [PROSPECT_API] Not found: API endpoint not available');
        throw Exception('Not found: API endpoint not available');
      } else if (response.statusCode >= 500) {
        print('❌ [PROSPECT_API] Server error: ${response.statusCode}');
        print('❌ [PROSPECT_API] Server error response: ${response.body}');
        throw Exception('Server error: ${response.statusCode}');
      } else {
        print('❌ [PROSPECT_API] Failed with status ${response.statusCode}');
        print('❌ [PROSPECT_API] Error response: ${response.body}');
        throw Exception(
            'Failed to fetch prospects: ${response.statusCode} - ${response.body}');
      }
    } on SocketException catch (e) {
      print('❌ [PROSPECT_API] Network error: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } on FormatException catch (e) {
      print('❌ [PROSPECT_API] Invalid response format: ${e.message}');
      throw Exception('Invalid response format: ${e.message}');
    } on http.ClientException catch (e) {
      print('❌ [PROSPECT_API] HTTP client error: ${e.message}');
      throw Exception('HTTP client error: ${e.message}');
    } catch (e) {
      print('❌ [PROSPECT_API] Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  @override
  Future<LeadsApiResponseModel> fetchProspectIncrementalChanges({
    required DateTime dateRangeFrom,
    required DateTime dateRangeTo,
    required String userId,
    int page = 1,
    int pageSize = 500,
  }) async {
    try {
      // Format dates to UTC ISO 8601 format (simple format without microseconds)
      final fromDateString = _formatDateForApi(dateRangeFrom.toUtc());
      final toDateString = _formatDateForApi(dateRangeTo.toUtc());

      // Get shouldViewOnlyAssigned from SharedPreferences first
      bool shouldViewOnlyAssigned = false;
      try {
        final prefs = await SharedPreferences.getInstance();
        shouldViewOnlyAssigned =
            prefs.getBool('shouldViewOnlyAssigned') ?? false;

        // If not found in SharedPreferences, try to fetch from global settings
        if (!shouldViewOnlyAssigned) {
          shouldViewOnlyAssigned = await _ensureGlobalSettingsLoaded();
          print(
              '🔧 [PROSPECT_INCREMENTAL] Retrieved shouldViewOnlyAssigned from global settings: $shouldViewOnlyAssigned');
        }
      } catch (e) {
        print(
            '⚠️ [PROSPECT_INCREMENTAL] Failed to get shouldViewOnlyAssigned: $e');
        // Try to fetch from global settings as fallback
        try {
          shouldViewOnlyAssigned = await _ensureGlobalSettingsLoaded();
          print(
              '🔧 [PROSPECT_INCREMENTAL] Fallback - Retrieved shouldViewOnlyAssigned from global settings: $shouldViewOnlyAssigned');
        } catch (fallbackError) {
          print(
              '⚠️ [PROSPECT_INCREMENTAL] Fallback also failed: $fallbackError');
        }
      }

      // Construct the incremental changes API URL for prospects
      final uri =
          Uri.parse('$baseUrl/api/v1/prospect/changesbasedonlastmodified')
              .replace(
        queryParameters: {
          'DateRangeFrom': fromDateString,
          'DateRangeTo': toDateString,
          'UserId': userId,
          'PageNumber': page.toString(),
          'PageSize': pageSize.toString(),
          'SendOnlyAssignedLeads': shouldViewOnlyAssigned.toString(),
        },
      );

      print(
          '🔄 [PROSPECT_INCREMENTAL] Starting prospect incremental sync API request...');
      print('🔄 [PROSPECT_INCREMENTAL] URL: $uri');
      print(
          '🔄 [PROSPECT_INCREMENTAL] Headers: {accept: application/json, tenant: $tenantId}');
      print(
          '🔄 [PROSPECT_INCREMENTAL] Parameters: {DateRangeFrom: $fromDateString, DateRangeTo: $toDateString, UserId: $userId, PageNumber: $page, PageSize: $pageSize, SendOnlyAssignedLeads: $shouldViewOnlyAssigned}');
      print(
          '🔄 [PROSPECT_INCREMENTAL] Date format validation: From=$fromDateString, To=$toDateString');

      final startTime = DateTime.now();

      // Make the HTTP request
      final response = await httpClient.get(
        uri,
        headers: {
          'accept': 'application/json',
          'tenant': tenantId,
        },
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('❌ [PROSPECT_INCREMENTAL] Request timeout after 30 seconds');
          throw const SocketException('Request timeout');
        },
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      print(
          '🔄 [PROSPECT_INCREMENTAL] Response received in ${duration.inMilliseconds}ms');
      print('🔄 [PROSPECT_INCREMENTAL] Status Code: ${response.statusCode}');
      print('🔄 [PROSPECT_INCREMENTAL] Response Headers: ${response.headers}');

      // Handle the response
      if (response.statusCode == 200) {
        print('✅ [PROSPECT_INCREMENTAL] Success! Processing response...');
        print(
            '🔄 [PROSPECT_INCREMENTAL] Response Body Length: ${response.body.length} characters');

        // Log first 500 characters of response for debugging
        final responsePreview = response.body.length > 500
            ? '${response.body.substring(0, 500)}...'
            : response.body;
        print('🔄 [PROSPECT_INCREMENTAL] Response Preview: $responsePreview');

        final Map<String, dynamic> jsonData = json.decode(response.body);
        print('✅ [PROSPECT_INCREMENTAL] JSON parsed successfully');

        // Parse the API response wrapper
        final apiWrapper = ApiResponseWrapperModel.fromJson(jsonData);
        print(
            '✅ [PROSPECT_INCREMENTAL] API wrapper parsed: succeeded=${apiWrapper.succeeded}, dataCount=${apiWrapper.data.length}');

        if (!apiWrapper.succeeded) {
          throw Exception(
              'API returned failure: ${apiWrapper.message ?? "Unknown error"}');
        }

        // Mark all prospects with isLead = false
        final prospectsWithFlag = apiWrapper.data
            .map((prospect) => prospect.copyWith(isLead: false))
            .toList();

        // Create the response model with the prospects marked as non-leads
        final apiResponse = LeadsApiResponseModel(data: prospectsWithFlag);
        print(
            '✅ [PROSPECT_INCREMENTAL] API response model created with ${apiResponse.data.length} prospects');

        // Log details of prospects with their modification status
        for (int i = 0;
            i < (apiResponse.data.length > 5 ? 5 : apiResponse.data.length);
            i++) {
          final prospect = apiResponse.data[i];
          print(
              '📋 [PROSPECT_INCREMENTAL] Prospect ${i + 1}: {id: ${prospect.id}, name: ${prospect.name}, contactNo: ${prospect.contactNo}, isDeleted: ${prospect.isDeleted}, isArchived: ${prospect.isArchived}, isLead: ${prospect.isLead}, lastModifiedOn: ${prospect.lastModifiedOn?.toIso8601String()}}');
        }

        return apiResponse;
      } else if (response.statusCode == 401) {
        print('❌ [PROSPECT_INCREMENTAL] Unauthorized: Invalid credentials');
        throw Exception('Unauthorized: Invalid credentials');
      } else if (response.statusCode == 403) {
        print('❌ [PROSPECT_INCREMENTAL] Forbidden: Access denied');
        throw Exception('Forbidden: Access denied');
      } else if (response.statusCode == 404) {
        print('❌ [PROSPECT_INCREMENTAL] Not found: API endpoint not available');
        throw Exception('Not found: API endpoint not available');
      } else if (response.statusCode >= 500) {
        print('❌ [PROSPECT_INCREMENTAL] Server error: ${response.statusCode}');
        print(
            '❌ [PROSPECT_INCREMENTAL] Server error response: ${response.body}');
        throw Exception('Server error: ${response.statusCode}');
      } else {
        print(
            '❌ [PROSPECT_INCREMENTAL] Failed with status ${response.statusCode}');
        print('❌ [PROSPECT_INCREMENTAL] Error response: ${response.body}');
        throw Exception(
            'Failed to fetch prospect incremental changes: ${response.statusCode} - ${response.body}');
      }
    } on SocketException catch (e) {
      print('❌ [PROSPECT_INCREMENTAL] Network error: ${e.message}');
      throw Exception('Network error: ${e.message}');
    } on FormatException catch (e) {
      print('❌ [PROSPECT_INCREMENTAL] Invalid response format: ${e.message}');
      throw Exception('Invalid response format: ${e.message}');
    } on http.ClientException catch (e) {
      print('❌ [PROSPECT_INCREMENTAL] HTTP client error: ${e.message}');
      throw Exception('HTTP client error: ${e.message}');
    } catch (e) {
      print('❌ [PROSPECT_INCREMENTAL] Unexpected error: $e');
      throw Exception('Unexpected error: $e');
    }
  }

  @override
  Future<Map<String, dynamic>> fetchGlobalSettings() async {
    try {
      print(
          '🔧 [GLOBAL_SETTINGS_API] Fetching global settings from API (direct call)...');

      if (tenantId.isEmpty || tenantId == 'default-tenant') {
        print('⚠️ [GLOBAL_SETTINGS_API] No valid tenant ID available');
        return {
          'shouldViewOnlyAssigned': false,
          'shouldOpenPopupAfterCall': true,
        };
      }

      // Make direct API call without using cache service to avoid infinite loop
      final url = '$baseUrl/api/v1/globalsettings/callsettings/anonymous';
      print('🔧 [GLOBAL_SETTINGS_API] Calling URL: $url');
      print('🔧 [GLOBAL_SETTINGS_API] Using tenant header: $tenantId');

      final response = await httpClient.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'tenant': tenantId,
        },
      );

      if (response.statusCode == 200) {
        print('✅ [GLOBAL_SETTINGS_API] Response received successfully');
        print('🔧 [GLOBAL_SETTINGS_API] Raw response body: ${response.body}');

        final responseData = json.decode(response.body);
        print('🔧 [GLOBAL_SETTINGS_API] Parsed JSON response: $responseData');
        print(
            '🔧 [GLOBAL_SETTINGS_API] Response type: ${responseData.runtimeType}');

        // Check if response is a Map and log all keys
        if (responseData is Map<String, dynamic>) {
          print(
              '🔧 [GLOBAL_SETTINGS_API] Available keys: ${responseData.keys.toList()}');
        }

        // Extract the actual data from the nested structure
        final data = responseData['data'] as Map<String, dynamic>? ?? {};
        print('🔧 [GLOBAL_SETTINGS_API] Extracted data object: $data');
        print(
            '🔧 [GLOBAL_SETTINGS_API] Data object keys: ${data.keys.toList()}');

        final shouldViewOnlyAssigned = data['shouldViewOnlyAssigned'] ?? false;
        final isCallRecordingEnabled = data['isCallRecordingEnabled'] ?? true;

        print(
            '✅ [GLOBAL_SETTINGS_API] Global settings fetched successfully (direct)');
        print(
            '🔧 [GLOBAL_SETTINGS_API] shouldViewOnlyAssigned: $shouldViewOnlyAssigned (from field: ${data['shouldViewOnlyAssigned']})');
        print(
            '🔧 [GLOBAL_SETTINGS_API] isCallRecordingEnabled: $isCallRecordingEnabled (from field: ${data['isCallRecordingEnabled']})');

        return {
          'shouldViewOnlyAssigned': shouldViewOnlyAssigned,
          'isCallRecordingEnabled': isCallRecordingEnabled,
          'shouldOpenPopupAfterCall': data['shouldOpenPopupAfterCall'] ?? true,
        };
      } else {
        print(
            '❌ [GLOBAL_SETTINGS_API] Failed to fetch global settings: ${response.statusCode} - ${response.body}');
        return {
          'shouldViewOnlyAssigned': false,
          'isCallRecordingEnabled': true,
          'shouldOpenPopupAfterCall': true,
        };
      }
    } catch (e) {
      print('❌ [GLOBAL_SETTINGS_API] Failed to fetch global settings: $e');
      // Return default values if unable to fetch
      return {
        'shouldViewOnlyAssigned': false,
        'isCallRecordingEnabled': true,
        'shouldOpenPopupAfterCall': true,
      };
    }
  }

  // Mock implementation for testing - replace with actual API endpoint
  Future<LeadsApiResponseModel> _mockFetchLeads({
    int page = 1,
    int pageSize = 100,
    DateTime? lastSyncTime,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));

    // Mock data matching the actual API response format
    final mockLeads = List.generate(pageSize, (index) {
      final leadId = 'lead_${page}_${index + 1}';
      return OfflineLeadModel(
        id: leadId,
        name: 'Lead Name $leadId',
        contactNo: '+1234567${(index + 1).toString().padLeft(3, '0')}',
        alternateContactNo: index % 3 == 0
            ? '+9876543${(index + 1).toString().padLeft(3, '0')}'
            : null,
        assignTo: 'user_${(index % 5) + 1}',
        isDeleted: false,
        lastModifiedOn: DateTime.now().subtract(Duration(hours: index)),
      );
    });

    return LeadsApiResponseModel(
      data: mockLeads,
    );
  }
}
