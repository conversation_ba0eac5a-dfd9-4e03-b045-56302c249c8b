import 'package:flutter_test/flutter_test.dart';
import 'package:call_detection/features/terms_and_conditions/data/services/terms_service.dart';
import 'package:call_detection/core/utils/preference_helper.dart';

void main() {
  group('TermsService', () {
    setUpAll(() async {
      // Initialize Flutter binding for SharedPreferences
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() async {
      // Reset terms acceptance before each test
      await TermsService.resetTermsAcceptance();
    });

    test('should return false when terms are not accepted initially', () async {
      final hasAccepted = await TermsService.hasAcceptedTerms();
      expect(hasAccepted, false);
    });

    test('should return true after accepting terms', () async {
      await TermsService.acceptTerms();
      final hasAccepted = await TermsService.hasAcceptedTerms();
      expect(hasAccepted, true);
    });

    test('should return false after resetting terms acceptance', () async {
      // First accept terms
      await TermsService.acceptTerms();
      expect(await TermsService.hasAcceptedTerms(), true);

      // Then reset
      await TermsService.resetTermsAcceptance();
      expect(await TermsService.hasAcceptedTerms(), false);
    });

    test('should persist terms acceptance across app sessions', () async {
      // Accept terms
      await TermsService.acceptTerms();
      expect(await TermsService.hasAcceptedTerms(), true);

      // Simulate app restart by checking directly with PreferencesHelper
      final persistedValue = await PreferencesHelper.getTermsAccepted();
      expect(persistedValue, true);
    });
  });
}
