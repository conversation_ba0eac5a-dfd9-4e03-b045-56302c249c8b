import 'package:json_annotation/json_annotation.dart';
import 'offline_lead_model.dart';

part 'leads_api_response_model.g.dart';

@JsonSerializable()
class LeadsApiResponseModel {
  final List<OfflineLeadModel> data;

  LeadsApiResponseModel({
    required this.data,
  });

  factory LeadsApiResponseModel.fromJson(List<dynamic> json) {
    return LeadsApiResponseModel(
      data: json.map((item) => OfflineLeadModel.fromJson(item as Map<String, dynamic>)).toList(),
    );
  }

  List<dynamic> toJson() => data.map((item) => item.toJson()).toList();

  @override
  String toString() {
    return 'LeadsApiResponseModel(dataCount: ${data.length})';
  }
}
