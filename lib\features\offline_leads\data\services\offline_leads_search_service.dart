import 'package:flutter/services.dart';
import '../models/offline_lead_model.dart';
import '../repository/offline_leads_repository_impl.dart';
import '../datasources/offline_leads_local_datasource.dart';

class OfflineLeadsSearchService {
  static const MethodChannel _methodChannel =
      MethodChannel('offline_leads_search');
  static OfflineLeadsRepositoryImpl? _repository;

  static void initialize(OfflineLeadsRepositoryImpl repository) {
    _repository = repository;
    _methodChannel.setMethodCallHandler(_handleMethodCall);
    print('📞 [SEARCH_SERVICE] Offline leads search service initialized');
  }

  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'searchByPhoneNumber':
        final phoneNumber = call.arguments as String?;
        if (phoneNumber != null) {
          return await _searchByPhoneNumber(phoneNumber);
        } else {
          throw PlatformException(
            code: 'INVALID_ARGUMENT',
            message: 'Phone number is required',
          );
        }
      default:
        throw PlatformException(
          code: 'UNIMPLEMENTED',
          message: 'Method ${call.method} not implemented',
        );
    }
  }

  static Future<Map<String, dynamic>?> _searchByPhoneNumber(
      String phoneNumber) async {
    try {
      print(
          '📞 [SEARCH_SERVICE] Searching for lead by phone number: $phoneNumber');
      print('📞 [SEARCH_SERVICE] Using native database search for consistency');

      // Use native database search instead of Flutter Hive to ensure consistency
      // This ensures the same database is used regardless of app state
      const platform = MethodChannel('call_detection_service');

      try {
        final result = await platform.invokeMethod('searchInNativeDatabase', {
          'phoneNumber': phoneNumber,
        });

        if (result != null && result is Map) {
          final leadData = Map<String, dynamic>.from(result);
          final entityType = (leadData['isLead'] == true) ? 'Lead' : 'Prospect';
          print(
              '✅ [SEARCH_SERVICE] $entityType found via native search: ${leadData['name']} (${leadData['contactNo']})');
          return leadData;
        } else {
          print(
              '📞 [SEARCH_SERVICE] No lead or prospect found via native search for phone number: $phoneNumber');
          return null;
        }
      } catch (platformException) {
        print(
            '⚠️ [SEARCH_SERVICE] Native search failed, falling back to Flutter Hive: $platformException');

        // Fallback to original Flutter Hive search if native search fails
        if (_repository == null) {
          print('❌ [SEARCH_SERVICE] Repository not initialized');
          throw PlatformException(
            code: 'NOT_INITIALIZED',
            message: 'Search service not initialized',
          );
        }

        final result = await _repository!.getLeadByPhoneNumber(phoneNumber);

        return result.fold(
          (error) {
            print('❌ [SEARCH_SERVICE] Fallback search failed: $error');
            throw PlatformException(
              code: 'SEARCH_ERROR',
              message: error,
            );
          },
          (lead) {
            if (lead != null) {
              final entityType = lead.isLead ? 'Lead' : 'Prospect';
              print(
                  '✅ [SEARCH_SERVICE] $entityType found via fallback: ${lead.name} (${lead.contactNo})');
              return {
                'id': lead.id,
                'name': lead.name,
                'contactNo': lead.contactNo,
                'alternateContactNo': lead.alternateContactNo,
                'assignTo': lead.assignTo,
                'isDeleted': lead.isDeleted,
                'isArchived': lead.isArchived,
                'isLead': lead.isLead,
                'lastModifiedOn': lead.lastModifiedOn?.toIso8601String(),
              };
            } else {
              print(
                  '📞 [SEARCH_SERVICE] No lead or prospect found via fallback for phone number: $phoneNumber');
              return null;
            }
          },
        );
      }
    } catch (e) {
      print('❌ [SEARCH_SERVICE] Unexpected error during search: $e');
      rethrow;
    }
  }

  /// Direct search method that can be called from Flutter code
  static Future<OfflineLeadModel?> searchLeadByPhoneNumber(
      String phoneNumber) async {
    try {
      print(
          '📞 [SEARCH_SERVICE] Direct search for lead or prospect by phone number: $phoneNumber');
      print('📞 [SEARCH_SERVICE] Using native database search for consistency');

      // Use native database search instead of Flutter Hive to ensure consistency
      const platform = MethodChannel('call_detection_service');

      try {
        final result = await platform.invokeMethod('searchInNativeDatabase', {
          'phoneNumber': phoneNumber,
        });

        if (result != null && result is Map) {
          final leadData = Map<String, dynamic>.from(result);
          final entityType = (leadData['isLead'] == true) ? 'Lead' : 'Prospect';
          print(
              '✅ [SEARCH_SERVICE] Direct search - $entityType found via native: ${leadData['name']} (${leadData['contactNo']})');

          // Convert the native result back to OfflineLeadModel
          return OfflineLeadModel(
            id: leadData['id'] ?? '',
            name: leadData['name'] ?? '',
            contactNo: leadData['contactNo'],
            alternateContactNo: leadData['alternateContactNo'],
            assignTo: leadData['assignTo'],
            isDeleted: leadData['isDeleted'] ?? false,
            isArchived: leadData['isArchived'] ?? false,
            isLead: leadData['isLead'] ?? true,
            lastModifiedOn: leadData['lastModifiedOn'] != null
                ? DateTime.tryParse(leadData['lastModifiedOn'])
                : null,
          );
        } else {
          print(
              '📞 [SEARCH_SERVICE] Direct search - No lead found via native for phone number: $phoneNumber');
          return null;
        }
      } catch (platformException) {
        print(
            '⚠️ [SEARCH_SERVICE] Native direct search failed, falling back to Flutter Hive: $platformException');

        // Fallback to original Flutter Hive search if native search fails
        if (_repository == null) {
          print(
              '❌ [SEARCH_SERVICE] Repository not initialized for direct search fallback');
          return null;
        }

        final result = await _repository!.getLeadByPhoneNumber(phoneNumber);

        return result.fold(
          (error) {
            print('❌ [SEARCH_SERVICE] Direct search fallback failed: $error');
            return null;
          },
          (lead) {
            if (lead != null) {
              final entityType = lead.isLead ? 'Lead' : 'Prospect';
              print(
                  '✅ [SEARCH_SERVICE] Direct search fallback - $entityType found: ${lead.name} (${lead.contactNo})');
            } else {
              print(
                  '📞 [SEARCH_SERVICE] Direct search fallback - No lead or prospect found for phone number: $phoneNumber');
            }
            return lead;
          },
        );
      }
    } catch (e) {
      print('❌ [SEARCH_SERVICE] Unexpected error during direct search: $e');
      return null;
    }
  }
}
