import 'package:fpdart/fpdart.dart';
import '../../data/models/offline_lead_model.dart';

abstract class OfflineLeadsRepository {
  Future<Either<String, List<OfflineLeadModel>>> syncLeads();
  Future<Either<String, List<OfflineLeadModel>>> getAllLeads();
  Future<Either<String, OfflineLeadModel?>> getLeadById(String id);
  Future<Either<String, OfflineLeadModel?>> getLeadByPhoneNumber(String phoneNumber);
  Future<Either<String, void>> deleteLead(String id);
  Future<Either<String, void>> deleteAllLeads();
  Future<Either<String, int>> getLeadsCount();
  Future<Either<String, DateTime?>> getLastSyncTime();
}
