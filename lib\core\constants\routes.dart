import 'package:call_detection/core/constants/routes_names.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import '../../features/call_recordings/presentation/pages/call_recordings_path_setters_page.dart';
import '../../features/offline_leads/presentation/pages/leads_list_page.dart';
import '../../features/permissions/presentation/pages/permissions_page.dart';
import '../../features/terms_and_conditions/presentation/pages/terms_and_conditions_page.dart';
import '../../features/terms_and_conditions/data/services/terms_service.dart';
import '../widgets/force_update_checker.dart';
import 'package:permission_handler/permission_handler.dart';

class Routes {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case RoutesName.splash:
        return MaterialPageRoute(
          builder: (BuildContext context) => ForceUpdateChecker(
            child: <PERSON>Builder<Widget>(
              future: _handleInitialNavigation(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Scaffold(
                    body: Center(
                        child:
                            CircularProgressIndicator()), // Show loader while checking
                  );
                }
                if (snapshot.hasData) {
                  return snapshot.data!;
                } else {
                  return const TermsAndConditionsPage(); // Default fallback
                }
              },
            ),
          ),
        );

      case RoutesName.permissions:
        return MaterialPageRoute(
          builder: (BuildContext context) => PermissionsPage(),
        );

      case RoutesName.termsAndConditions:
        return MaterialPageRoute(
          builder: (BuildContext context) => const TermsAndConditionsPage(),
        );

      default:
        return MaterialPageRoute(builder: (_) {
          return const Scaffold(
            body: Center(
              child: Text('No route defined'),
            ),
          );
        });
    }
  }

  static Future<Widget> _handleInitialNavigation() async {
    // First check if user has accepted terms and conditions
    bool hasAcceptedTerms = await TermsService.hasAcceptedTerms();

    if (!hasAcceptedTerms) {
      return const TermsAndConditionsPage();
    }

    // If terms accepted, proceed with existing logic
    bool isRunning = await isServiceRunning();

    // If service is running -> Navigate to LeadsListPage (Dashboard)
    // Otherwise -> Navigate to CallRecordingsPathSetPage (setup)
    if (isRunning) {
      return const LeadsListPage();
    } else {
      return CallRecordingsPathSetPage();
    }
  }

  static Future<bool> isServiceRunning() async {
    const platform = MethodChannel('call_detection_service');
    try {
      final bool isRunning = await platform.invokeMethod('isServiceRunning');
      return isRunning;
    } catch (e, stackTrace) {
      if (kReleaseMode) {
        Sentry.captureException(e, stackTrace: stackTrace);
      } else {
        print('Error checking service status: $e');
      }
      return false;
    }
  }

  static Future<bool> areAllPermissionsAllowed() async {
    // First check critical permissions (phone and call logs)
    if (!await Permission.phone.isGranted) {
      return false;
    }

    // Check call logs permission using platform channel
    try {
      const platform = MethodChannel('call_detection_service');
      final bool hasCallLogPermission =
          await platform.invokeMethod('checkPhoneAndCallLogPermissions');
      if (!hasCallLogPermission) {
        return false;
      }
    } catch (e, stackTrace) {
      // If we can't check call logs permission, continue with other permissions
      if (kReleaseMode) {
        Sentry.captureException(e, stackTrace: stackTrace);
      } else {
        print('Error checking call log permission: $e');
      }
    }

    // Check all other permissions
    return await Permission.ignoreBatteryOptimizations.isGranted &&
        await Permission.systemAlertWindow.isGranted &&
        await Permission.notification.isGranted &&
        await Permission.microphone.isGranted &&
        await Permission.manageExternalStorage.isGranted &&
        await Permission.audio.isGranted;
  }
}
