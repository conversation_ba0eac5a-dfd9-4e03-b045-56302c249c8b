import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:meta/meta.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import '../../../../core/enums/page_state_enum.dart';
import '../items/permission_item.dart';

part 'permissions_event.dart';
part 'permissions_state.dart';

class PermissionsBloc extends Bloc<PermissionsEvent, PermissionsState> {
  List<PermissionItem>? permissionsEvent;
  PermissionsBloc() : super(PermissionsState()) {
    on<InitPermissionsEvent>(_initPermissionsEvent);
    on<RequestPermissionsEvent>(_onRequestPermissions);
    on<NavigateToCallDetectionPageEvent>(_navigateToCallDetectionPageEvent);
  }

  FutureOr<void> _initPermissionsEvent(InitPermissionsEvent event, Emitter<PermissionsState> emit) async {
    try {
      permissionsEvent = [
        PermissionItem(
          title: "Phone State",
          description: "Required to detect the phone’s SIM and call state. This helps ensure your registered phone number matches your device’s number.",
          permission: Permission.phone,
          icon: Icons.phone_android_rounded,
          isPermissionAllowed: await Permission.phone.isGranted,
        ),
        PermissionItem(
          title: "Battery Optimization",
          description: "Required to keep the call detection service running in the background without being killed by the system.",
          permission: Permission.ignoreBatteryOptimizations,
          icon: Icons.battery_saver,
          isPermissionAllowed: await Permission.ignoreBatteryOptimizations.isGranted,
        ),
        PermissionItem(
          title: "Display Over Other Apps",
          description: "Required to show call overlay when receiving or making calls.",
          permission: Permission.systemAlertWindow,
          icon: Icons.layers,
          isPermissionAllowed: await Permission.systemAlertWindow.isGranted,
        ),
        PermissionItem(
          title: "Notifications",
          description: "Required to show notifications for call detection service status and updates.",
          permission: Permission.notification,
          icon: Icons.notifications,
          isPermissionAllowed: await Permission.notification.isGranted,
        ),
        PermissionItem(
          title: "Contacts",
          description: "Required to access call logs and identify callers from your contacts.",
          permission: Permission.contacts,
          icon: Icons.contacts,
          isPermissionAllowed: await Permission.contacts.isGranted,
        ),

        PermissionItem(
          title: "Microphone",
          description: "Required for call recording functionality.",
          permission: Permission.microphone,
          icon: Icons.mic,
          isPermissionAllowed: await Permission.microphone.isGranted,
        ),
        PermissionItem(
          title: "Audio Recording",
          description: "Required for recording audio during calls.",
          permission: Permission.audio,
          icon: Icons.audiotrack,
          isPermissionAllowed: await Permission.audio.isGranted,
        ),
        PermissionItem(
          title: "Storage Access",
          description: "Required to save call recordings and access external storage.",
          permission: Permission.manageExternalStorage,
          icon: Icons.storage,
          isPermissionAllowed: await Permission.manageExternalStorage.isGranted,
        ),
      ];

      emit(state.copyWith(permissionsList: permissionsEvent));
    } catch (e, stackTrace) {
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
      );
    }
  }

  FutureOr<void> _onRequestPermissions(RequestPermissionsEvent event, Emitter<PermissionsState> emit) async{
    try {
      if (event.permission != null) {
        await _handlePermissionRequest(event.permission!, emit);
      } else {
        emit(state.copyWith(
          pageState: PageState.failure,
          message: "Invalid permission request"
        ));
      }
    } catch (e, stackTrace) {
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
      );
    }
  }

  /// Generic method to handle permission requests for any permission
  Future<void> _handlePermissionRequest(Permission permission, Emitter<PermissionsState> emit) async {
    String permissionName = _getPermissionDisplayName(permission);

    if (await permission.isGranted) {
      emit(state.copyWith(
        pageState: PageState.success,
        message: "The $permissionName permission is already granted."
      ));
      return;
    }

    if (await permission.isPermanentlyDenied) {
      emit(state.copyWith(
        pageState: PageState.failure,
        message: "The $permissionName permission is permanently denied. Please go to Settings > Apps > Call Detection > Permissions to enable it."
      ));
      return;
    }

    // Special handling for certain permissions that need to open settings
    if (permission == Permission.ignoreBatteryOptimizations ||
        permission == Permission.systemAlertWindow ||
        permission == Permission.manageExternalStorage) {
      await openAppSettings();
      emit(state.copyWith(
        pageState: PageState.success,
        message: "Please enable $permissionName in the settings that just opened, then return to the app."
      ));
      return;
    }

    // Request the permission
    var response = await permission.request();

    // Find and update the permission in the list
    int index = permissionsEvent?.indexWhere((p) => p.permission == permission) ?? -1;

    if (response == PermissionStatus.granted) {
      if (index != -1) {
        permissionsEvent?[index] = permissionsEvent![index].copyWith(isPermissionAllowed: true);
      }
      emit(state.copyWith(
        pageState: PageState.success,
        permissionsList: List.from(permissionsEvent!),
        message: "Successfully granted the $permissionName permission",
      ));
    } else if (response == PermissionStatus.denied) {
      if (index != -1) {
        permissionsEvent?[index] = permissionsEvent![index].copyWith(isPermissionAllowed: false);
      }
      emit(state.copyWith(
        pageState: PageState.failure,
        permissionsList: List.from(permissionsEvent!),
        message: "Please allow the $permissionName permission to continue"
      ));
    } else if (response == PermissionStatus.permanentlyDenied) {
      if (index != -1) {
        permissionsEvent?[index] = permissionsEvent![index].copyWith(isPermissionAllowed: false);
      }
      emit(state.copyWith(
        pageState: PageState.failure,
        permissionsList: List.from(permissionsEvent!),
        message: "The $permissionName permission is permanently denied. Please go to Settings > Apps > Call Detection > Permissions to enable it."
      ));
    }
  }

  /// Get user-friendly permission name
  String _getPermissionDisplayName(Permission permission) {
    switch (permission) {
      case Permission.phone:
        return "Phone State";
      case Permission.ignoreBatteryOptimizations:
        return "Battery Optimization";
      case Permission.systemAlertWindow:
        return "Display Over Other Apps";
      case Permission.notification:
        return "Notifications";
      case Permission.contacts:
        return "Contacts";

      case Permission.microphone:
        return "Microphone";
      case Permission.audio:
        return "Audio Recording";
      case Permission.manageExternalStorage:
        return "Storage Access";
      default:
        return "Permission";
    }
  }

  FutureOr<void> _navigateToCallDetectionPageEvent(NavigateToCallDetectionPageEvent event, Emitter<PermissionsState> emit) async {
    // if (!await Permission.phone.isGranted) {
    //   emit(state.copyWith(pageState: PageState.error, message: "Kindly allow the phone permission"));
    // } else if (!await Permission.ignoreBatteryOptimizations.isGranted) {
    //   emit(state.copyWith(pageState: PageState.error, message: "Kindly allow the ignoreBatteryOptimizations permission"));
    // } else if (!await Permission.notification.isGranted) {
    //   emit(state.copyWith(pageState: PageState.error, message: "Kindly allow the notification permission"));
    // } else if (!await Permission.location.isGranted) {
    //   emit(state.copyWith(pageState: PageState.error, message: "Kindly allow the location permission"));
    // } else if (!await Permission.contacts.isGranted) {
    //   emit(state.copyWith(pageState: PageState.error, message: "Kindly allow the contacts permission"));
    // } else if (!await Permission.microphone.isGranted) {
    //   emit(state.copyWith(pageState: PageState.error, message: "Kindly allow the microphone permission"));
    // } else if (!await Permission.manageExternalStorage.isGranted) {
    //   emit(state.copyWith(pageState: PageState.error, message: "Kindly allow the manageExternalStorage permission"));
    // } else {
    //   emit(state.copyWith(pageState: PageState.loading));
    // }

    emit(state.copyWith(pageState: PageState.loading));
  }

}
