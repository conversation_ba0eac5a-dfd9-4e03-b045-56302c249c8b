import '../../../../core/utils/preference_helper.dart';

class TermsService {
  /// Check if user has accepted terms and conditions
  static Future<bool> hasAcceptedTerms() async {
    return await PreferencesHelper.getTermsAccepted();
  }

  /// Mark terms and conditions as accepted
  static Future<void> acceptTerms() async {
    await PreferencesHelper.setTermsAccepted(true);
  }

  /// Reset terms acceptance (for testing or app reset)
  static Future<void> resetTermsAcceptance() async {
    await PreferencesHelper.setTermsAccepted(false);
  }
}
