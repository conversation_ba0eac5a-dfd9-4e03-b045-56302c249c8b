import 'package:call_detection/features/call_recordings/data/models/lead_call_log_model.dart';
import 'package:call_detection/features/call_recordings/data/models/lead_found_model.dart';
import 'package:call_detection/features/call_recordings/data/models/response.dart';

import '../../data/models/is_lead_or_data_model.dart';
import 'package:fpdart/fpdart.dart';

abstract class CallLogRepository {
  Future<Either<String, IsLeadOrDataModel>> isLeadOrData(String contactNo);
  Future<Either<String, LeadFoundModel>> findLeadByContactNo(String contactNo, String userId);
  Future<Either<String, Response>> addCallLog(LeadCallLogModel callLog);
}