import 'package:call_detection/features/call_recordings/data/models/lead_status_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'lead_found_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: true)
class LeadFoundModel {
  final LeadStatusModel? status;
  final LeadStatusModel? baseStatus;

  LeadFoundModel({this.status, this.baseStatus});

  factory LeadFoundModel.fromJson(Map<String, dynamic> json) =>
      _$LeadFoundModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadFoundModelToJson(this);
}