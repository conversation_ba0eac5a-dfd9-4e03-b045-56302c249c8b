# Force Update Feature Setup Guide

This guide explains how to configure and use the in-app force update feature that checks version requirements from AWS Secrets Manager.

## Overview

The force update feature works as follows:
1. When the app starts, it checks the current app version against a force update version stored in AWS Secrets Manager
2. If the current version is lower than the required version, a mandatory update dialog is shown
3. Users cannot proceed without updating the app
4. Clicking "Update Now" opens the default browser with the specified update URL

## Configuration

### 1. AWS Secrets Manager Setup

You need to configure AWS Secrets Manager with the following secret:

**Secret Name:** `LeadratServicesForceUpdate`

The secret value should be a JSON object with platform-specific versions:
```json
{
  "AndroidLatestVersion": "1.0.1.0",
  "IOSLatestVersion": "1.0.1.0",
  "AndroidForceUpdateVersion": "1.0.1.1",
  "IOSForceUpdateVersion": "1.0.1.1",
  "IsUnderMaintenance": "false"
}
```

**Note:** Based on your requirements, you mentioned using:
- **Secret Name:** `LeadratServicesForceUpdate`
- **Secret Value Key:** `LeadratServicesForceUpdateVersion`
- **Sample Value:** `1.0.1.1`

If you want to use a simpler format, you can modify the AWS secret to:
```json
{
  "LeadratServicesForceUpdateVersion": "1.0.1.1"
}
```

### 2. App Configuration

Update the AWS configuration in `lib/core/config/aws_config.dart`:

```dart
class AwsConfig {
  static const String region = 'us-east-1'; // Your AWS region
  static const String accessKey = 'your-aws-access-key';
  static const String secretKey = 'your-aws-secret-key';
}
```

**Important:** Replace the placeholder values with your actual AWS credentials.

### 3. Update URL Configuration

The update URL is currently set to:
`https://dev.azure.com/gharoffice/Leadrat-Black/_git/leadrat-calldetection-flutter`

To change this URL, modify the `_openUpdateUrl()` method in `lib/core/widgets/force_update_dialog.dart`.

## How It Works

### Version Comparison Logic

The system compares versions using semantic versioning:
- Current app version is read from `pubspec.yaml` using `package_info_plus`
- Force update version is fetched from AWS Secrets Manager
- The system automatically detects the platform (Android/iOS) and uses the appropriate version
- Versions are compared part by part (major.minor.patch.build)
- If current version < force update version, the update dialog is shown

### App Flow Integration

The force update check is integrated into the app's splash screen:
1. `ForceUpdateChecker` widget wraps the main app content
2. On app start, it checks for updates before showing any other content
3. If update is required, shows the force update dialog
4. If no update is required, proceeds with normal app flow

## Production Ready

### Current Setup Status
- ✅ **App Version**: 1.0.4 (pubspec.yaml)
- ✅ **AWS Secret**: 1.0.5 (LeadratServicesForceUpdate)
- ✅ **Expected Result**: Force update dialog will show

### Running the App
1. **Build and Run**: `flutter run --debug`
2. **Expected Behavior**: Beautiful force update dialog appears immediately
3. **Dialog Content**: Shows current version (1.0.4) vs required (1.0.5)
4. **Update Action**: Opens Azure DevOps URL in browser

## File Structure

```
lib/
├── core/
│   ├── config/
│   │   └── aws_config.dart                   # AWS configuration
│   ├── services/
│   │   ├── secret_manager_service/
│   │   │   ├── secret_manager_service.dart   # AWS Secrets Manager interface
│   │   │   ├── secret_manager_service_impl.dart # AWS implementation
│   │   │   └── secret_manager_local_service.dart # Local storage service
│   │   └── force_update_service/
│   │       ├── force_update_service.dart     # Service interface
│   │       └── force_update_service_impl.dart # Production implementation
│   ├── models/
│   │   └── force_update_model.dart           # Force update data model
│   └── widgets/
│       ├── force_update_checker.dart         # Main checker widget
│       └── force_update_dialog.dart          # Update dialog UI
```

## Dependencies

The following packages are required:
- `url_launcher: ^6.2.4` - For opening browser
- `package_info_plus: ^8.3.0` - For getting app version
- `http: ^1.1.0` - For Azure Key Vault API calls

## Security Considerations

1. **AWS Credentials Security:** Store AWS access keys and secret keys securely, consider using environment variables or IAM roles
2. **Network Security:** Ensure HTTPS is used for all AWS Secrets Manager communications
3. **Error Handling:** The system gracefully handles network errors and doesn't block app startup
4. **Least Privilege:** Use IAM policies to grant minimal required permissions for Secrets Manager access

## Troubleshooting

### Common Issues

1. **Dialog not showing:** Check AWS Secrets Manager configuration and network connectivity
2. **Version comparison issues:** Ensure version format is consistent (e.g., 1.0.1.1)
3. **Browser not opening:** Verify URL format and device browser availability
4. **AWS Authentication errors:** Verify access key, secret key, and region configuration

### Debug Mode

Add debug prints to track the force update process:
- Current app version
- Fetched force update version from AWS
- Platform detection (Android/iOS)
- Comparison result
- Network request status

## Production Deployment

Before deploying to production:
1. Replace all placeholder values in `aws_config.dart`
2. Test with actual AWS Secrets Manager setup
3. Verify update URL points to correct app store/distribution platform
4. Test version comparison logic with various version formats
5. Set up proper IAM permissions for AWS Secrets Manager access
