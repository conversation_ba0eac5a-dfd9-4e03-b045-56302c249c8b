import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:fpdart/fpdart.dart';
import 'package:http/http.dart' as http;

import '../../../../core/enums/page_state_enum.dart';
import '../../data/models/offline_lead_model.dart';
import '../../data/services/background_sync_service.dart';
import '../../data/datasources/offline_leads_local_datasource.dart';
import '../../data/datasources/offline_leads_remote_datasource.dart';
import '../../data/repository/offline_leads_repository_impl.dart';
import '../../domain/usecases/sync_leads_usecase.dart';

part 'offline_leads_event.dart';
part 'offline_leads_state.dart';

class OfflineLeadsBloc extends Bloc<OfflineLeadsEvent, OfflineLeadsState> {
  final SyncLeadsUseCase syncLeadsUseCase;
  final GetAllLeadsUseCase getAllLeadsUseCase;
  final GetLeadByIdUseCase getLeadByIdUseCase;
  final DeleteLeadUseCase deleteLeadUseCase;
  final GetLeadsCountUseCase getLeadsCountUseCase;
  final GetLastSyncTimeUseCase getLastSyncTimeUseCase;
  final GetHiveNativeDbCountUseCase getHiveNativeDbCountUseCase;

  Timer? _syncStatusTimer;

  OfflineLeadsBloc({
    required this.syncLeadsUseCase,
    required this.getAllLeadsUseCase,
    required this.getLeadByIdUseCase,
    required this.deleteLeadUseCase,
    required this.getLeadsCountUseCase,
    required this.getLastSyncTimeUseCase,
    required this.getHiveNativeDbCountUseCase,
  }) : super(const OfflineLeadsState()) {
    on<InitializeOfflineLeadsEvent>(_onInitializeOfflineLeads);
    on<SyncLeadsEvent>(_onSyncLeads);
    on<GetAllLeadsEvent>(_onGetAllLeads);
    on<GetLeadByIdEvent>(_onGetLeadById);
    on<DeleteLeadEvent>(_onDeleteLead);
    on<DeleteAllLeadsEvent>(_onDeleteAllLeads);
    on<StartBackgroundSyncEvent>(_onStartBackgroundSync);
    on<StopBackgroundSyncEvent>(_onStopBackgroundSync);
    on<GetSyncStatusEvent>(_onGetSyncStatus);
    on<RefreshLeadsEvent>(_onRefreshLeads);
    on<SearchLeadsEvent>(_onSearchLeads);
    on<FilterLeadsEvent>(_onFilterLeads);
    on<CompleteDatabaseRefreshEvent>(_onCompleteDatabaseRefresh);

    // Start periodic sync status updates
    _startSyncStatusTimer();
  }

  void _startSyncStatusTimer() {
    _syncStatusTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      add(GetSyncStatusEvent());
    });
  }

  @override
  Future<void> close() {
    _syncStatusTimer?.cancel();
    return super.close();
  }

  Future<void> _onInitializeOfflineLeads(
    InitializeOfflineLeadsEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    print('🚀 [BLOC] === INITIALIZE OFFLINE LEADS EVENT STARTED ===');
    emit(state.copyWith(isLoading: true, pageState: PageState.loading));

    try {
      print('🔄 [BLOC] Starting initialization process...');
      // Initialize background sync service
      await BackgroundSyncService.initialize();

      // Get all leads from local database
      final leadsResult = await getAllLeadsUseCase();
      final lastSyncResult = await getLastSyncTimeUseCase();
      final countResult = await getLeadsCountUseCase();

      print('🔍 [BLOC] About to call getHiveNativeDbCountUseCase...');
      final hiveNativeCountResult = await getHiveNativeDbCountUseCase();
      print('🔍 [BLOC] getHiveNativeDbCountUseCase call completed');

      leadsResult.fold(
        (error) => emit(state.copyWith(
          pageState: PageState.failure,
          errorMessage: error,
          isLoading: false,
        )),
        (leads) {
          final lastSyncTime = lastSyncResult.fold((l) => null, (r) => r);
          final count = countResult.fold((l) => 0, (r) => r);
          final hiveNativeCount = hiveNativeCountResult.fold((l) {
            print('❌ [BLOC] Error getting Hive Native DB count: $l');
            return 0;
          }, (r) {
            print('✅ [BLOC] Successfully got Hive Native DB count: $r');
            return r;
          });

          emit(state.copyWith(
            pageState: PageState.success,
            leads: leads,
            filteredLeads: leads,
            lastSyncTime: lastSyncTime,
            totalLeadsCount: count,
            hiveNativeDbCount: hiveNativeCount,
            isLoading: false,
          ));
        },
      );
    } catch (e) {
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Failed to initialize: $e',
        isLoading: false,
      ));
    }
  }

  Future<void> _onSyncLeads(
    SyncLeadsEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    print('🔄 [BLOC] Manual sync triggered by user');
    emit(state.copyWith(isSyncing: true));

    print('🔄 [BLOC] Executing sync leads use case...');
    final result = await syncLeadsUseCase();

    result.fold(
      (error) {
        print('❌ [BLOC] Sync failed: $error');
        emit(state.copyWith(
          pageState: PageState.failure,
          errorMessage: error,
          isSyncing: false,
        ));
      },
      (leads) async {
        print('✅ [BLOC] Sync successful: ${leads.length} leads received');

        // Get updated Hive Native DB count after sync
        final hiveNativeCountResult = await getHiveNativeDbCountUseCase();
        final hiveNativeCount = hiveNativeCountResult.fold((l) {
          print('❌ [BLOC] Error getting Hive Native DB count after sync: $l');
          return 0;
        }, (r) {
          print(
              '✅ [BLOC] Successfully got Hive Native DB count after sync: $r');
          return r;
        });

        emit(state.copyWith(
          pageState: PageState.success,
          leads: leads,
          filteredLeads: _applyFilters(leads),
          lastSyncTime: DateTime.now(),
          totalLeadsCount: leads.length,
          hiveNativeDbCount: hiveNativeCount,
          successMessage: 'Sync completed successfully',
          isSyncing: false,
        ));
      },
    );
  }

  Future<void> _onGetAllLeads(
    GetAllLeadsEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    emit(state.copyWith(isLoading: true));

    final result = await getAllLeadsUseCase();

    result.fold(
      (error) => emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: error,
        isLoading: false,
      )),
      (leads) => emit(state.copyWith(
        pageState: PageState.success,
        leads: leads,
        filteredLeads: _applyFilters(leads),
        totalLeadsCount: leads.length,
        isLoading: false,
      )),
    );
  }

  Future<void> _onGetLeadById(
    GetLeadByIdEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    final result = await getLeadByIdUseCase(event.id);

    result.fold(
      (error) => emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: error,
      )),
      (lead) => emit(state.copyWith(
        selectedLead: lead,
        pageState: PageState.success,
      )),
    );
  }

  Future<void> _onDeleteLead(
    DeleteLeadEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    final result = await deleteLeadUseCase(event.id);

    result.fold(
      (error) => emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: error,
      )),
      (_) {
        // Remove lead from current list
        final updatedLeads =
            state.leads.where((lead) => lead.id != event.id).toList();
        emit(state.copyWith(
          leads: updatedLeads,
          filteredLeads: _applyFilters(updatedLeads),
          totalLeadsCount: updatedLeads.length,
          successMessage: 'Lead deleted successfully',
          pageState: PageState.success,
        ));
      },
    );
  }

  Future<void> _onDeleteAllLeads(
    DeleteAllLeadsEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    // Implementation for delete all leads
    emit(state.copyWith(
      leads: [],
      filteredLeads: [],
      totalLeadsCount: 0,
      successMessage: 'All leads deleted successfully',
      pageState: PageState.success,
    ));
  }

  Future<void> _onStartBackgroundSync(
    StartBackgroundSyncEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    try {
      print('🚀 [BLOC] Starting background sync service...');
      await BackgroundSyncService.startPeriodicSync();
      print('✅ [BLOC] Background sync service started successfully');
      emit(state.copyWith(
        isBackgroundSyncRunning: true,
        successMessage: 'Background sync started',
        pageState: PageState.success,
      ));
    } catch (e) {
      print('❌ [BLOC] Failed to start background sync: $e');
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Failed to start background sync: $e',
      ));
    }
  }

  Future<void> _onStopBackgroundSync(
    StopBackgroundSyncEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    try {
      print('🛑 [BLOC] Stopping background sync service...');
      await BackgroundSyncService.stopPeriodicSync();
      print('✅ [BLOC] Background sync service stopped successfully');
      emit(state.copyWith(
        isBackgroundSyncRunning: false,
        successMessage: 'Background sync stopped',
        pageState: PageState.success,
      ));
    } catch (e) {
      print('❌ [BLOC] Failed to stop background sync: $e');
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Failed to stop background sync: $e',
      ));
    }
  }

  Future<void> _onGetSyncStatus(
    GetSyncStatusEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    try {
      // Get last sync time
      final lastSyncResult = await getLastSyncTimeUseCase();
      final lastSyncTime = lastSyncResult.fold((l) => null, (r) => r);

      // Get updated Hive Native DB count
      final hiveNativeCountResult = await getHiveNativeDbCountUseCase();
      final hiveNativeCount = hiveNativeCountResult.fold((l) {
        print(
            '❌ [BLOC] Error getting Hive Native DB count during status update: $l');
        return state.hiveNativeDbCount; // Keep current value if error
      }, (r) {
        // Only log if the count has changed to avoid spam
        if (r != state.hiveNativeDbCount) {
          print(
              '🔄 [BLOC] Hive Native DB count updated: ${state.hiveNativeDbCount} → $r');
        }
        return r;
      });

      emit(state.copyWith(
        lastSyncTime: lastSyncTime,
        hiveNativeDbCount: hiveNativeCount,
      ));
    } catch (e) {
      print('❌ [BLOC] Error during sync status update: $e');
      // Don't emit error state for periodic updates, just log the error
    }
  }

  Future<void> _onRefreshLeads(
    RefreshLeadsEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    add(SyncLeadsEvent());
  }

  Future<void> _onSearchLeads(
    SearchLeadsEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    emit(state.copyWith(
      searchQuery: event.query,
      filteredLeads: _applyFilters(state.leads),
    ));
  }

  Future<void> _onFilterLeads(
    FilterLeadsEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    emit(state.copyWith(
      statusFilter: event.status,
      sourceFilter: event.source,
      assignedToFilter: event.assignedTo,
      filteredLeads: _applyFilters(state.leads),
    ));
  }

  List<OfflineLeadModel> _applyFilters(List<OfflineLeadModel> leads) {
    var filtered = leads;

    // Apply search filter
    if (state.searchQuery != null && state.searchQuery!.isNotEmpty) {
      final query = state.searchQuery!.toLowerCase();
      filtered = filtered.where((lead) {
        return (lead.name?.toLowerCase().contains(query) ?? false) ||
            (lead.contactNo?.toLowerCase().contains(query) ?? false) ||
            (lead.alternateContactNo?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // Apply assigned to filter
    if (state.assignedToFilter != null && state.assignedToFilter!.isNotEmpty) {
      filtered = filtered
          .where((lead) => lead.assignTo == state.assignedToFilter)
          .toList();
    }

    // Apply deleted and archived filter (show only non-deleted and non-archived leads by default)
    filtered =
        filtered.where((lead) => !lead.isDeleted && !lead.isArchived).toList();

    return filtered;
  }

  Future<void> _onCompleteDatabaseRefresh(
    CompleteDatabaseRefreshEvent event,
    Emitter<OfflineLeadsState> emit,
  ) async {
    print('🔄 [BLOC] Complete database refresh triggered by user');

    try {
      emit(state.copyWith(
        isCompleteRefreshing: true,
        refreshProgress: 'Syncing the leads....',
        pageState: PageState.loading,
      ));

      // Step 1: Stop all background sync services
      print('🛑 [BLOC] Stopping background sync services...');
      await BackgroundSyncService.stopPeriodicSync();

      emit(state.copyWith(
        refreshProgress: 'Clearing Hive database...',
      ));

      // Step 2: Clear Hive database (Flutter)
      print('🗑️ [BLOC] Clearing Hive database...');
      await BackgroundSyncService.clearDatabase();

      emit(state.copyWith(
        refreshProgress: 'Clearing native databases...',
      ));

      // Step 3: Clear native SQLite database
      print('🗑️ [BLOC] Clearing native SQLite database...');
      try {
        const platform = MethodChannel('data_sync_service');
        await platform.invokeMethod('clearNativeLeadsData');
        print('✅ [BLOC] Native SQLite database cleared successfully');
      } catch (e) {
        print('⚠️ [BLOC] Failed to clear native database: $e');
        // Continue with the process even if this fails
      }

      // Step 4: Clear Hive Native database (call detection database)
      print('🗑️ [BLOC] Clearing Hive Native database...');
      try {
        const platform = MethodChannel('call_detection_service');
        await platform.invokeMethod('clearHiveNativeDatabase');
        print('✅ [BLOC] Hive Native database cleared successfully');
      } catch (e) {
        print('⚠️ [BLOC] Failed to clear Hive Native database: $e');
        // Continue with the process even if this fails
      }

      emit(state.copyWith(
        refreshProgress: 'Clearing sync timestamps...',
      ));

      // Step 5: Clear all sync timestamps to force full reload
      print('🔄 [BLOC] Clearing sync timestamps...');
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('last_sync_time');
      await prefs.remove('native_db_synced');
      await prefs.remove('native_db_sync_time');
      await prefs.remove('should_clear_hive_for_settings_change');

      // Clear the sync time from Hive sync box to ensure full sync
      try {
        await BackgroundSyncService.clearSyncTime();
        print('✅ [BLOC] Sync timestamps cleared successfully');
      } catch (e) {
        print('⚠️ [BLOC] Failed to clear sync timestamps: $e');
        // Continue with the process even if this fails
      }

      emit(state.copyWith(
        refreshProgress: 'Performing full sync...',
      ));

      // Step 6: Perform full sync from APIs with fresh repository
      print('🔄 [BLOC] Starting full sync from APIs...');
      final result = await _performFullSyncWithFreshRepository();

      result.fold(
        (error) {
          print('❌ [BLOC] Complete refresh failed during sync: $error');
          emit(state.copyWith(
            pageState: PageState.failure,
            errorMessage: 'Complete refresh failed: $error',
            isCompleteRefreshing: false,
            refreshProgress: null,
          ));
        },
        (leads) async {
          print('✅ [BLOC] Full sync completed: ${leads.length} leads received');

          // Emit final success state immediately
          emit(state.copyWith(
            pageState: PageState.success,
            leads: leads,
            filteredLeads: _applyFilters(leads),
            lastSyncTime: DateTime.now(),
            totalLeadsCount: leads.length,
            successMessage: 'Complete database refresh completed successfully',
            isCompleteRefreshing: false,
            refreshProgress: null,
            isBackgroundSyncRunning:
                false, // Will be updated after background service starts
          ));

          // Step 7: Restart background sync services (without emitting)
          await _restartBackgroundServicesWithoutEmit(leads);
        },
      );
    } catch (e) {
      print('❌ [BLOC] Complete database refresh failed: $e');
      emit(state.copyWith(
        pageState: PageState.failure,
        errorMessage: 'Complete refresh failed: ${e.toString()}',
        isCompleteRefreshing: false,
        refreshProgress: null,
      ));

      // Try to restart background services even if refresh failed
      try {
        await BackgroundSyncService.startPeriodicSync();
      } catch (restartError) {
        print('❌ [BLOC] Failed to restart background services: $restartError');
      }
    }
  }

  Future<void> _restartBackgroundServicesWithoutEmit(
      List<OfflineLeadModel> leads) async {
    try {
      print('🚀 [BLOC] Restarting background sync services...');
      await BackgroundSyncService.startPeriodicSync();
      print('✅ [BLOC] Background sync services restarted successfully');
    } catch (e) {
      print('❌ [BLOC] Failed to restart background services: $e');
      // Continue without throwing - the main sync was successful
    }
  }

  /// Perform full sync with a fresh repository that has proper User ID and Tenant ID
  Future<Either<String, List<OfflineLeadModel>>>
      _performFullSyncWithFreshRepository() async {
    try {
      print(
          '🔄 [BLOC] Creating fresh repository with User ID and Tenant ID from SharedPreferences...');

      // Get User ID and Tenant ID from Android native SharedPreferences
      String? tenantId;
      String? userId;

      // First try to get from Android native SharedPreferences via method channel
      try {
        print(
            '🔧 [BLOC] Attempting to get credentials from Android native SharedPreferences...');
        const platform = MethodChannel('data_sync_service');

        // Get tenant ID from Android
        try {
          tenantId = await platform.invokeMethod('getTenantId');
          if (tenantId != null && tenantId.isNotEmpty && tenantId != 'null') {
            print(
                '🔧 [BLOC] Found tenant ID from Android native SharedPreferences: $tenantId');
          } else {
            print(
                '⚠️ [BLOC] Tenant ID from Android is null or empty: $tenantId');
            tenantId = null;
          }
        } catch (e) {
          print('⚠️ [BLOC] Failed to get tenant ID from Android: $e');
          tenantId = null;
        }

        // Get user ID from Android
        try {
          userId = await platform.invokeMethod('getUserId');
          if (userId != null && userId.isNotEmpty && userId != 'null') {
            print(
                '🔧 [BLOC] Found user ID from Android native SharedPreferences: $userId');
          } else {
            print('⚠️ [BLOC] User ID from Android is null or empty: $userId');
            userId = null;
          }
        } catch (e) {
          print('⚠️ [BLOC] Failed to get user ID from Android: $e');
          userId = null;
        }
      } catch (e) {
        print('❌ [BLOC] Error accessing Android native SharedPreferences: $e');
      }

      // If not found in Android native, try Flutter SharedPreferences as fallback
      if (tenantId == null || userId == null) {
        print('🔧 [BLOC] Falling back to Flutter SharedPreferences...');
        try {
          final prefs = await SharedPreferences.getInstance();

          // List of possible keys for tenant ID
          final tenantKeys = [
            'TenantId',
            'flutter.TenantId',
            'LeadratGlobal_TenantId',
            'flutter.LeadratGlobal_TenantId',
            'domain',
            'flutter.domain'
          ];

          // List of possible keys for user ID
          final userKeys = [
            'UserId',
            'flutter.UserId',
            'LeadratGlobal_UserId',
            'flutter.LeadratGlobal_UserId',
            'user_id',
            'flutter.user_id'
          ];

          // Try to find tenant ID if not found in Android
          if (tenantId == null) {
            for (final key in tenantKeys) {
              final value = prefs.getString(key);
              if (value != null && value.isNotEmpty && value != 'null') {
                tenantId = value;
                print(
                    '🔧 [BLOC] Found tenant ID from Flutter SharedPreferences key "$key": $tenantId');
                break;
              }
            }
          }

          // Try to find user ID if not found in Android
          if (userId == null) {
            for (final key in userKeys) {
              final value = prefs.getString(key);
              if (value != null && value.isNotEmpty && value != 'null') {
                userId = value;
                print(
                    '🔧 [BLOC] Found user ID from Flutter SharedPreferences key "$key": $userId');
                break;
              }
            }
          }

          // Log all available Flutter keys for debugging
          final allKeys = prefs.getKeys();
          print(
              '🔍 [BLOC] Flutter SharedPreferences keys (${allKeys.length} total):');
          for (final key in allKeys) {
            final value = prefs.get(key);
            print('🔍 [BLOC] - $key: $value');
          }
        } catch (e) {
          print('❌ [BLOC] Error accessing Flutter SharedPreferences: $e');
        }
      }

      if (tenantId == null || tenantId.isEmpty || tenantId == 'null') {
        return const Left(
            'Tenant ID is required for full sync but was not found in SharedPreferences. Please ensure you are logged in to LeadRat.');
      }

      if (userId == null || userId.isEmpty || userId == 'null') {
        return const Left(
            'User ID is required for full sync but was not found in SharedPreferences. Please ensure you are logged in to LeadRat.');
      }

      print(
          '✅ [BLOC] Found credentials - TenantId: $tenantId, UserId: $userId');

      // Create fresh data sources with proper credentials
      final localDataSource = OfflineLeadsLocalDataSourceImpl();
      final remoteDataSource = OfflineLeadsRemoteDataSourceImpl(
        httpClient: http.Client(),
        baseUrl: 'https://prd-mobile.leadrat.com',
        tenantId: tenantId,
        userId: userId,
      );

      // Create fresh repository
      final repository = OfflineLeadsRepositoryImpl(
        localDataSource: localDataSource,
        remoteDataSource: remoteDataSource,
      );

      // Perform sync with fresh repository
      print('🔄 [BLOC] Performing sync with fresh repository...');
      return await repository.syncLeads();
    } catch (e) {
      print('❌ [BLOC] Error creating fresh repository: $e');
      return Left('Failed to create fresh repository: ${e.toString()}');
    }
  }
}
