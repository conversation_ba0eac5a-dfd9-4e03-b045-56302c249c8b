part of 'permissions_bloc.dart';

@immutable
class PermissionsState {
  final List<PermissionItem>? permissionsList;
  final PageState pageState;
  final String? message;

  const PermissionsState({this.permissionsList, this.pageState = PageState.initial, this.message});

  PermissionsState copyWith({
    List<PermissionItem>? permissionsList,
    PageState? pageState,
    String? message,
  }) {
    return PermissionsState(
      permissionsList: permissionsList ?? this.permissionsList,
      pageState: pageState ?? this.pageState,
      message: message ?? this.message,
    );
  }
}
