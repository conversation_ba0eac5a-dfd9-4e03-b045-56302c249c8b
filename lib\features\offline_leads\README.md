# Offline Leads Feature

This feature implements a local database with background API synchronization for managing leads offline. The system automatically syncs data every 1 minute in the background, even when the app is killed.

## Architecture

The feature follows Clean Architecture principles with the following layers:

### Data Layer
- **Models**: `OfflineLeadModel`, `LeadsApiResponseModel`
- **Data Sources**:
  - `OfflineLeadsLocalDataSource` - Hive database operations
  - `OfflineLeadsRemoteDataSource` - API calls
- **Repository**: `OfflineLeadsRepositoryImpl` - Coordinates local and remote data

### Domain Layer
- **Repository Interface**: `OfflineLeadsRepository`
- **Use Cases**: Various use cases for CRUD operations and sync

### Presentation Layer
- **BLoC**: `OfflineLeadsBloc` - State management
- **Pages**: `OfflineLeadsPage` - UI for viewing leads

### Background Services
- **BackgroundSyncService** - Handles periodic API calls using WorkManager
- **OfflineLeadsServiceInitializer** - Service initialization and management

## Key Features

### 1. Local Database (Hive)
- Stores leads locally using Hive database
- Automatic data persistence
- Fast local queries and operations
- Type-safe model with Hive adapters

### 2. Background Synchronization
- **Periodic Sync**: Runs every 1 minute using WorkManager
- **Survives App Kill**: Continues running even when app is terminated
- **Network Aware**: Only syncs when network is available
- **Incremental Sync**: Only fetches data updated since last sync

### 3. Data Management
- **Insert/Update Logic**: Checks if lead ID exists, updates if found, inserts if new
- **Conflict Resolution**: Remote data takes precedence over local data
- **Last Sync Tracking**: Tracks when data was last synchronized

### 4. Error Handling
- Network error handling with retry logic
- Graceful degradation when API is unavailable
- User-friendly error messages

## Usage

### 1. Initialize the Service

In your main app initialization:

```dart
import 'package:call_detection/features/offline_leads/data/services/offline_leads_service_initializer.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize dependency injection
  await initDependencies();

  // Initialize offline leads background sync
  await OfflineLeadsServiceInitializer.initialize();

  runApp(MyApp());
}
```

### 2. Using the BLoC

```dart
// Initialize and load leads
context.read<OfflineLeadsBloc>().add(InitializeOfflineLeadsEvent());

// Manual sync
context.read<OfflineLeadsBloc>().add(SyncLeadsEvent());

// Start/stop background sync
context.read<OfflineLeadsBloc>().add(StartBackgroundSyncEvent());
context.read<OfflineLeadsBloc>().add(StopBackgroundSyncEvent());

// Search and filter
context.read<OfflineLeadsBloc>().add(SearchLeadsEvent("query"));
context.read<OfflineLeadsBloc>().add(FilterLeadsEvent(status: "new"));
```

### 3. Accessing the UI

Navigate to the OfflineLeadsPage to view and manage leads:

```dart
Navigator.push(
  context,
  MaterialPageRoute(builder: (context) => const OfflineLeadsPage()),
);
```

## Configuration

### 1. API Endpoint Configuration

Update the base URL and tenant ID in `injection_container.dart`:

```dart
..registerLazySingleton<OfflineLeadsRemoteDataSource>(
  () => OfflineLeadsRemoteDataSourceImpl(
    httpClient: getIt(),
    baseUrl: 'https://your-api-endpoint.com', // Update this
    tenantId: 'your-tenant-id', // Update this
  ),
)
```

### 2. Sync Frequency

To change the sync frequency, modify the duration in `BackgroundSyncService`:

```dart
await Workmanager().registerPeriodicTask(
  uniqueTaskName,
  syncTaskName,
  frequency: const Duration(minutes: 15), // Change this
  // ...
);
```

### 3. Database Configuration

The Hive database is automatically configured. To customize:

- Box names are defined in `OfflineLeadsLocalDataSourceImpl`
- Type IDs are defined in `hive_model_constants.dart`

## API Contract

The remote API should follow this contract:

### Endpoint: GET /api/v1/leads/sync

**Query Parameters:**
- `page` (int): Page number (default: 1)
- `pageSize` (int): Number of items per page (default: 100)
- `lastSyncTime` (ISO 8601 string, optional): Only return leads updated after this time

**Response:**
```json
{
  "success": true,
  "message": "Leads fetched successfully",
  "data": [
    {
      "id": "lead_1",
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "company": "Acme Corp",
      "status": "new",
      "source": "website",
      "assignedTo": "user_1",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "notes": "Interested in product demo",
      "priority": "high",
      "leadValue": 5000.0,
      "tags": ["hot", "enterprise"],
      "customFields": {
        "industry": "technology",
        "employees": 100
      }
    }
  ],
  "totalCount": 1000,
  "page": 1,
  "pageSize": 100,
  "hasNextPage": true,
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## Monitoring and Debugging

### 1. Check Sync Status

```dart
// Get current sync status
context.read<OfflineLeadsBloc>().add(GetSyncStatusEvent());

// Check if background service is running
final isRunning = state.isBackgroundSyncRunning;
final lastSync = state.lastSyncTime;
```

### 2. Debug Logs

In debug mode, the service logs sync operations:

```
Background sync successful: 150 leads synced
Background sync failed: Network error
```

### 3. Manual Operations

```dart
// Perform one-time sync
await OfflineLeadsServiceInitializer.performOneTimeSync();

// Check service status
final isInitialized = OfflineLeadsServiceInitializer.isInitialized;
```

## Dependencies

The feature requires these dependencies (already added to pubspec.yaml):

- `hive` & `hive_flutter` - Local database
- `workmanager` - Background tasks
- `http` - API calls
- `json_annotation` & `json_serializable` - JSON serialization
- `flutter_bloc` - State management
- `fpdart` - Functional programming utilities

## Troubleshooting

### Background Sync Not Working
1. Check if WorkManager is properly initialized
2. Verify network connectivity
3. Check API endpoint configuration
4. Review device battery optimization settings

### Data Not Syncing
1. Verify API response format matches expected contract
2. Check last sync time tracking
3. Review error logs in debug mode
4. Ensure proper permissions for background execution

### Performance Issues
1. Adjust sync frequency if needed
2. Implement pagination for large datasets
3. Consider data cleanup strategies for old records
4. Monitor local database size

## Future Enhancements

1. **Conflict Resolution**: Handle conflicts when local and remote data differ
2. **Offline Mutations**: Support creating/updating leads offline
3. **Selective Sync**: Sync only specific lead categories
4. **Data Compression**: Compress API responses for better performance
5. **Real-time Updates**: WebSocket support for real-time data updates
