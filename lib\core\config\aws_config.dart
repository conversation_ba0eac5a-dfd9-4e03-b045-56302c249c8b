class AwsConfig {
  // TODO: Replace these with your actual AWS configuration
  static const String region = 'ap-south-1'; // Replace with your AWS region
  static const String accessKey = '********************'; // Replace with your AWS access key
  static const String secretKey = 'XcDOxHtp//5J0eHMc/TL1vKL61P4imzQ4cJs/cpi'; // Replace with your AWS secret key
  
  // Alternative: You can also use environment variables
  // static String get region => const String.fromEnvironment('AWS_REGION', defaultValue: 'us-east-1');
  // static String get accessKey => const String.fromEnvironment('AWS_ACCESS_KEY', defaultValue: 'your-aws-access-key');
  // static String get secretKey => const String.fromEnvironment('AWS_SECRET_KEY', defaultValue: 'your-aws-secret-key');
}
