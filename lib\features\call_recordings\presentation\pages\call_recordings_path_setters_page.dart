import 'package:call_detection/core/enums/page_state_enum.dart';
import 'package:call_detection/core/injection_container.dart';
import 'package:call_detection/core/utils/custom_snackbar.dart';
import 'package:call_detection/features/call_recordings/presentation/bloc/call_recordings_bloc/call_recordoings_bloc.dart';
import 'package:call_detection/features/offline_leads/presentation/pages/leads_list_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../widgets/flashing_text_widget.dart';
import '../widgets/path_popup.dart';
import '../widgets/recording_card.dart';

class CallRecordingsPathSetPage extends StatefulWidget {
  const CallRecordingsPathSetPage({super.key});

  @override
  _CallRecordingsPathSetPageState createState() =>
      _CallRecordingsPathSetPageState();
}

class _CallRecordingsPathSetPageState extends State<CallRecordingsPathSetPage> {
  @override
  void initState() {
    super.initState();
    getIt<CallRecordingsBloc>().add(CallRecordingsInitialEvent());
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<CallRecordingsBloc, CallRecordingsState>(
      listener: (context, state) {
        if (state.pageState == PageState.failure && state.message != null) {
          LeadratCustomSnackbar.show(
            context: context,
            type: SnackbarType.error,
            message: state.message ?? 'Something went wrong',
          );
        } else if (state.pageState == PageState.success &&
            state.message != null) {
          LeadratCustomSnackbar.show(
            context: context,
            type: SnackbarType.success,
            message: state.message ?? 'Success',
          );
        }
      },
      builder: (context, state) {
        return SafeArea(
          child: Scaffold(
            body: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  colors: [Color(0XFF292734), Color(0XFF000000)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 20),
                          const Center(
                            child: Text(
                              "Call recording",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                color: Color(0XFFCFD0D0),
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          const SizedBox(height: 20),
                          const Text(
                            "1. Start the call detection service",
                            style: TextStyle(
                              color: Color(0XFFCFD0D0),
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 20),
                          RecordingCard(
                            title: "Call detection",
                            description:
                                "Start the call detection service to track the calls of the leads.",
                            buttonText: state.isServiceRunning ?? false
                                ? "call detection activated"
                                : "start the service",
                            imagePath: 'assets/images/leadrat_icon_black.png',
                            onButtonPressed: () {
                              getIt<CallRecordingsBloc>()
                                  .add(StartCallDetectionEvent());
                            },
                          ),
                          if ((state.isServiceRunning ?? false) &&
                              (state.isCallRecordingEnabled ?? false)) ...[
                            const SizedBox(height: 20),
                            const Text(
                              "2. Setup the call recording",
                              style: TextStyle(
                                color: Color(0XFFCFD0D0),
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 20),

                            /// Google Dialer – show message that call recording is not available
                            if (state.isGoogleDialer ?? false)
                              Container(
                                width: double.infinity,
                                padding: const EdgeInsets.all(16),
                                margin:
                                    const EdgeInsets.symmetric(vertical: 10),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF2A2A2A),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: const Color(0xFF404040),
                                    width: 1,
                                  ),
                                ),
                                child: const Text(
                                  "Call recording is not available for this device",
                                  style: TextStyle(
                                    color: Color(0XFFCFD0D0),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),

                            /// Other Dialers – suggest using inbuilt
                            if (!(state.isGoogleDialer ?? false))
                              RecordingCard(
                                title: "in built call recorder",
                                description:
                                    "Do you want to use the device's inbuilt call recorder?",
                                buttonText: "use in built call recorder",
                                imagePath:
                                    'assets/images/leadrat_icon_black.png',
                                isImageVisible: false,
                                onButtonPressed: () {
                                  getIt<CallRecordingsBloc>()
                                      .add(InBuiltCallRecorderSelectedEvent());
                                },
                              ),
                            const SizedBox(height: 20),

                            /// Inbuilt recorder path setup
                            if (state.openTheInBuiltCallRecorder ?? false) ...[
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                spacing: 10,
                                children: [
                                  RecordingCard(
                                    title: "Setup in built Recording feature",
                                    description:
                                        "To setup the recording feature, kindly provide us the path where the call recordings are stored.",
                                    buttonText: "enter Path manually",
                                    imagePath:
                                        'assets/images/settings_icon.png',
                                    onButtonPressed: () {
                                      showDialog(
                                        context: context,
                                        builder: (context) => PathDialog(
                                            title: "Enter Path",
                                            isSetupMode: false,
                                            controller:
                                                getIt<CallRecordingsBloc>()
                                                    .controller,
                                            onCancel: () =>
                                                Navigator.pop(context),
                                            onConfirm: () {
                                              getIt<CallRecordingsBloc>().add(
                                                  GetCallRecordingsEvent(
                                                      recordingsPath: getIt<
                                                              CallRecordingsBloc>()
                                                          .controller
                                                          .text));
                                              Navigator.pop(context);
                                            }),
                                      );
                                    },
                                  )
                                ],
                              ),
                              const SizedBox(height: 16),
                            ],
                          ],
                          if ((state.isServiceRunning ?? false) &&
                              !(state.isCallRecordingEnabled ?? false))
                            Padding(
                                padding: const EdgeInsets.all(20.0),
                                child:
                                    FlashingText(message: state.message ?? ""))
                        ],
                      ),
                    ),
                  ),

                  /// Finish Button (Conditional)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          if (state.isFinalSetupDone ?? false) {
                            Navigator.pushAndRemoveUntil(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => const LeadsListPage()),
                              (route) => false, // Remove all previous routes
                            );
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: state.isFinalSetupDone ?? false
                              ? const Color(0XFF50BFA8)
                              : const Color(0xFF163D38),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                        child: const Text(
                          "Finish",
                          style: TextStyle(
                            color: Color(0XFFFFFFFF),
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
