import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'offline_lead_model.g.dart';

@HiveType(typeId: 114)
@JsonSerializable()
class OfflineLeadModel extends HiveObject {
  @HiveField(0)
  @<PERSON>son<PERSON><PERSON>(name: 'id')
  final String id;

  @HiveField(1)
  @<PERSON>son<PERSON><PERSON>(name: 'name')
  final String? name;

  @HiveField(2)
  @<PERSON>son<PERSON>ey(name: 'contactNo')
  final String? contactNo;

  @HiveField(3)
  @<PERSON>son<PERSON><PERSON>(name: 'alternateContactNo')
  final String? alternateContactNo;

  @HiveField(4)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'assignTo')
  final String? assignTo;

  @HiveField(5)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'isDeleted')
  final bool isDeleted;

  @HiveField(6)
  @<PERSON>son<PERSON><PERSON>(name: 'isArchived')
  final bool isArchived;

  @HiveField(7)
  @<PERSON><PERSON><PERSON><PERSON>(name: 'lastModifiedOn')
  final DateTime? lastModifiedOn;

  @HiveField(8)
  final DateTime? lastSyncedAt;

  @HiveField(9)
  @Json<PERSON>ey(name: 'isLead')
  final bool isLead;

  OfflineLeadModel({
    required this.id,
    this.name,
    this.contactNo,
    this.alternateContactNo,
    this.assignTo,
    this.isDeleted = false,
    this.isArchived = false,
    this.lastModifiedOn,
    this.lastSyncedAt,
    this.isLead =
        false, // Default to false, will be set explicitly by data source
  });

  factory OfflineLeadModel.fromJson(Map<String, dynamic> json) =>
      _$OfflineLeadModelFromJson(json);

  Map<String, dynamic> toJson() => _$OfflineLeadModelToJson(this);

  OfflineLeadModel copyWith({
    String? id,
    String? name,
    String? contactNo,
    String? alternateContactNo,
    String? assignTo,
    bool? isDeleted,
    bool? isArchived,
    DateTime? lastModifiedOn,
    DateTime? lastSyncedAt,
    bool? isLead,
  }) {
    return OfflineLeadModel(
      id: id ?? this.id,
      name: name ?? this.name,
      contactNo: contactNo ?? this.contactNo,
      alternateContactNo: alternateContactNo ?? this.alternateContactNo,
      assignTo: assignTo ?? this.assignTo,
      isDeleted: isDeleted ?? this.isDeleted,
      isArchived: isArchived ?? this.isArchived,
      lastModifiedOn: lastModifiedOn ?? this.lastModifiedOn,
      lastSyncedAt: lastSyncedAt ?? this.lastSyncedAt,
      isLead: isLead ?? this.isLead,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OfflineLeadModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'OfflineLeadModel(id: $id, name: $name, contactNo: $contactNo, alternateContactNo: $alternateContactNo, assignTo: $assignTo, isDeleted: $isDeleted, isArchived: $isArchived)';
  }
}
