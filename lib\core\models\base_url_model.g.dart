// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_url_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class KeyValueObjAdapter extends TypeAdapter<KeyValueObj> {
  @override
  final int typeId = 112;

  @override
  KeyValueObj read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return KeyValueObj();
  }

  @override
  void write(BinaryWriter writer, KeyValueObj obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is KeyValueObjAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class BaseUrlModelAdapter extends TypeAdapter<BaseUrlModel> {
  @override
  final int typeId = 111;

  @override
  BaseUrlModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return BaseUrlModel();
  }

  @override
  void write(BinaryWriter writer, BaseUrlModel obj) {
    writer.writeByte(0);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BaseUrlModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

KeyValueObj _$KeyValueObjFromJson(Map<String, dynamic> json) => KeyValueObj(
      key: json['key'] as String?,
      value: json['value'] as String?,
    );

Map<String, dynamic> _$KeyValueObjToJson(KeyValueObj instance) =>
    <String, dynamic>{
      'key': instance.key,
      'value': instance.value,
    };
