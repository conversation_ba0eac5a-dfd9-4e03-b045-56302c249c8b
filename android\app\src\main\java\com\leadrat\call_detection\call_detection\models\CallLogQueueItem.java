package com.leadrat.call_detection.call_detection.models;

import com.leadrat.call_detection.call_detection.enums.CallDirection;
import com.leadrat.call_detection.call_detection.enums.CallStatus;

/**
 * Model class for call log queue items that need to be synced to server
 */
public class CallLogQueueItem {
    public String id;                    // Unique identifier for this queue item
    public String phoneNumber;           // Phone number associated with the call
    public String entityId;              // Lead ID or Prospect ID
    public boolean isLead;               // true if this is a lead, false if prospect
    public CallDirection callDirection;  // Direction of the call
    public CallStatus callStatus;        // Status of the call
    public long callStartTime;           // Call start timestamp
    public long callEndTime;             // Call end timestamp
    public double callDuration;          // Call duration in seconds
    public String notes;                 // Additional notes
    public String callRecordingUrl;      // S3 URL of the uploaded recording (if any)
    public String recordingQueueItemId;  // Reference to the recording queue item
    public long createdAt;               // When this queue item was created
    public int retryCount;               // Number of sync attempts
    public long lastRetryAt;             // Last retry timestamp
    public String errorMessage;          // Last error message if sync failed
    public boolean isProcessing;         // Flag to prevent concurrent processing
    
    // Sync status
    public enum SyncStatus {
        PENDING,        // Waiting to be synced
        WAITING_FOR_RECORDING, // Waiting for recording to be uploaded first
        PROCESSING,     // Currently being synced
        COMPLETED,      // Successfully synced
        FAILED,         // Sync failed (will retry)
        ABANDONED       // Too many failures, giving up
    }
    
    public SyncStatus syncStatus;
    
    public CallLogQueueItem() {
        this.id = java.util.UUID.randomUUID().toString();
        this.createdAt = System.currentTimeMillis();
        this.retryCount = 0;
        this.syncStatus = SyncStatus.PENDING;
        this.isProcessing = false;
        this.callRecordingUrl = "";
    }
    
    public CallLogQueueItem(String phoneNumber, String entityId, boolean isLead,
                           CallDirection callDirection, CallStatus callStatus,
                           long callStartTime, long callEndTime, double callDuration,
                           String notes, String recordingQueueItemId) {
        this();
        this.phoneNumber = phoneNumber;
        this.entityId = entityId;
        this.isLead = isLead;
        this.callDirection = callDirection;
        this.callStatus = callStatus;
        this.callStartTime = callStartTime;
        this.callEndTime = callEndTime;
        this.callDuration = callDuration;
        this.notes = notes;
        this.recordingQueueItemId = recordingQueueItemId;
        
        // If there's a recording, wait for it to be uploaded first
        if (recordingQueueItemId != null && !recordingQueueItemId.isEmpty()) {
            this.syncStatus = SyncStatus.WAITING_FOR_RECORDING;
        }
    }
    
    /**
     * Check if this item should be retried
     */
    public boolean shouldRetry() {
        if (syncStatus == SyncStatus.COMPLETED || syncStatus == SyncStatus.ABANDONED) {
            return false;
        }
        
        // Don't retry if currently processing
        if (isProcessing) {
            return false;
        }
        
        // Don't retry if waiting for recording upload
        if (syncStatus == SyncStatus.WAITING_FOR_RECORDING) {
            return false;
        }
        
        // Maximum 5 retry attempts
        if (retryCount >= 5) {
            syncStatus = SyncStatus.ABANDONED;
            return false;
        }
        
        // Exponential backoff: wait 1min, 2min, 4min, 8min, 16min
        long backoffTime = (long) Math.pow(2, retryCount) * 60 * 1000; // in milliseconds
        long timeSinceLastRetry = System.currentTimeMillis() - lastRetryAt;
        
        return timeSinceLastRetry >= backoffTime;
    }
    
    /**
     * Mark as processing
     */
    public void markAsProcessing() {
        this.isProcessing = true;
        this.syncStatus = SyncStatus.PROCESSING;
        this.lastRetryAt = System.currentTimeMillis();
    }
    
    /**
     * Mark as completed
     */
    public void markAsCompleted() {
        this.isProcessing = false;
        this.syncStatus = SyncStatus.COMPLETED;
    }
    
    /**
     * Mark as failed and increment retry count
     */
    public void markAsFailed(String errorMessage) {
        this.isProcessing = false;
        this.syncStatus = SyncStatus.FAILED;
        this.errorMessage = errorMessage;
        this.retryCount++;
        this.lastRetryAt = System.currentTimeMillis();
        
        // Check if we should abandon after too many failures
        if (retryCount >= 5) {
            this.syncStatus = SyncStatus.ABANDONED;
        }
    }
    
    /**
     * Mark as ready to sync (recording upload completed)
     */
    public void markAsReadyToSync(String recordingUrl) {
        this.callRecordingUrl = recordingUrl;
        this.syncStatus = SyncStatus.PENDING;
    }
    
    @Override
    public String toString() {
        return "CallLogQueueItem{" +
                "id='" + id + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", entityId='" + entityId + '\'' +
                ", isLead=" + isLead +
                ", syncStatus=" + syncStatus +
                ", retryCount=" + retryCount +
                ", callDuration=" + callDuration +
                '}';
    }
}
