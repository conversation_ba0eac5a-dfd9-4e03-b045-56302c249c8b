import 'package:fpdart/fpdart.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../domain/repository/offline_leads_repository.dart';
import '../datasources/offline_leads_local_datasource.dart';
import '../datasources/offline_leads_remote_datasource.dart';
import '../models/offline_lead_model.dart';

class OfflineLeadsRepositoryImpl implements OfflineLeadsRepository {
  final OfflineLeadsLocalDataSource localDataSource;
  final OfflineLeadsRemoteDataSource remoteDataSource;

  OfflineLeadsRepositoryImpl({
    required this.localDataSource,
    required this.remoteDataSource,
  });

  @override
  Future<Either<String, List<OfflineLeadModel>>> syncLeads() async {
    try {
      print('🔄 [REPOSITORY] Starting sync leads operation');

      // Set sync in progress flag to prevent infinite loops
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('sync_in_progress', true);
      print('🔄 [REPOSITORY] Set sync_in_progress flag to prevent infinite loops');

      // Get last sync time
      final lastSyncTime = await localDataSource.getLastSyncTime();
      print('🔄 [REPOSITORY] Last sync time: ${lastSyncTime?.toIso8601String() ?? "Never synced"}');

      List<OfflineLeadModel> allLeads;

      // Check if this is first time sync or forced full sync (epoch time = 1970)
      final isFirstTimeSync = lastSyncTime == null;
      final isForcedFullSync = lastSyncTime != null && lastSyncTime.year == 1970;
      final shouldDoFullSync = isFirstTimeSync || isForcedFullSync;

      if (shouldDoFullSync) {
        // First time sync or forced full sync - fetch all leads and prospects using pagination
        print('🔄 [REPOSITORY] ========== FULL SYNC STARTING ==========');
        if (isFirstTimeSync) {
          print('🔄 [REPOSITORY] First time sync - fetching all leads and prospects');
          print('🔄 [REPOSITORY] lastSyncTime is null: ${lastSyncTime == null}');
        } else {
          print('🔄 [REPOSITORY] Forced full sync - fetching all leads and prospects');
          print('🔄 [REPOSITORY] lastSyncTime is epoch (forced): ${lastSyncTime?.toIso8601String()}');
        }

        // Step 0: Fetch global settings first
        print('🔄 [REPOSITORY] ========== STEP 0: FETCHING GLOBAL SETTINGS ==========');
        bool shouldViewOnlyAssigned = false;
        try {
          print('🔄 [REPOSITORY] Calling global settings API...');
          final globalSettings = await (remoteDataSource as OfflineLeadsRemoteDataSourceImpl).fetchGlobalSettings();
          shouldViewOnlyAssigned = globalSettings['shouldViewOnlyAssigned'] ?? false;
          print('✅ [REPOSITORY] Global settings fetched successfully');
          print('🔄 [REPOSITORY] shouldViewOnlyAssigned: $shouldViewOnlyAssigned');
        } catch (e) {
          print('⚠️ [REPOSITORY] Failed to fetch global settings: $e');
          print('🔄 [REPOSITORY] Continuing with shouldViewOnlyAssigned = false (default)');
          shouldViewOnlyAssigned = false;
        }

        // Step 1: Fetch all leads with global settings
        print('🔄 [REPOSITORY] ========== STEP 1: FETCHING LEADS ==========');
        print('🔄 [REPOSITORY] Fetching leads with shouldViewOnlyAssigned: $shouldViewOnlyAssigned...');
        final leads = await _fetchAllLeadsWithPagination(lastSyncTime, shouldViewOnlyAssigned);
        print('🔄 [REPOSITORY] ========== LEADS FETCH COMPLETED ==========');
        print('🔄 [REPOSITORY] Total leads fetched from all pages: ${leads.length}');

        // Then fetch all prospects
        print('🔄 [REPOSITORY] ========== STEP 2: FETCHING PROSPECTS ==========');
        print('🔄 [REPOSITORY] Fetching prospects...');
        print('🔄 [REPOSITORY] About to call _fetchAllProspectsWithPagination...');
        print('🔄 [REPOSITORY] remoteDataSource is null: ${remoteDataSource == null}');
        print('🔄 [REPOSITORY] remoteDataSource type: ${remoteDataSource.runtimeType}');

        List<OfflineLeadModel> prospects = [];
        try {
          print('🔄 [REPOSITORY] *** CALLING _fetchAllProspectsWithPagination ***');
          print('🔄 [REPOSITORY] Calling _fetchAllProspectsWithPagination with lastSyncTime: $lastSyncTime');
          prospects = await _fetchAllProspectsWithPagination(lastSyncTime);
          print('🔄 [REPOSITORY] *** PROSPECT FETCH COMPLETED SUCCESSFULLY ***');
          print('🔄 [REPOSITORY] Total prospects fetched from all pages: ${prospects.length}');
        } catch (e, stackTrace) {
          print('❌ [REPOSITORY] *** ERROR FETCHING PROSPECTS ***');
          print('❌ [REPOSITORY] Error fetching prospects: $e');
          print('❌ [REPOSITORY] Stack trace: $stackTrace');
          print('⚠️ [REPOSITORY] Continuing with leads only due to prospect fetch error');
          // Continue with leads only - don't fail the entire sync
        }

        print('🔄 [REPOSITORY] ========== STEP 3: COMBINING DATA ==========');
        print('🔄 [REPOSITORY] Leads count: ${leads.length}');
        print('🔄 [REPOSITORY] Prospects count: ${prospects.length}');

        // Check for duplicate IDs between leads and prospects
        final leadIds = leads.map((lead) => lead.id).toSet();
        final prospectIds = prospects.map((prospect) => prospect.id).toSet();
        final duplicateIds = leadIds.intersection(prospectIds);

        if (duplicateIds.isNotEmpty) {
          print('⚠️ [REPOSITORY] *** DUPLICATE IDs DETECTED ***');
          print('⚠️ [REPOSITORY] Found ${duplicateIds.length} duplicate IDs between leads and prospects:');
          for (final duplicateId in duplicateIds.take(5)) {
            final leadWithId = leads.firstWhere((lead) => lead.id == duplicateId);
            final prospectWithId = prospects.firstWhere((prospect) => prospect.id == duplicateId);
            print('⚠️ [REPOSITORY] Duplicate ID: $duplicateId');
            print('   - Lead: {name: ${leadWithId.name}, contactNo: ${leadWithId.contactNo}, isLead: ${leadWithId.isLead}}');
            print('   - Prospect: {name: ${prospectWithId.name}, contactNo: ${prospectWithId.contactNo}, isLead: ${prospectWithId.isLead}}');
          }
          if (duplicateIds.length > 5) {
            print('⚠️ [REPOSITORY] ... and ${duplicateIds.length - 5} more duplicates');
          }
        } else {
          print('✅ [REPOSITORY] No duplicate IDs found between leads and prospects');
        }

        // Combine leads and prospects
        allLeads = [...leads, ...prospects];
        print('🔄 [REPOSITORY] Total leads and prospects combined: ${allLeads.length}');
        print('🔄 [REPOSITORY] Combined data breakdown:');
        final leadsInCombined = allLeads.where((item) => item.isLead).length;
        final prospectsInCombined = allLeads.where((item) => !item.isLead).length;
        print('🔄 [REPOSITORY] - Leads in combined: $leadsInCombined');
        print('🔄 [REPOSITORY] - Prospects in combined: $prospectsInCombined');

        // Check for unique IDs in combined data
        final uniqueIds = allLeads.map((item) => item.id).toSet();
        print('🔄 [REPOSITORY] - Unique IDs in combined data: ${uniqueIds.length}');
        if (uniqueIds.length != allLeads.length) {
          print('⚠️ [REPOSITORY] *** DATA LOSS WARNING: ${allLeads.length - uniqueIds.length} records will be lost due to duplicate IDs ***');
        }

        // Update local database with all leads and prospects
        print('🔄 [REPOSITORY] ========== STEP 4: UPDATING DATABASE ==========');
        if (allLeads.isNotEmpty) {
          print('🔄 [REPOSITORY] Updating local database with ${allLeads.length} leads and prospects');
          await localDataSource.insertOrUpdateLeads(allLeads);
          print('🔄 [REPOSITORY] Database update completed');
        } else {
          print('⚠️ [REPOSITORY] No data to insert into database');
        }
      } else {
        // Incremental sync - fetch only changes since last sync for both leads and prospects
        print('🔄 [REPOSITORY] Incremental sync - fetching changes since ${lastSyncTime.toIso8601String()}');

        // Fetch incremental changes for leads
        print('🔄 [REPOSITORY] Fetching incremental lead changes...');
        final leadChanges = await _fetchIncrementalChanges(lastSyncTime);
        print('🔄 [REPOSITORY] Total lead incremental changes fetched: ${leadChanges.length}');

        // Fetch incremental changes for prospects
        print('🔄 [REPOSITORY] Fetching incremental prospect changes...');
        List<OfflineLeadModel> prospectChanges = [];
        try {
          prospectChanges = await _fetchProspectIncrementalChanges(lastSyncTime);
          print('🔄 [REPOSITORY] Total prospect incremental changes fetched: ${prospectChanges.length}');
        } catch (e) {
          print('❌ [REPOSITORY] Error fetching prospect incremental changes: $e');
          print('⚠️ [REPOSITORY] Continuing with lead changes only due to prospect incremental fetch error');
          // Continue with lead changes only - don't fail the entire sync
        }

        // Combine lead and prospect changes
        allLeads = [...leadChanges, ...prospectChanges];
        print('🔄 [REPOSITORY] Total incremental changes combined: ${allLeads.length}');

        // Process incremental changes (insert/update/delete)
        if (allLeads.isNotEmpty) {
          print('🔄 [REPOSITORY] Processing incremental changes with ${allLeads.length} leads and prospects');
          await localDataSource.processIncrementalChanges(allLeads);
        }
      }

      // Update sync time regardless of whether we had changes
      final syncTime = DateTime.now().toUtc();
      await localDataSource.updateLastSyncTime(syncTime);
      print('🔄 [REPOSITORY] Sync completed successfully at ${syncTime.toIso8601String()}');

      // Clear sync in progress flag
      await prefs.setBool('sync_in_progress', false);
      print('🔄 [REPOSITORY] Cleared sync_in_progress flag');

      // Return all local leads
      final localLeads = await localDataSource.getAllLeads();
      print('✅ [REPOSITORY] Sync operation completed successfully. Total leads in database: ${localLeads.length}');
      return Right(localLeads);
    } catch (e) {
      print('❌ [REPOSITORY] Sync operation failed: $e');

      // Clear sync in progress flag even on error
      try {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('sync_in_progress', false);
        print('🔄 [REPOSITORY] Cleared sync_in_progress flag after error');
      } catch (prefError) {
        print('⚠️ [REPOSITORY] Failed to clear sync_in_progress flag: $prefError');
      }

      return Left('Sync failed: ${e.toString()}');
    }
  }

  /// Fetch all leads from API using pagination
  Future<List<OfflineLeadModel>> _fetchAllLeadsWithPagination(DateTime? lastSyncTime, [bool shouldViewOnlyAssigned = false]) async {
    final List<OfflineLeadModel> allLeads = [];
    int currentPage = 1;
    const int pageSize = 500;
    bool hasMoreData = true;

    print('🔄 [REPOSITORY] Starting pagination fetch...');

    while (hasMoreData) {
      try {
        print('🔄 [REPOSITORY] Fetching page $currentPage (pageSize: $pageSize)...');

        final apiResponse = await remoteDataSource.fetchLeads(
          page: currentPage,
          pageSize: pageSize,
          lastSyncTime: lastSyncTime,
          shouldViewOnlyAssigned: shouldViewOnlyAssigned,
        );

        final pageLeads = apiResponse.data;
        print('🔄 [REPOSITORY] Page $currentPage returned ${pageLeads.length} leads');

        if (pageLeads.isEmpty) {
          print('✅ [REPOSITORY] Page $currentPage returned empty list - pagination complete');
          hasMoreData = false;
        } else {
          allLeads.addAll(pageLeads);
          print('🔄 [REPOSITORY] Added ${pageLeads.length} leads. Total so far: ${allLeads.length}');

          // Check if we got less than the page size, which might indicate last page
          if (pageLeads.length < pageSize) {
            print('✅ [REPOSITORY] Page $currentPage returned ${pageLeads.length} < $pageSize leads - likely last page');
            hasMoreData = false;
          } else {
            currentPage++;
            print('🔄 [REPOSITORY] Moving to next page: $currentPage');
          }
        }
      } catch (e) {
        print('❌ [REPOSITORY] Error fetching page $currentPage: $e');
        // Don't break the entire sync for one page failure, but stop pagination
        hasMoreData = false;
        rethrow;
      }
    }

    print('✅ [REPOSITORY] Pagination completed. Total pages fetched: ${currentPage - 1}, Total leads: ${allLeads.length}');
    return allLeads;
  }

  /// Fetch incremental changes from API using pagination
  Future<List<OfflineLeadModel>> _fetchIncrementalChanges(DateTime lastSyncTime) async {
    final List<OfflineLeadModel> allChanges = [];
    int currentPage = 1;
    const int pageSize = 500;
    bool hasMoreData = true;
    final currentTime = DateTime.now().toUtc();

    print('🔄 [INCREMENTAL_REPOSITORY] Starting incremental changes fetch...');
    print('🔄 [INCREMENTAL_REPOSITORY] Date range: ${lastSyncTime.toUtc().toIso8601String()} to ${currentTime.toIso8601String()}');

    // Get user ID from remote data source
    final userId = (remoteDataSource as OfflineLeadsRemoteDataSourceImpl).userId;

    if (userId == null || userId.isEmpty) {
      throw Exception('User ID is required for incremental sync but was not provided to remote data source');
    }

    while (hasMoreData) {
      try {
        print('🔄 [INCREMENTAL_REPOSITORY] Fetching incremental page $currentPage (pageSize: $pageSize)...');

        final apiResponse = await (remoteDataSource as OfflineLeadsRemoteDataSourceImpl).fetchIncrementalChanges(
          dateRangeFrom: lastSyncTime.toUtc(),
          dateRangeTo: currentTime,
          userId: userId,
          page: currentPage,
          pageSize: pageSize,
        );

        final pageChanges = apiResponse.data;
        print('🔄 [INCREMENTAL_REPOSITORY] Incremental page $currentPage returned ${pageChanges.length} changes');

        if (pageChanges.isEmpty) {
          print('✅ [INCREMENTAL_REPOSITORY] Incremental page $currentPage returned empty list - pagination complete');
          hasMoreData = false;
        } else {
          allChanges.addAll(pageChanges);
          print('🔄 [INCREMENTAL_REPOSITORY] Added ${pageChanges.length} changes. Total so far: ${allChanges.length}');

          // Log summary of changes
          final insertUpdates = pageChanges.where((lead) => !lead.isDeleted && !lead.isArchived).length;
          final deletes = pageChanges.where((lead) => lead.isDeleted || lead.isArchived).length;
          print('🔄 [INCREMENTAL_REPOSITORY] Page $currentPage summary: $insertUpdates insert/updates, $deletes deletes/archives');

          // Check if we got less than the page size, which might indicate last page
          if (pageChanges.length < pageSize) {
            print('✅ [INCREMENTAL_REPOSITORY] Incremental page $currentPage returned ${pageChanges.length} < $pageSize changes - likely last page');
            hasMoreData = false;
          } else {
            currentPage++;
            print('🔄 [INCREMENTAL_REPOSITORY] Moving to next incremental page: $currentPage');
          }
        }
      } catch (e) {
        print('❌ [INCREMENTAL_REPOSITORY] Error fetching incremental page $currentPage: $e');
        // Don't break the entire sync for one page failure, but stop pagination
        hasMoreData = false;
        rethrow;
      }
    }

    // Log final summary
    final totalInsertUpdates = allChanges.where((lead) => !lead.isDeleted && !lead.isArchived).length;
    final totalDeletes = allChanges.where((lead) => lead.isDeleted || lead.isArchived).length;
    print('✅ [INCREMENTAL_REPOSITORY] Incremental pagination completed:');
    print('✅ [INCREMENTAL_REPOSITORY] - Total pages fetched: ${currentPage - 1}');
    print('✅ [INCREMENTAL_REPOSITORY] - Total changes: ${allChanges.length}');
    print('✅ [INCREMENTAL_REPOSITORY] - Insert/Updates: $totalInsertUpdates');
    print('✅ [INCREMENTAL_REPOSITORY] - Deletes/Archives: $totalDeletes');

    return allChanges;
  }

  /// Fetch all prospects from API using pagination
  Future<List<OfflineLeadModel>> _fetchAllProspectsWithPagination(DateTime? lastSyncTime) async {
    print('🔄 [REPOSITORY] _fetchAllProspectsWithPagination called with lastSyncTime: $lastSyncTime');
    final List<OfflineLeadModel> allProspects = [];
    int currentPage = 1;
    const int pageSize = 500;
    bool hasMoreData = true;

    print('🔄 [REPOSITORY] Starting prospect pagination fetch...');
    print('🔄 [REPOSITORY] remoteDataSource type: ${remoteDataSource.runtimeType}');

    while (hasMoreData) {
      try {
        print('🔄 [REPOSITORY] *** FETCHING PROSPECT PAGE $currentPage ***');
        print('🔄 [REPOSITORY] Fetching prospect page $currentPage (pageSize: $pageSize)...');
        print('🔄 [REPOSITORY] About to call remoteDataSource.fetchProspects...');
        print('🔄 [REPOSITORY] Parameters: page=$currentPage, pageSize=$pageSize, lastSyncTime=$lastSyncTime');

        final apiResponse = await remoteDataSource.fetchProspects(
          page: currentPage,
          pageSize: pageSize,
          lastSyncTime: lastSyncTime,
        );

        print('🔄 [REPOSITORY] *** PROSPECT API CALL COMPLETED ***');
        print('🔄 [REPOSITORY] API response received for prospect page $currentPage');

        final pageProspects = apiResponse.data;
        print('🔄 [REPOSITORY] Prospect page $currentPage returned ${pageProspects.length} prospects');

        if (pageProspects.isEmpty) {
          print('✅ [REPOSITORY] Prospect page $currentPage returned empty list - pagination complete');
          hasMoreData = false;
        } else {
          allProspects.addAll(pageProspects);
          print('🔄 [REPOSITORY] Added ${pageProspects.length} prospects. Total so far: ${allProspects.length}');

          // Check if we got less than the page size, which might indicate last page
          if (pageProspects.length < pageSize) {
            print('✅ [REPOSITORY] Prospect page $currentPage returned ${pageProspects.length} < $pageSize prospects - likely last page');
            hasMoreData = false;
          } else {
            currentPage++;
            print('🔄 [REPOSITORY] Moving to next prospect page: $currentPage');
          }
        }
      } catch (e) {
        print('❌ [REPOSITORY] Error fetching prospect page $currentPage: $e');
        // Don't break the entire sync for one page failure, but stop pagination
        hasMoreData = false;
        rethrow;
      }
    }

    print('✅ [REPOSITORY] Prospect pagination completed. Total prospects fetched: ${allProspects.length}');
    return allProspects;
  }

  /// Fetch prospect incremental changes from API using pagination
  Future<List<OfflineLeadModel>> _fetchProspectIncrementalChanges(DateTime lastSyncTime) async {
    final List<OfflineLeadModel> allChanges = [];
    int currentPage = 1;
    const int pageSize = 500;
    bool hasMoreData = true;
    final currentTime = DateTime.now().toUtc();

    print('🔄 [PROSPECT_INCREMENTAL_REPOSITORY] Starting prospect incremental changes fetch...');
    print('🔄 [PROSPECT_INCREMENTAL_REPOSITORY] Date range: ${lastSyncTime.toUtc().toIso8601String()} to ${currentTime.toIso8601String()}');

    // Get user ID from remote data source
    final userId = (remoteDataSource as OfflineLeadsRemoteDataSourceImpl).userId;
    if (userId == null || userId.isEmpty) {
      throw Exception('User ID is required for incremental sync but was not provided');
    }

    while (hasMoreData) {
      try {
        print('🔄 [PROSPECT_INCREMENTAL_REPOSITORY] Fetching prospect incremental page $currentPage (pageSize: $pageSize)...');

        final apiResponse = await (remoteDataSource as OfflineLeadsRemoteDataSourceImpl).fetchProspectIncrementalChanges(
          dateRangeFrom: lastSyncTime.toUtc(),
          dateRangeTo: currentTime,
          userId: userId,
          page: currentPage,
          pageSize: pageSize,
        );

        final pageChanges = apiResponse.data;
        print('🔄 [PROSPECT_INCREMENTAL_REPOSITORY] Prospect incremental page $currentPage returned ${pageChanges.length} changes');

        if (pageChanges.isEmpty) {
          print('✅ [PROSPECT_INCREMENTAL_REPOSITORY] Prospect incremental page $currentPage returned empty list - pagination complete');
          hasMoreData = false;
        } else {
          allChanges.addAll(pageChanges);
          print('🔄 [PROSPECT_INCREMENTAL_REPOSITORY] Added ${pageChanges.length} prospect changes. Total so far: ${allChanges.length}');

          // Log summary of changes
          final insertUpdates = pageChanges.where((prospect) => !prospect.isDeleted && !prospect.isArchived).length;
          final deletes = pageChanges.where((prospect) => prospect.isDeleted || prospect.isArchived).length;
          print('🔄 [PROSPECT_INCREMENTAL_REPOSITORY] Page $currentPage summary: $insertUpdates insert/updates, $deletes deletes/archives');

          // Check if we got less than the page size, which might indicate last page
          if (pageChanges.length < pageSize) {
            print('✅ [PROSPECT_INCREMENTAL_REPOSITORY] Prospect incremental page $currentPage returned ${pageChanges.length} < $pageSize changes - likely last page');
            hasMoreData = false;
          } else {
            currentPage++;
            print('🔄 [PROSPECT_INCREMENTAL_REPOSITORY] Moving to next prospect incremental page: $currentPage');
          }
        }
      } catch (e) {
        print('❌ [PROSPECT_INCREMENTAL_REPOSITORY] Error fetching prospect incremental page $currentPage: $e');
        // Don't break the entire sync for one page failure, but stop pagination
        hasMoreData = false;
        rethrow;
      }
    }

    // Log final summary
    final totalInsertUpdates = allChanges.where((prospect) => !prospect.isDeleted && !prospect.isArchived).length;
    final totalDeletes = allChanges.where((prospect) => prospect.isDeleted || prospect.isArchived).length;
    print('✅ [PROSPECT_INCREMENTAL_REPOSITORY] Prospect incremental pagination completed:');
    print('✅ [PROSPECT_INCREMENTAL_REPOSITORY] - Total pages fetched: ${currentPage - 1}');
    print('✅ [PROSPECT_INCREMENTAL_REPOSITORY] - Total changes: ${allChanges.length}');
    print('✅ [PROSPECT_INCREMENTAL_REPOSITORY] - Insert/Updates: $totalInsertUpdates');
    print('✅ [PROSPECT_INCREMENTAL_REPOSITORY] - Deletes/Archives: $totalDeletes');

    return allChanges;
  }

  @override
  Future<Either<String, List<OfflineLeadModel>>> getAllLeads() async {
    try {
      final leads = await localDataSource.getAllLeads();
      return Right(leads);
    } catch (e) {
      return Left('Failed to get leads: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, OfflineLeadModel?>> getLeadById(String id) async {
    try {
      final lead = await localDataSource.getLeadById(id);
      return Right(lead);
    } catch (e) {
      return Left('Failed to get lead: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, OfflineLeadModel?>> getLeadByPhoneNumber(String phoneNumber) async {
    try {
      final lead = await localDataSource.getLeadByPhoneNumber(phoneNumber);
      return Right(lead);
    } catch (e) {
      return Left('Failed to get lead by phone number: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, void>> deleteLead(String id) async {
    try {
      await localDataSource.deleteLead(id);
      return const Right(null);
    } catch (e) {
      return Left('Failed to delete lead: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, void>> deleteAllLeads() async {
    try {
      await localDataSource.deleteAllLeads();
      return const Right(null);
    } catch (e) {
      return Left('Failed to delete all leads: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, int>> getLeadsCount() async {
    try {
      final count = await localDataSource.getLeadsCount();
      return Right(count);
    } catch (e) {
      return Left('Failed to get leads count: ${e.toString()}');
    }
  }

  @override
  Future<Either<String, DateTime?>> getLastSyncTime() async {
    try {
      final lastSyncTime = await localDataSource.getLastSyncTime();
      return Right(lastSyncTime);
    } catch (e) {
      return Left('Failed to get last sync time: ${e.toString()}');
    }
  }
}
