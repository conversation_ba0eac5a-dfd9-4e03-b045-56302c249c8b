import 'package:package_info_plus/package_info_plus.dart';
import '../secret_manager_service/secret_manager_service.dart';
import 'force_update_service.dart';

class ForceUpdateServiceImpl implements ForceUpdateService {
  final SecretsManagerService secretsManagerService;

  ForceUpdateServiceImpl({required this.secretsManagerService});

  @override
  Future<String> getCurrentAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    return packageInfo.version;
  }

  @override
  Future<String?> getForceUpdateVersion() async {
    try {
      return await secretsManagerService.getForceUpdateVersion();
    } catch (e) {
      print('Error getting force update version: $e');
      return null;
    }
  }

  @override
  Future<bool> shouldForceUpdate() async {
    try {
      final currentVersion = await getCurrentAppVersion();
      final forceUpdateVersion = await getForceUpdateVersion();

      if (forceUpdateVersion == null || forceUpdateVersion.isEmpty) {
        return false;
      }

      return _isVersionLower(currentVersion, forceUpdateVersion);
    } catch (e) {
      print('Error checking force update: $e');
      return false;
    }
  }

  bool _isVersionLower(String currentVersion, String forceUpdateVersion) {
    try {
      final currentParts = currentVersion.split('.').map(int.parse).toList();
      final forceParts = forceUpdateVersion.split('.').map(int.parse).toList();

      // Ensure both version arrays have the same length by padding with zeros
      final maxLength = currentParts.length > forceParts.length 
          ? currentParts.length 
          : forceParts.length;

      while (currentParts.length < maxLength) {
        currentParts.add(0);
      }
      while (forceParts.length < maxLength) {
        forceParts.add(0);
      }

      // Compare version parts
      for (int i = 0; i < maxLength; i++) {
        if (currentParts[i] < forceParts[i]) {
          return true;
        } else if (currentParts[i] > forceParts[i]) {
          return false;
        }
      }

      return false; // Versions are equal
    } catch (e) {
      print('Error comparing versions: $e');
      return false;
    }
  }
}
