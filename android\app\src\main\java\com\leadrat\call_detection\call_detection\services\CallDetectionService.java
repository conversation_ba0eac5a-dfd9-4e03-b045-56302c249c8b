package com.leadrat.call_detection.call_detection.services;

import android.Manifest;
import android.app.ActivityManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.database.ContentObserver;
import android.database.Cursor;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.provider.CallLog;
import android.telephony.PhoneStateListener;
import android.telephony.TelephonyManager;
import android.util.Log;
import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;
import androidx.core.content.ContextCompat;

import com.leadrat.call_detection.call_detection.CallReceiver;
import com.leadrat.call_detection.call_detection.MainActivity;
import com.leadrat.call_detection.call_detection.R;
import com.leadrat.call_detection.call_detection.enums.CallDirection;
import com.leadrat.call_detection.call_detection.enums.CallStatus;
import com.leadrat.call_detection.call_detection.enums.CallType;
import com.leadrat.call_detection.call_detection.models.CallInfo;
import com.leadrat.call_detection.call_detection.models.CallLogInfo;

import java.util.Date;
import java.util.concurrent.atomic.AtomicBoolean;

public class CallDetectionService extends Service {
    private static final String TAG = "CallDetectionService";
    private static final String CHANNEL_ID = "CallDetectionServiceChannel";
    private static final int SERVICE_RESTART_DELAY = 5000; // 5 seconds
    private static final int SERVICE_CHECK_INTERVAL = 60000; // 1 minute

    private TelephonyManager telephonyManager;
    private PhoneStateListener phoneStateListener;
    private SharedPreferences preferences;
    private SharedPreferences.Editor editor;
    private Handler mainHandler;
    private Handler serviceCheckHandler;
    private ContentObserver callLogObserver;

    // Call state tracking
    private String lastPhoneNumber = "";
    private long callStartTime = 0;
    private long callRingingStartTime = 0;
    private long callEndTime = 0;
    private boolean isIncoming = false;
    private boolean isCallAnswered = false;
    private boolean isCallDialed = false;
    private boolean isRinging = false;
    private boolean callInProgress = false;
    private long lastCallLogCheck = 0;

    // Enhanced outgoing call tracking
    private boolean outgoingCallConnected = false;
    private long outgoingCallConnectedTime = 0;
    private long minimumCallDurationForConnection = 3000; // 3 seconds minimum to consider connected

    // Prevent duplicate processing
    private AtomicBoolean processingCallEnd = new AtomicBoolean(false);

    // Call end detection timer for devices that don't reliably fire IDLE state
    private Runnable callEndDetectionRunnable;
    private static final int CALL_END_DETECTION_DELAY = 10000; // 10 seconds

    // Reference to CallReceiver for API calls and recording
    private CallReceiver callReceiver;

    // Service health check runnable
    private final Runnable serviceHealthCheck = new Runnable() {
        @Override
        public void run() {
            Log.d(TAG, "Performing service health check");

            // Schedule next check
            if (serviceCheckHandler != null) {
                serviceCheckHandler.postDelayed(this, SERVICE_CHECK_INTERVAL);
            }
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "📞 [CALL_DETECTION] CallDetectionService onCreate - Starting foreground service immediately");

        // CRITICAL: Start foreground service IMMEDIATELY to prevent ANR
        // Create a basic notification first, then improve it later
        try {
            createNotificationChannel(); // This should be fast
            Notification notification = createNotification();

            if (Build.VERSION.SDK_INT >= 34) { // Android 14+ (UPSIDE_DOWN_CAKE)
                try {
                    int foregroundServiceTypePhoneCall = (int) Class
                            .forName("android.app.ServiceInfo")
                            .getDeclaredField("FOREGROUND_SERVICE_TYPE_PHONE_CALL")
                            .get(null);

                    startForeground(1, notification, foregroundServiceTypePhoneCall);
                    Log.d(TAG, "✅ [CALL_DETECTION] Started foreground service with phone call type for Android 14+");
                } catch (Exception e) {
                    Log.e(TAG, "Error starting foreground service with phone call type: " + e.getMessage());
                    startForeground(1, notification); // Fallback in case of error
                    Log.d(TAG, "✅ [CALL_DETECTION] Started foreground service with fallback method");
                }
            } else {
                startForeground(1, notification);
                Log.d(TAG, "✅ [CALL_DETECTION] Started foreground service for Android < 14");
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ [CALL_DETECTION] CRITICAL: Failed to start foreground service: " + e.getMessage());
            // Create a minimal notification as last resort
            try {
                Notification.Builder builder = new Notification.Builder(this)
                        .setContentTitle("Call Detection Service")
                        .setContentText("Running")
                        .setSmallIcon(android.R.drawable.ic_menu_call);

                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    builder.setChannelId("call_detection_channel");
                }

                startForeground(1, builder.build());
                Log.d(TAG, "✅ [CALL_DETECTION] Started foreground service with minimal notification");
            } catch (Exception e2) {
                Log.e(TAG, "❌ [CALL_DETECTION] FATAL: Could not start foreground service at all: " + e2.getMessage());
                stopSelf();
                return;
            }
        }

        // Initialize other components after foreground service is started
        try {
            preferences = PreferenceManager.getDefaultSharedPreferences(this);
            editor = preferences.edit();
            mainHandler = new Handler(Looper.getMainLooper());
            callReceiver = new CallReceiver();

            initializeCallStateListener();
            Log.d(TAG, "✅ [CALL_DETECTION] Service components initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "❌ [CALL_DETECTION] Failed to initialize service components: " + e.getMessage());
        }

        // Start the offline queue service for background recording and call log sync
        try {
            Intent offlineQueueIntent = new Intent(this, OfflineQueueService.class);
            startService(offlineQueueIntent);
            Log.d(TAG, "✅ [OFFLINE_QUEUE] OfflineQueueService started");
        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_QUEUE] Failed to start OfflineQueueService: " + e.getMessage());
        }
    }

    private void initializeCallStateListener() {
        // Check for required permissions before initializing
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED ||
                ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CALL_LOG) != PackageManager.PERMISSION_GRANTED) {

                Log.e(TAG, "Required permissions not granted. Cannot initialize call state listener.");
                stopSelf(); // Stop the service if permissions are not granted
                return;
            }
        }

        telephonyManager = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
        phoneStateListener = new PhoneStateListener() {
            @Override
            public void onCallStateChanged(int state, String incomingNumber) {
                super.onCallStateChanged(state, incomingNumber);
                handleCallStateChange(state, incomingNumber);
            }
        };

        try {
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
            Log.d(TAG, "Call state listener initialized");
        } catch (SecurityException e) {
            Log.e(TAG, "Security exception when initializing call state listener: " + e.getMessage());
            stopSelf(); // Stop the service if there's a security exception
        }
    }

    private void handleCallStateChange(int state, String phoneNumber) {
        // Always log the state change for debugging
        String stateStr = "UNKNOWN";
        switch (state) {
            case TelephonyManager.CALL_STATE_RINGING: stateStr = "RINGING"; break;
            case TelephonyManager.CALL_STATE_OFFHOOK: stateStr = "OFFHOOK"; break;
            case TelephonyManager.CALL_STATE_IDLE: stateStr = "IDLE"; break;
        }

        Log.d(TAG, "📞 [STATE_CHANGE] Call state changed to: " + stateStr + ", number: " +
              (phoneNumber != null ? phoneNumber : "unknown"));
        Log.d(TAG, "📞 [STATE_CHANGE] Current flags - callInProgress: " + callInProgress + ", isIncoming: " + isIncoming + ", isCallAnswered: " + isCallAnswered);

        // If phone number is null or empty in RINGING state, try to get it from preferences
        // This helps with cases where the number might not be available in the broadcast
        if ((phoneNumber == null || phoneNumber.isEmpty()) && state == TelephonyManager.CALL_STATE_RINGING) {
            phoneNumber = preferences.getString("LastPhoneNumber", "");
            Log.d(TAG, "Using last phone number from preferences: " + phoneNumber);
        }

        // Save the phone number if it's not empty
        if (phoneNumber != null && !phoneNumber.isEmpty()) {
            lastPhoneNumber = phoneNumber;
            editor.putString("LastPhoneNumber", phoneNumber);
            editor.apply();
        }

        switch (state) {
            case TelephonyManager.CALL_STATE_RINGING:
                Log.d(TAG, "📞 [STATE_CHANGE] Processing RINGING state");
                handleRingingState(phoneNumber);
                break;

            case TelephonyManager.CALL_STATE_OFFHOOK:
                Log.d(TAG, "📞 [STATE_CHANGE] Processing OFFHOOK state");
                handleOffHookState(phoneNumber);
                break;

            case TelephonyManager.CALL_STATE_IDLE:
                Log.d(TAG, "📞 [STATE_CHANGE] Processing IDLE state");
                handleIdleState(phoneNumber);
                break;
        }

        Log.d(TAG, "📞 [STATE_CHANGE] State change processing completed for: " + stateStr);
    }

    private void handleRingingState(String phoneNumber) {
        Log.d(TAG, "Handling RINGING state");

        // Reset call state for new incoming call
        isIncoming = true;
        isRinging = true;
        isCallAnswered = false;
        callRingingStartTime = System.currentTimeMillis();

        // Store state in preferences
        editor.putBoolean("Ringing", true);
        editor.putBoolean("CallApi", true);
        editor.putLong("RingingStartTime", callRingingStartTime);
        editor.apply();

        // Make API call to get caller info and show overlay
        if (phoneNumber != null && !phoneNumber.isEmpty() && !callInProgress) {
            callInProgress = true;
            callReceiver.makeApiCall(phoneNumber, this);
        }

        editor.putBoolean("CallApi", false).apply();
    }

    private void handleOffHookState(String phoneNumber) {
        Log.d(TAG, "Handling OFFHOOK state");

        boolean didRing = preferences.getBoolean("Ringing", false);

        // Start a fallback timer to detect call end if IDLE state is not triggered
        // This helps with devices that don't reliably fire IDLE state
        startCallEndDetectionTimer();

        if (didRing) {
            // Scenario 1: Incoming call answered
            Log.d(TAG, "Incoming call answered");
            isCallAnswered = true;
            callStartTime = System.currentTimeMillis();

            editor.putBoolean("CallPicked", true);
            editor.putLong("CallPickedStartTime", callStartTime);
            editor.apply();

            // Stop overlay service as call is answered
            stopService(new Intent(this, OverlayService.class));

            // Check if external recording path is configured
            String directoryPath = preferences.getString("flutter.directoryPath", null);
            if (directoryPath != null) {
                Log.d(TAG, "Using external directory path for recording: " + directoryPath);
            } else {
                Log.d(TAG, "No external recording path configured - call recording disabled");
            }
        } else if (!callInProgress) {
            // Scenario 2: Outgoing call
            Log.d(TAG, "Outgoing call detected");
            isIncoming = false;
            isCallDialed = true;
            callStartTime = System.currentTimeMillis();
            callInProgress = true;

            // Reset outgoing call connection tracking
            outgoingCallConnected = false;
            outgoingCallConnectedTime = 0;

            editor.putBoolean("CallDialed", true);
            editor.putLong("CallDialedStartTime", callStartTime);
            editor.apply();

            // Make API call for outgoing call
            if (phoneNumber != null && !phoneNumber.isEmpty()) {
                callReceiver.makeApiCall(phoneNumber, this);
            }

            // Check if call recording is enabled
            callReceiver.isCallRecordingEnabledInBackground((isEnabled, shouldViewOnlyAssigned, shouldOpenPopupAfterCall) -> {
                if (isEnabled) {
                    String directoryPath = preferences.getString("flutter.directoryPath", null);
                    if (directoryPath != null) {
                        Log.d(TAG, "Using external directory path for recording: " + directoryPath);
                    } else {
                        Log.d(TAG, "No external recording path configured - call recording disabled");
                    }
                }
                // shouldViewOnlyAssigned is already stored in SharedPreferences by the callback method
                Log.d(TAG, "shouldViewOnlyAssigned setting received: " + shouldViewOnlyAssigned);
            });
        } else if (isCallDialed && !outgoingCallConnected) {
            // Scenario 3: Outgoing call connection detected (OFFHOOK after dial)
            Log.d(TAG, "Outgoing call connection detected - call was answered");
            outgoingCallConnected = true;
            outgoingCallConnectedTime = System.currentTimeMillis();

            editor.putBoolean("OutgoingCallConnected", true);
            editor.putLong("OutgoingCallConnectedTime", outgoingCallConnectedTime);
            editor.apply();

            Log.d(TAG, "📞 [OUTGOING_CONNECTED] Call connected at: " + new java.util.Date(outgoingCallConnectedTime));
        }
    }

    private void handleIdleState(String phoneNumber) {
        Log.d(TAG, "🔚 [CALL_END] Handling IDLE state for phone: " + phoneNumber);
        Log.d(TAG, "🔚 [CALL_END] Current call state - isIncoming: " + isIncoming + ", isCallAnswered: " + isCallAnswered + ", isCallDialed: " + isCallDialed);

        // Stop the call end detection timer since IDLE state was triggered
        stopCallEndDetectionTimer();

        // Prevent duplicate processing of call end
        if (processingCallEnd.getAndSet(true)) {
            Log.d(TAG, "🔚 [CALL_END] Already processing call end, ignoring duplicate event");
            return;
        }

        // Add 3-second delay to allow Android system to write call log entry
        Log.d(TAG, "⏳ [CALL_END] Waiting 3 seconds for system to write call log entry...");

        // Use Handler to post delayed execution to avoid ANR (Application Not Responding)
        mainHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "✅ [CALL_END] Delay completed, proceeding with call log fetch");
                processCallEndAfterDelay(phoneNumber);
            }
        }, 3000); // 3 second delay
    }

    private void processCallEndAfterDelay(String phoneNumber) {
        callInProgress = false;

        // Store call end time
        editor.putLong("CallEndTime", callEndTime);
        editor.apply();

        boolean didRing = preferences.getBoolean("Ringing", false);
        boolean callPicked = preferences.getBoolean("CallPicked", false);
        boolean callDialed = preferences.getBoolean("CallDialed", false);

        Log.d(TAG, "🔚 [CALL_END] Preferences check - didRing: " + didRing + ", callPicked: " + callPicked + ", callDialed: " + callDialed);

        String directoryPath = preferences.getString("flutter.directoryPath", null);

        if (didRing && !callPicked) {
            // Scenario 3: Missed call - No recording needed
            Log.d(TAG, "Missed call detected - No recording needed, duration will be set to 0");
            editor.putBoolean("MissedCall", true);
            editor.apply();
            // For missed calls, don't send recording
            callReceiver.saveCallLog(this, null, phoneNumber != null ? phoneNumber : lastPhoneNumber, editor);
            processingCallEnd.set(false);
        } else if (didRing && callPicked) {
            // Scenario 1: Incoming answered call - Need recording
            if (directoryPath != null) {
                // External recording path - find matching recording
                Log.d(TAG, "Finding external recording for answered call");
                // Add a delay to ensure recording file is saved
                mainHandler.postDelayed(() -> {
                    Log.d(TAG, "Attempting to find call recording after delay");
                    String callRecording = callReceiver.getMatchingCallRecordingEnhanced(
                        directoryPath,
                        phoneNumber != null ? phoneNumber : lastPhoneNumber,
                        preferences.getLong("RingingStartTime", 0),
                        callEndTime
                    );
                    callReceiver.saveCallLog(this, callRecording, phoneNumber != null ? phoneNumber : lastPhoneNumber, editor);
                    processingCallEnd.set(false);
                }, 8000); // Wait 8 seconds before attempting to match
            } else {
                // No external recording path configured
                Log.d(TAG, "No external recording path configured - saving call log without recording");
                callReceiver.saveCallLog(this, null, phoneNumber != null ? phoneNumber : lastPhoneNumber, editor);
                processingCallEnd.set(false);
            }
        } else if (callDialed) {
            // Scenario 2: Outgoing call
            // Check if the call was connected (duration > 0)
            CallInfo callInfo = getCallInformation();
            boolean wasConnected = false;

            try {
                // Try to determine if call was connected by checking duration
                double duration = Double.parseDouble(callInfo.duration);
                wasConnected = duration > 0;
            } catch (NumberFormatException e) {
                Log.e(TAG, "Error parsing call duration: " + e.getMessage());
                // Default to not connected if we can't parse the duration
                wasConnected = false;
            }

            if (wasConnected) {
                // Outgoing call was connected - send recording
                if (directoryPath != null) {
                    // External recording path - find matching recording
                    Log.d(TAG, "Finding external recording for connected outgoing call");
                    // Add a delay to ensure recording file is saved
                    mainHandler.postDelayed(() -> {
                        Log.d(TAG, "Attempting to find call recording after delay");
                        String callRecording = callReceiver.getMatchingCallRecordingEnhanced(
                            directoryPath,
                            phoneNumber != null ? phoneNumber : lastPhoneNumber,
                            preferences.getLong("CallDialedStartTime", 0),
                            callEndTime
                        );
                        callReceiver.saveCallLog(this, callRecording, phoneNumber != null ? phoneNumber : lastPhoneNumber, editor);
                        processingCallEnd.set(false);
                    }, 8000); // Wait 8 seconds before attempting to match
                } else {
                    // No external recording path configured
                    Log.d(TAG, "No external recording path configured - saving call log without recording");
                    callReceiver.saveCallLog(this, null, phoneNumber != null ? phoneNumber : lastPhoneNumber, editor);
                    processingCallEnd.set(false);
                }
            } else {
                // Outgoing call was disconnected - stop recording but don't send it
                Log.d(TAG, "Outgoing call was disconnected - stopping recording but not sending it");

                // For disconnected outgoing calls, no recording processing needed
                if (directoryPath != null) {
                    Log.d(TAG, "External recording path - no action needed for disconnected call");
                } else {
                    Log.d(TAG, "No external recording path configured - no action needed");
                }

                // Save call log without the recording
                callReceiver.saveCallLog(this, null, phoneNumber != null ? phoneNumber : lastPhoneNumber, editor);
                processingCallEnd.set(false);
            }
        } else {
            // Fallback case - should not normally happen
            Log.d(TAG, "Unidentified call type - saving call log without recording");
            callReceiver.saveCallLog(this, null, phoneNumber != null ? phoneNumber : lastPhoneNumber, editor);
            processingCallEnd.set(false);
        }

        // Reset call state for next call
        isRinging = false;
        isCallAnswered = false;
        isCallDialed = false;
        outgoingCallConnected = false;
        outgoingCallConnectedTime = 0;
    }

    /**
     * Get call information including duration, direction, and status
     */
    public CallInfo getCallInformation() {
        boolean didRing = preferences.getBoolean("Ringing", false);
        boolean callDialed = preferences.getBoolean("CallDialed", false);
        boolean callPicked = preferences.getBoolean("CallPicked", false);
        boolean missedCall = preferences.getBoolean("MissedCall", false);

        long startTime;
        long endTime;
        String duration = "0";
        CallDirection callDirection = CallDirection.NONE;
        CallStatus callStatus = CallStatus.NONE;

        Log.d(TAG, "GetCallInfo: didRing=" + didRing + ", callPicked=" + callPicked + ", callDialed=" + callDialed);

        try {
            if (didRing && callPicked) {
                // Incoming answered call
                startTime = preferences.getLong("RingingStartTime", 0);
                endTime = preferences.getLong("CallEndTime", 0);
                CallLogInfo callInfo = getCallLogs(CallType.INCOMING);

                // Validate duration from call log
                if (callInfo != null && !callInfo.phoneDuration.equals("0")) {
                    duration = callInfo.phoneDuration;
                } else {
                    // Fallback to calculated duration if call log duration is 0
                    long calculatedDuration = (endTime - preferences.getLong("CallPickedStartTime", 0)) / 1000;
                    if (calculatedDuration > 0) {
                        duration = String.valueOf(calculatedDuration);
                    }
                }

                callDirection = CallDirection.INCOMING;
                callStatus = CallStatus.ANSWERED;
            } else if (callDialed) {
                // Outgoing call - Enhanced detection logic
                long callDialedStartTime = preferences.getLong("CallDialedStartTime", 0);
                long callConnectedTime = preferences.getLong("OutgoingCallConnectedTime", 0);
                endTime = preferences.getLong("CallEndTime", 0);
                CallLogInfo callInfo = getCallLogs(CallType.OUTGOING);

                // Calculate durations
                long totalCallTime = (endTime - callDialedStartTime) / 1000; // Total time from dial to end
                long conversationTime = 0; // Actual conversation time (OFF_HOOK to IDLE)

                if (callConnectedTime > 0 && endTime > callConnectedTime) {
                    conversationTime = (endTime - callConnectedTime) / 1000;
                }

                long callDuration = 0;
                if (callInfo != null && !callInfo.phoneDuration.isEmpty()) {
                    try {
                        Log.d(TAG, "📞 [CALL_INFO] Call log duration: " + callInfo.phoneDuration);
                        callDuration = Long.parseLong(callInfo.phoneDuration);
                    } catch (NumberFormatException e) {
                        Log.e(TAG, "Error parsing call duration: " + e.getMessage());
                    }
                }

                // Enhanced connection detection using multiple indicators
                boolean isConnected = determineOutgoingCallConnection(callDuration, totalCallTime, callDialedStartTime, endTime);

                callDirection = CallDirection.OUTGOING;
                callStatus = isConnected ? CallStatus.ANSWERED : CallStatus.DISCONNECTED;

                // Set proper start time based on connection status
                if (isConnected && callConnectedTime > 0) {
                    startTime = callConnectedTime; // Use actual conversation start time (OFF_HOOK)
                } else {
                    startTime = callDialedStartTime; // Use dial time for disconnected calls
                }

                Log.d(TAG, "📞 [CALL_INFO] Outgoing call analysis:");
                Log.d(TAG, "📞 [CALL_INFO] - Dial time: " + new java.util.Date(callDialedStartTime));
                if (callConnectedTime > 0) {
                    Log.d(TAG, "📞 [CALL_INFO] - Connected time: " + new java.util.Date(callConnectedTime));
                }
                Log.d(TAG, "📞 [CALL_INFO] - End time: " + new java.util.Date(endTime));
                Log.d(TAG, "📞 [CALL_INFO] - Call log duration: " + callDuration + " seconds");
                Log.d(TAG, "📞 [CALL_INFO] - Total call time: " + totalCallTime + " seconds");
                Log.d(TAG, "📞 [CALL_INFO] - Conversation time: " + conversationTime + " seconds");
                Log.d(TAG, "📞 [CALL_INFO] - Connection detected: " + isConnected);
                Log.d(TAG, "📞 [CALL_INFO] - Final status: " + callStatus);

                // Determine final duration based on connection status and available data
                if (callStatus == CallStatus.ANSWERED) {
                    // For connected calls, prioritize system call log duration
                    if (callInfo != null && !callInfo.phoneDuration.equals("0")) {
                        // Use call log duration (most accurate)
                        duration = callInfo.phoneDuration;
                        Log.d(TAG, "📞 [CALL_INFO] Connected call - using call log duration: " + duration);
                    } else if (conversationTime > 0) {
                        // Use actual conversation time (OFF_HOOK to IDLE)
                        duration = String.valueOf(conversationTime);
                        Log.d(TAG, "📞 [CALL_INFO] Connected call - using conversation duration: " + duration);
                    } else {
                        // This shouldn't happen for truly connected calls, but fallback
                        duration = "1"; // Minimum 1 second for connected calls
                        Log.d(TAG, "📞 [CALL_INFO] Connected call - using fallback duration: " + duration);
                    }
                } else {
                    // For disconnected calls, always set duration to 0
                    duration = "0";
                    Log.d(TAG, "📞 [CALL_INFO] Disconnected call - setting duration to 0");
                }
            } else {
                // Missed call
                startTime = preferences.getLong("RingingStartTime", 0);
                endTime = preferences.getLong("CallEndTime", 0);
                CallLogInfo callInfo = getCallLogs(CallType.MISSED);

                // For missed calls, always set duration to 0
                duration = "0";
                callDirection = didRing ? CallDirection.INCOMING : CallDirection.OUTGOING;
                callStatus = CallStatus.MISSED;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error retrieving call information: " + e.getMessage());
            duration = "0";
            callDirection = CallDirection.NONE;
            callStatus = CallStatus.NONE;
            startTime = 0;
            endTime = 0;
        }

        Log.d(TAG, "📞 [CALL_INFO] Final result - Duration: " + duration + "s, Direction: " + callDirection + ", Status: " + callStatus);
        return new CallInfo(duration, callDirection, startTime, endTime, callStatus);
    }

    /**
     * Enhanced method to determine if an outgoing call was actually connected
     * IMPORTANT: System call logs are authoritative - if duration=0, the call was disconnected
     * We use multiple indicators but always respect the system's final determination
     */
    private boolean determineOutgoingCallConnection(long callLogDuration, long calculatedDuration, long startTime, long endTime) {
        Log.d(TAG, "📞 [CONNECTION_DETECTION] Analyzing outgoing call connection...");

        // Indicator 1: System call log duration (MOST AUTHORITATIVE)
        boolean callLogIndicatesConnection = callLogDuration > 0;
        Log.d(TAG, "📞 [CONNECTION_DETECTION] Call log indicates connection: " + callLogIndicatesConnection + " (duration: " + callLogDuration + "s)");

        // Indicator 2: Check if we detected a connection event during the call
        boolean connectionEventDetected = preferences.getBoolean("OutgoingCallConnected", false);
        long connectionTime = preferences.getLong("OutgoingCallConnectedTime", 0);

        Log.d(TAG, "📞 [CONNECTION_DETECTION] Connection event detected: " + connectionEventDetected);
        if (connectionEventDetected) {
            Log.d(TAG, "📞 [CONNECTION_DETECTION] Connection time: " + new java.util.Date(connectionTime));
        }

        // Indicator 3: Calculated duration with minimum threshold (LEAST RELIABLE)
        boolean calculatedDurationIndicatesConnection = calculatedDuration >= (minimumCallDurationForConnection / 1000);
        Log.d(TAG, "📞 [CONNECTION_DETECTION] Calculated duration indicates connection: " + calculatedDurationIndicatesConnection + " (duration: " + calculatedDuration + "s, threshold: " + (minimumCallDurationForConnection / 1000) + "s)");

        // Indicator 4: Connection duration check (if connection was detected)
        boolean connectionDurationValid = false;
        if (connectionEventDetected && connectionTime > 0 && endTime > connectionTime) {
            long connectionDuration = endTime - connectionTime;
            connectionDurationValid = connectionDuration >= minimumCallDurationForConnection;
            Log.d(TAG, "📞 [CONNECTION_DETECTION] Connection duration valid: " + connectionDurationValid + " (duration: " + (connectionDuration / 1000) + "s)");
        }

        // Decision logic: System call log is authoritative, other indicators are supplementary
        boolean isConnected = false;

        // PRIORITY 1: If system call log shows disconnected (duration=0), respect it
        if (!callLogIndicatesConnection) {
            isConnected = false;
            Log.d(TAG, "📞 [CONNECTION_DETECTION] ❌ System call log shows disconnected (duration=0) - call was not answered");
            if (calculatedDuration >= 10 || connectionEventDetected) {
                Log.d(TAG, "📞 [CONNECTION_DETECTION] ℹ️ Note: App detected activity (calculated=" + calculatedDuration + "s, event=" + connectionEventDetected + ") but respecting authoritative system data");
            }
        }
        // PRIORITY 2: If system call log shows connected (duration>0), confirm with other indicators
        else if (callLogIndicatesConnection) {
            if (connectionEventDetected && connectionDurationValid) {
                // Best case: System says connected AND we detected connection event with valid duration
                isConnected = true;
                Log.d(TAG, "📞 [CONNECTION_DETECTION] ✅ Connection confirmed by system call log + event detection + duration");
            } else if (calculatedDurationIndicatesConnection) {
                // Good case: System says connected AND calculated duration is reasonable
                isConnected = true;
                Log.d(TAG, "📞 [CONNECTION_DETECTION] ✅ Connection confirmed by system call log + calculated duration");
            } else if (calculatedDuration >= 2) {
                // Acceptable case: System says connected AND we have some calculated duration
                isConnected = true;
                Log.d(TAG, "📞 [CONNECTION_DETECTION] ✅ Connection confirmed by system call log + some duration");
            } else {
                // Trust system even if other indicators are weak
                isConnected = true;
                Log.d(TAG, "📞 [CONNECTION_DETECTION] ✅ Connection confirmed by system call log (trusting authoritative source)");
            }
        }
        // PRIORITY 3: Fallback if no call log data (shouldn't happen normally)
        else {
            Log.d(TAG, "📞 [CONNECTION_DETECTION] ⚠️ No system call log data available, using fallback logic");
            if (connectionEventDetected && connectionDurationValid) {
                isConnected = true;
                Log.d(TAG, "📞 [CONNECTION_DETECTION] ✅ Connection confirmed by event detection + duration (fallback)");
            } else {
                isConnected = false;
                Log.d(TAG, "📞 [CONNECTION_DETECTION] ❌ No reliable connection indicators (fallback)");
            }
        }

        Log.d(TAG, "📞 [CONNECTION_DETECTION] Final decision: " + (isConnected ? "CONNECTED" : "DISCONNECTED"));
        return isConnected;
    }

    /**
     * Get call logs from the system with simple retry mechanism
     */
    private CallLogInfo getCallLogs(CallType callType) {
        return getCallLogsWithRetry(callType, 3, 1000); // Simple retry: 3 attempts with 1 second delay
    }

    /**
     * Get call logs with retry mechanism to handle timing issues
     * Note: duration=0 is not a failure - it indicates the call was disconnected
     */
    private CallLogInfo getCallLogsWithRetry(CallType callType, int maxRetries, long delayMs) {
        CallLogInfo lastResult = null;

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            Log.d(TAG, "📞 [CALL_LOG] Attempt " + attempt + "/" + maxRetries + " to fetch call log");

            CallLogInfo result = getCallLogsInternal(callType);
            lastResult = result;

            // If we got a result with a phone number, we have valid data
            if (result != null && result.phoneNumber != null && !result.phoneNumber.isEmpty()) {
                if (!result.phoneDuration.equals("0")) {
                    Log.d(TAG, "✅ [CALL_LOG] Found connected call on attempt " + attempt + ": duration=" + result.phoneDuration + "s");
                } else {
                    Log.d(TAG, "✅ [CALL_LOG] Found disconnected call on attempt " + attempt + ": duration=0s (call was not answered)");
                }
                return result;
            }

            // Only retry if we didn't find any call log entry at all
            if (attempt < maxRetries) {
                Log.d(TAG, "⏳ [CALL_LOG] No call log found, waiting " + delayMs + "ms before retry...");
                try {
                    Thread.sleep(delayMs);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        if (lastResult != null && lastResult.phoneNumber != null && !lastResult.phoneNumber.isEmpty()) {
            Log.d(TAG, "📞 [CALL_LOG] Returning call log data: duration=" + lastResult.phoneDuration + "s");
        } else {
            Log.w(TAG, "⚠️ [CALL_LOG] No call log entry found after " + maxRetries + " attempts");
        }
        return lastResult;
    }

    /**
     * Internal method to fetch call logs with improved accuracy
     */
    private CallLogInfo getCallLogsInternal(CallType callType) {
        String queryFilter = "";
        switch (callType) {
            case INCOMING:
                queryFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.INCOMING_TYPE;
                break;
            case OUTGOING:
                queryFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.OUTGOING_TYPE;
                break;
            case MISSED:
                queryFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.MISSED_TYPE;
                break;
        }

        // Get the call end time for more accurate matching
        long callEndTime = preferences.getLong("CallEndTime", 0);
        long relevantTimestamp = 0;

        switch (callType) {
            case INCOMING:
                relevantTimestamp = preferences.getLong("RingingStartTime", 0);
                break;
            case OUTGOING:
                relevantTimestamp = preferences.getLong("CallDialedStartTime", 0);
                break;
            case MISSED:
                relevantTimestamp = preferences.getLong("RingingStartTime", 0);
                break;
        }

        // Use a wider time window to ensure we don't miss call logs due to timing differences
        // The system might record call logs at slightly different times than our app detects call end
        long timeWindowStart = Math.max(relevantTimestamp - (5 * 60 * 1000), callEndTime - (5 * 60 * 1000));
        long timeWindowEnd = callEndTime + (5 * 60 * 1000); // 5 minutes after call end to handle delays

        // Add time window to query filter if we have a valid timestamp
        if (relevantTimestamp > 0 && callEndTime > 0) {
            queryFilter += " AND " + CallLog.Calls.DATE + " >= " + timeWindowStart +
                          " AND " + CallLog.Calls.DATE + " <= " + timeWindowEnd;
        }

        String querySorter = CallLog.Calls.DATE + " DESC";
        String phoneNumber = "";
        String phoneDuration = "0";
        long callDate = 0;

        try {
            // Check if we have permission to read call logs
            if (checkSelfPermission(android.Manifest.permission.READ_CALL_LOG) != android.content.pm.PackageManager.PERMISSION_GRANTED) {
                Log.e(TAG, "📞 [CALL_LOG] ❌ READ_CALL_LOG permission not granted!");
                return new CallLogInfo("", "0");
            }

            Log.d(TAG, "📞 [CALL_LOG] Querying call logs with filter: " + queryFilter);
            Log.d(TAG, "📞 [CALL_LOG] Time window: " + new java.util.Date(timeWindowStart) + " to " + new java.util.Date(timeWindowEnd));
            Log.d(TAG, "📞 [CALL_LOG] Relevant timestamp: " + new java.util.Date(relevantTimestamp));
            Log.d(TAG, "📞 [CALL_LOG] Call end time: " + new java.util.Date(callEndTime));

            Cursor queryData = getContentResolver().query(CallLog.Calls.CONTENT_URI, null, queryFilter, null, querySorter);

            if (queryData != null && queryData.getCount() > 0) {
                Log.d(TAG, "Found " + queryData.getCount() + " matching call logs");

                // Find the call log entry closest to our relevant timestamp
                long closestTimeDiff = Long.MAX_VALUE;
                int closestPosition = -1;

                queryData.moveToFirst();
                do {
                    long currentCallDate = queryData.getLong(queryData.getColumnIndex(CallLog.Calls.DATE));
                    long timeDiff = Math.abs(currentCallDate - relevantTimestamp);

                    if (timeDiff < closestTimeDiff) {
                        closestTimeDiff = timeDiff;
                        closestPosition = queryData.getPosition();
                        callDate = currentCallDate;
                    }
                } while (queryData.moveToNext());

                // Move to the closest matching call
                if (closestPosition >= 0) {
                    queryData.moveToPosition(closestPosition);
                    phoneNumber = queryData.getString(queryData.getColumnIndex(CallLog.Calls.NUMBER));
                    phoneDuration = queryData.getString(queryData.getColumnIndex(CallLog.Calls.DURATION));

                    // Log the details for debugging
                    Log.d(TAG, "Selected call log: Number=" + phoneNumber +
                          ", Duration=" + phoneDuration +
                          ", Date=" + new java.util.Date(callDate).toString() +
                          ", TimeDiff=" + (closestTimeDiff / 1000) + " seconds");
                }

                queryData.close();
            } else {
                Log.d(TAG, "📞 [CALL_LOG] No matching call logs found in time window, trying broader search...");

                // Debug: Show what call logs are actually available
                debugRecentCallLogs(callType);

                // Fallback: Search without time constraints for recent calls
                String fallbackFilter = "";
                switch (callType) {
                    case INCOMING:
                        fallbackFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.INCOMING_TYPE;
                        break;
                    case OUTGOING:
                        fallbackFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.OUTGOING_TYPE;
                        break;
                    case MISSED:
                        fallbackFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.MISSED_TYPE;
                        break;
                }

                Log.d(TAG, "📞 [CALL_LOG] Fallback query with filter: " + fallbackFilter);
                Cursor fallbackQuery = getContentResolver().query(CallLog.Calls.CONTENT_URI, null, fallbackFilter, null, querySorter);

                if (fallbackQuery != null && fallbackQuery.getCount() > 0) {
                    Log.d(TAG, "📞 [CALL_LOG] Found " + fallbackQuery.getCount() + " recent calls in fallback search");

                    // Process only the first 20 calls to avoid performance issues
                    int processedCount = 0;
                    long bestTimeDiff = Long.MAX_VALUE;
                    String bestNumber = "";
                    String bestDuration = "0";
                    long bestCallDate = 0;

                    fallbackQuery.moveToFirst();
                    do {
                        if (processedCount >= 20) break; // Limit processing to avoid performance issues

                        long currentCallDate = fallbackQuery.getLong(fallbackQuery.getColumnIndex(CallLog.Calls.DATE));
                        String currentNumber = fallbackQuery.getString(fallbackQuery.getColumnIndex(CallLog.Calls.NUMBER));
                        String currentDuration = fallbackQuery.getString(fallbackQuery.getColumnIndex(CallLog.Calls.DURATION));

                        if (currentNumber == null) currentNumber = "";
                        if (currentDuration == null) currentDuration = "0";

                        Log.d(TAG, "📞 [CALL_LOG] Fallback candidate #" + (processedCount + 1) + ": " + currentNumber +
                              ", Duration: " + currentDuration +
                              ", Date: " + new java.util.Date(currentCallDate));

                        // Calculate time difference from both call start and end times
                        long timeDiffFromEnd = Math.abs(currentCallDate - callEndTime);
                        long timeDiffFromStart = Math.abs(currentCallDate - relevantTimestamp);
                        long minTimeDiff = Math.min(timeDiffFromEnd, timeDiffFromStart);

                        // Check phone number matching
                        boolean phoneMatches = isPhoneNumberMatch(currentNumber, lastPhoneNumber);

                        Log.d(TAG, "📞 [CALL_LOG] Analysis: TimeDiff=" + (minTimeDiff / 1000) + "s, PhoneMatch=" + phoneMatches);

                        // Accept calls within 15 minutes that match phone number, or within 5 minutes regardless
                        boolean isGoodMatch = (minTimeDiff <= (15 * 60 * 1000) && phoneMatches) ||
                                            (minTimeDiff <= (5 * 60 * 1000));

                        if (isGoodMatch && minTimeDiff < bestTimeDiff) {
                            bestTimeDiff = minTimeDiff;
                            bestNumber = currentNumber;
                            bestDuration = currentDuration;
                            bestCallDate = currentCallDate;
                            Log.d(TAG, "📞 [CALL_LOG] 🎯 New best match found!");
                        }

                        processedCount++;
                    } while (fallbackQuery.moveToNext());

                    if (!bestNumber.isEmpty()) {
                        phoneNumber = bestNumber;
                        phoneDuration = bestDuration;
                        callDate = bestCallDate;
                        Log.d(TAG, "📞 [CALL_LOG] ✅ Using best fallback match: " + phoneNumber +
                              ", Duration: " + phoneDuration +
                              ", TimeDiff: " + (bestTimeDiff / 1000) + "s");
                    } else {
                        Log.d(TAG, "📞 [CALL_LOG] ❌ No suitable match found in " + processedCount + " candidates");
                    }

                    fallbackQuery.close();
                } else {
                    Log.d(TAG, "📞 [CALL_LOG] No recent calls found in fallback search either");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error querying call logs: " + e.getMessage());
        }

        return new CallLogInfo(phoneNumber, phoneDuration);
    }

    /**
     * Check if two phone numbers match, handling different formats
     */
    private boolean isPhoneNumberMatch(String number1, String number2) {
        if (number1 == null || number2 == null || number1.isEmpty() || number2.isEmpty()) {
            return false;
        }

        // Clean both numbers - keep only digits
        String clean1 = number1.replaceAll("[^0-9]", "");
        String clean2 = number2.replaceAll("[^0-9]", "");

        if (clean1.isEmpty() || clean2.isEmpty()) {
            return false;
        }

        // Direct match
        if (clean1.equals(clean2)) {
            return true;
        }

        // Remove country code and try again
        String withoutCountry1 = clean1.replaceFirst("^(91|\\+91)", "");
        String withoutCountry2 = clean2.replaceFirst("^(91|\\+91)", "");

        if (withoutCountry1.equals(withoutCountry2)) {
            return true;
        }

        // Check if one contains the other (for cases where one has country code, other doesn't)
        if (clean1.length() >= 10 && clean2.length() >= 10) {
            String last10_1 = clean1.substring(clean1.length() - 10);
            String last10_2 = clean2.substring(clean2.length() - 10);
            return last10_1.equals(last10_2);
        }

        return false;
    }

    /**
     * Debug method to show what call logs are actually available
     */
    private void debugRecentCallLogs(CallType callType) {
        try {
            String debugFilter = "";
            switch (callType) {
                case INCOMING:
                    debugFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.INCOMING_TYPE;
                    break;
                case OUTGOING:
                    debugFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.OUTGOING_TYPE;
                    break;
                case MISSED:
                    debugFilter = CallLog.Calls.TYPE + "=" + CallLog.Calls.MISSED_TYPE;
                    break;
            }

            Log.d(TAG, "📞 [DEBUG] Showing recent " + callType + " call logs:");
            Cursor debugQuery = getContentResolver().query(CallLog.Calls.CONTENT_URI, null, debugFilter, null, CallLog.Calls.DATE + " DESC");

            if (debugQuery != null && debugQuery.getCount() > 0) {
                int count = 0;
                debugQuery.moveToFirst();
                do {
                    if (count >= 5) break; // Show only first 5

                    long callDate = debugQuery.getLong(debugQuery.getColumnIndex(CallLog.Calls.DATE));
                    String number = debugQuery.getString(debugQuery.getColumnIndex(CallLog.Calls.NUMBER));
                    String duration = debugQuery.getString(debugQuery.getColumnIndex(CallLog.Calls.DURATION));

                    Log.d(TAG, "📞 [DEBUG] #" + (count + 1) + ": " + number +
                          ", Duration: " + duration + "s" +
                          ", Date: " + new java.util.Date(callDate));

                    count++;
                } while (debugQuery.moveToNext());

                debugQuery.close();
            } else {
                Log.d(TAG, "📞 [DEBUG] No " + callType + " call logs found at all!");
            }
        } catch (Exception e) {
            Log.e(TAG, "📞 [DEBUG] Error in debug query: " + e.getMessage());
        }
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "CallDetectionService onStartCommand");

        // Check if notification channel can be created (handles permission check on Android 13+)
        if (!createNotificationChannel()) {
            Log.e(TAG, "Failed to create notification channel in onStartCommand - stopping service");
            stopSelf();
            return START_NOT_STICKY;
        }

        // If service is restarted, re-initialize the call state listener
        if (telephonyManager == null || phoneStateListener == null) {
            initializeCallStateListener();
        }

        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "CallDetectionService onDestroy");
        super.onDestroy();
        if (telephonyManager != null && phoneStateListener != null) {
            telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_NONE);
        }
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }

    private boolean createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            try {
                // Check for notification permission on Android 13+
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                        Log.e(TAG, "Notification permission not granted. Cannot create notification channel.");
                        return false;
                    }
                }

                NotificationChannel serviceChannel = new NotificationChannel(
                        CHANNEL_ID,
                        "Call Detection Service",
                        NotificationManager.IMPORTANCE_LOW
                );
                NotificationManager manager = getSystemService(NotificationManager.class);
                if (manager != null) {
                    manager.createNotificationChannel(serviceChannel);
                    return true;
                } else {
                    Log.e(TAG, "NotificationManager is null. Cannot create notification channel.");
                    return false;
                }
            } catch (Exception e) {
                Log.e(TAG, "Error creating notification channel: " + e.getMessage());
                return false;
            }
        }
        return true; // On older Android versions, no channel needed
    }

    private Notification createNotification() {
        return new Notification.Builder(this, CHANNEL_ID)
                .setContentTitle("Call Detection Running")
                .setContentText("Monitoring calls in the background")
                .setSmallIcon(R.mipmap.ic_launcher)
                .build();
    }

    /**
     * Check if a service is running
     */
    public static boolean isServiceRunning(Context context, Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Start a timer to detect call end if IDLE state is not triggered
     * This helps with devices that don't reliably fire IDLE state
     */
    private void startCallEndDetectionTimer() {
        // Cancel any existing timer
        stopCallEndDetectionTimer();

        callEndDetectionRunnable = new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "⏰ [CALL_END_TIMER] Timer triggered - checking if call is still active");

                // Check if call is still in progress by querying telephony manager
                if (telephonyManager != null) {
                    int currentState = telephonyManager.getCallState();
                    Log.d(TAG, "⏰ [CALL_END_TIMER] Current telephony state: " + currentState);

                    if (currentState == TelephonyManager.CALL_STATE_IDLE && callInProgress) {
                        Log.d(TAG, "⏰ [CALL_END_TIMER] Call ended but IDLE state was not triggered - manually triggering");
                        handleIdleState(lastPhoneNumber);
                    } else if (currentState != TelephonyManager.CALL_STATE_IDLE) {
                        Log.d(TAG, "⏰ [CALL_END_TIMER] Call still active - restarting timer");
                        // Call is still active, restart the timer
                        startCallEndDetectionTimer();
                    }
                }
            }
        };

        // Start the timer
        if (mainHandler != null) {
            mainHandler.postDelayed(callEndDetectionRunnable, CALL_END_DETECTION_DELAY);
            Log.d(TAG, "⏰ [CALL_END_TIMER] Started call end detection timer (" + CALL_END_DETECTION_DELAY + "ms)");
        }
    }

    /**
     * Stop the call end detection timer
     */
    private void stopCallEndDetectionTimer() {
        if (callEndDetectionRunnable != null && mainHandler != null) {
            mainHandler.removeCallbacks(callEndDetectionRunnable);
            callEndDetectionRunnable = null;
            Log.d(TAG, "⏰ [CALL_END_TIMER] Stopped call end detection timer");
        }
    }
}
