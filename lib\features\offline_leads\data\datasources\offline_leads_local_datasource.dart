import 'package:hive/hive.dart';
import '../models/offline_lead_model.dart';

abstract class OfflineLeadsLocalDataSource {
  Future<void> insertOrUpdateLead(OfflineLeadModel lead);
  Future<void> insertOrUpdateLeads(List<OfflineLeadModel> leads);
  Future<List<OfflineLeadModel>> getAllLeads();
  Future<OfflineLeadModel?> getLeadById(String id);
  Future<OfflineLeadModel?> getLeadByPhoneNumber(String phoneNumber);
  Future<void> deleteLead(String id);
  Future<void> deleteAllLeads();
  Future<List<OfflineLeadModel>> getLeadsUpdatedAfter(DateTime dateTime);
  Future<int> getLeadsCount();
  Future<DateTime?> getLastSyncTime();
  Future<void> updateLastSyncTime(DateTime dateTime);
  Future<void> processIncrementalChanges(List<OfflineLeadModel> leads);
}

class OfflineLeadsLocalDataSourceImpl implements OfflineLeadsLocalDataSource {
  static const String _boxName = 'offline_leads';
  static const String _syncBoxName = 'sync_metadata';
  static const String _lastSyncKey = 'last_sync_time';

  Box<OfflineLeadModel>? _leadsBox;
  Box? _syncBox;

  Future<Box<OfflineLeadModel>> get leadsBox async {
    if (_leadsBox == null || !_leadsBox!.isOpen) {
      _leadsBox = await Hive.openBox<OfflineLeadModel>(_boxName);
    }
    return _leadsBox!;
  }

  Future<Box> get syncBox async {
    if (_syncBox == null || !_syncBox!.isOpen) {
      _syncBox = await Hive.openBox(_syncBoxName);
    }
    return _syncBox!;
  }

  @override
  Future<void> insertOrUpdateLead(OfflineLeadModel lead) async {
    final box = await leadsBox;
    final updatedLead = lead.copyWith(lastSyncedAt: DateTime.now());
    await box.put(lead.id, updatedLead);
  }

  @override
  Future<void> insertOrUpdateLeads(List<OfflineLeadModel> leads) async {
    print('💾 [DATABASE] Starting insertOrUpdateLeads with ${leads.length} leads');
    final box = await leadsBox;
    final now = DateTime.now();

    int insertCount = 0;
    int updateCount = 0;

    // Check for duplicate IDs in the input data
    final inputIds = leads.map((lead) => lead.id).toList();
    final uniqueInputIds = inputIds.toSet();
    if (uniqueInputIds.length != inputIds.length) {
      final duplicateCount = inputIds.length - uniqueInputIds.length;
      print('⚠️ [DATABASE] *** DUPLICATE IDs IN INPUT DATA ***');
      print('⚠️ [DATABASE] Input has ${inputIds.length} records but only ${uniqueInputIds.length} unique IDs');
      print('⚠️ [DATABASE] $duplicateCount records will be lost due to duplicate IDs');

      // Find and log some duplicate IDs
      final seenIds = <String>{};
      final duplicateIds = <String>{};
      for (final id in inputIds) {
        if (seenIds.contains(id)) {
          duplicateIds.add(id);
        } else {
          seenIds.add(id);
        }
      }

      print('⚠️ [DATABASE] Sample duplicate IDs: ${duplicateIds.take(5).join(', ')}');
      if (duplicateIds.length > 5) {
        print('⚠️ [DATABASE] ... and ${duplicateIds.length - 5} more duplicates');
      }
    }

    // Check data breakdown by isLead field
    final leadsInInput = leads.where((item) => item.isLead).length;
    final prospectsInInput = leads.where((item) => !item.isLead).length;
    print('💾 [DATABASE] Input data breakdown:');
    print('💾 [DATABASE] - Leads (isLead=true): $leadsInInput');
    print('💾 [DATABASE] - Prospects (isLead=false): $prospectsInInput');

    final Map<String, OfflineLeadModel> leadsMap = {};
    for (final lead in leads) {
      // Check if lead already exists
      final existingLead = box.get(lead.id);

      if (existingLead != null) {
        updateCount++;
        final entityType = lead.isLead ? 'Lead' : 'Prospect';
        print('🔄 [DATABASE] Updating $entityType: {id: ${lead.id}, name: ${lead.name}, contactNo: ${lead.contactNo}, isLead: ${lead.isLead}}');
      } else {
        insertCount++;
        final entityType = lead.isLead ? 'Lead' : 'Prospect';
        print('➕ [DATABASE] Inserting new $entityType: {id: ${lead.id}, name: ${lead.name}, contactNo: ${lead.contactNo}, isLead: ${lead.isLead}}');
      }

      final updatedLead = lead.copyWith(lastSyncedAt: now);
      leadsMap[lead.id] = updatedLead;

      // Debug: Check if isLead value is preserved after copyWith
      if (lead.isLead != updatedLead.isLead) {
        print('⚠️ [DATABASE] isLead value changed during copyWith! Original: ${lead.isLead}, Updated: ${updatedLead.isLead}');
      }
    }

    print('💾 [DATABASE] About to insert ${leadsMap.length} records into database');

    // Debug: Check a few sample records before insertion
    int sampleCount = 0;
    for (final entry in leadsMap.entries) {
      if (sampleCount < 5) {
        print('🔍 [DATABASE] Sample before insertion: {id: ${entry.value.id}, name: ${entry.value.name}, isLead: ${entry.value.isLead}}');
        sampleCount++;
      } else {
        break;
      }
    }

    await box.putAll(leadsMap);

    // Debug: Check the same sample records after insertion
    sampleCount = 0;
    for (final entry in leadsMap.entries) {
      if (sampleCount < 5) {
        final storedLead = box.get(entry.key);
        print('🔍 [DATABASE] Sample after insertion: {id: ${storedLead?.id}, name: ${storedLead?.name}, isLead: ${storedLead?.isLead}}');
        sampleCount++;
      } else {
        break;
      }
    }

    // Verify what was actually inserted
    final finalLeadsCount = box.values.where((item) => item.isLead).length;
    final finalProspectsCount = box.values.where((item) => !item.isLead).length;

    print('✅ [DATABASE] insertOrUpdateLeads completed: $insertCount inserted, $updateCount updated');
    print('💾 [DATABASE] Total leads in database: ${box.length}');
    print('💾 [DATABASE] Final database breakdown:');
    print('💾 [DATABASE] - Leads (isLead=true): $finalLeadsCount');
    print('💾 [DATABASE] - Prospects (isLead=false): $finalProspectsCount');
  }

  @override
  Future<List<OfflineLeadModel>> getAllLeads() async {
    print('📋 [DATABASE] Getting all leads from database');
    final box = await leadsBox;
    final leads = box.values.toList();
    print('📋 [DATABASE] Retrieved ${leads.length} leads from database');
    return leads;
  }

  @override
  Future<OfflineLeadModel?> getLeadById(String id) async {
    print('🔍 [DATABASE] Getting lead by ID: $id');
    final box = await leadsBox;
    final lead = box.get(id);
    print('🔍 [DATABASE] Lead found: ${lead != null ? "Yes" : "No"}');
    return lead;
  }

  @override
  Future<OfflineLeadModel?> getLeadByPhoneNumber(String phoneNumber) async {
    print('📞 [DATABASE] Searching for lead by phone number: $phoneNumber');
    final box = await leadsBox;

    // Clean the phone number for comparison (remove spaces, dashes, etc.)
    final cleanPhoneNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    print('📞 [DATABASE] Cleaned phone number: $cleanPhoneNumber');

    // Search through all leads
    for (final lead in box.values) {
      // Check primary contact number
      if (lead.contactNo != null) {
        final cleanContactNo = lead.contactNo!.replaceAll(RegExp(r'[^\d+]'), '');
        if (cleanContactNo == cleanPhoneNumber ||
            cleanContactNo.endsWith(cleanPhoneNumber.substring(cleanPhoneNumber.length > 10 ? cleanPhoneNumber.length - 10 : 0)) ||
            cleanPhoneNumber.endsWith(cleanContactNo.substring(cleanContactNo.length > 10 ? cleanContactNo.length - 10 : 0))) {
          print('📞 [DATABASE] Lead found by primary contact: ${lead.name} (${lead.contactNo})');
          return lead;
        }
      }

      // Check alternate contact number
      if (lead.alternateContactNo != null) {
        final cleanAlternateNo = lead.alternateContactNo!.replaceAll(RegExp(r'[^\d+]'), '');
        if (cleanAlternateNo == cleanPhoneNumber ||
            cleanAlternateNo.endsWith(cleanPhoneNumber.substring(cleanPhoneNumber.length > 10 ? cleanPhoneNumber.length - 10 : 0)) ||
            cleanPhoneNumber.endsWith(cleanAlternateNo.substring(cleanAlternateNo.length > 10 ? cleanAlternateNo.length - 10 : 0))) {
          print('📞 [DATABASE] Lead found by alternate contact: ${lead.name} (${lead.alternateContactNo})');
          return lead;
        }
      }
    }

    print('📞 [DATABASE] No lead found for phone number: $phoneNumber');
    return null;
  }

  @override
  Future<void> deleteLead(String id) async {
    print('🗑️ [DATABASE] Deleting lead: $id');
    final box = await leadsBox;
    await box.delete(id);
    print('🗑️ [DATABASE] Lead deleted. Total leads remaining: ${box.length}');
  }

  @override
  Future<void> deleteAllLeads() async {
    print('🗑️ [DATABASE] Deleting all leads from database');
    final box = await leadsBox;
    final count = box.length;
    await box.clear();
    print('🗑️ [DATABASE] Deleted $count leads. Database cleared.');
  }

  @override
  Future<List<OfflineLeadModel>> getLeadsUpdatedAfter(DateTime dateTime) async {
    final box = await leadsBox;
    return box.values.where((lead) {
      return lead.lastModifiedOn != null && lead.lastModifiedOn!.isAfter(dateTime);
    }).toList();
  }

  @override
  Future<int> getLeadsCount() async {
    final box = await leadsBox;
    return box.length;
  }

  @override
  Future<DateTime?> getLastSyncTime() async {
    print('⏰ [DATABASE] Getting last sync time');
    final box = await syncBox;
    final timestamp = box.get(_lastSyncKey);
    if (timestamp is String) {
      final dateTime = DateTime.tryParse(timestamp);
      print('⏰ [DATABASE] Last sync time: ${dateTime?.toIso8601String() ?? "null"}');
      return dateTime;
    }
    print('⏰ [DATABASE] No last sync time found');
    return null;
  }

  @override
  Future<void> updateLastSyncTime(DateTime dateTime) async {
    print('⏰ [DATABASE] Updating last sync time to: ${dateTime.toIso8601String()}');
    final box = await syncBox;
    await box.put(_lastSyncKey, dateTime.toIso8601String());
    print('⏰ [DATABASE] Last sync time updated successfully');
  }

  @override
  Future<void> processIncrementalChanges(List<OfflineLeadModel> leads) async {
    print('🔄 [INCREMENTAL_DATABASE] Starting processIncrementalChanges with ${leads.length} leads');
    final box = await leadsBox;
    final now = DateTime.now();

    int insertCount = 0;
    int updateCount = 0;
    int deleteCount = 0;

    for (final lead in leads) {
      if (lead.isDeleted || lead.isArchived) {
        // Delete the lead if it exists (either deleted or archived)
        final existingLead = box.get(lead.id);
        if (existingLead != null) {
          await box.delete(lead.id);
          deleteCount++;
          final reason = lead.isDeleted ? 'deleted' : 'archived';
          print('🗑️ [INCREMENTAL_DATABASE] Removed lead ($reason): {id: ${lead.id}, name: ${lead.name}, contactNo: ${lead.contactNo}}');
        } else {
          final reason = lead.isDeleted ? 'delete' : 'archive';
          print('⚠️ [INCREMENTAL_DATABASE] Lead to $reason not found: {id: ${lead.id}, name: ${lead.name}}');
        }
      } else {
        // Insert or update the lead
        final existingLead = box.get(lead.id);

        if (existingLead != null) {
          updateCount++;
          print('🔄 [INCREMENTAL_DATABASE] Updating lead: {id: ${lead.id}, name: ${lead.name}, contactNo: ${lead.contactNo}, lastModifiedOn: ${lead.lastModifiedOn?.toIso8601String()}}');
        } else {
          insertCount++;
          print('➕ [INCREMENTAL_DATABASE] Inserting new lead: {id: ${lead.id}, name: ${lead.name}, contactNo: ${lead.contactNo}, lastModifiedOn: ${lead.lastModifiedOn?.toIso8601String()}}');
        }

        // Update the lead with sync timestamp
        final updatedLead = lead.copyWith(lastSyncedAt: now);
        await box.put(lead.id, updatedLead);
      }
    }

    print('✅ [INCREMENTAL_DATABASE] processIncrementalChanges completed:');
    print('✅ [INCREMENTAL_DATABASE] - $insertCount leads inserted');
    print('✅ [INCREMENTAL_DATABASE] - $updateCount leads updated');
    print('✅ [INCREMENTAL_DATABASE] - $deleteCount leads removed (deleted/archived)');
    print('💾 [INCREMENTAL_DATABASE] Total leads in database: ${box.length}');
  }

  Future<void> close() async {
    await _leadsBox?.close();
    await _syncBox?.close();
  }
}
