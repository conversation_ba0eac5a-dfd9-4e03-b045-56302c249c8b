import 'dart:convert';
import 'package:aws_client/secrets_manager_2017_10_17.dart';
import 'package:call_detection/core/services/secret_manager_service/secret_manager_local_service.dart';
import 'package:call_detection/core/services/secret_manager_service/secret_manager_service.dart';

import '../../models/base_url_model.dart';
import '../../models/force_update_model.dart';

class SecretsManagerServiceImpl implements SecretsManagerService {
  final SecretsManager secretsManager;
  final SecretManagerLocalService localStorageService;

  SecretsManagerServiceImpl({
    required String region,
    required String accessKey,
    required String secretKey,
    required this.localStorageService,
  }) : secretsManager = SecretsManager(
    region: region,
    credentials: AwsClientCredentials(
      accessKey: accessKey,
      secretKey: secretKey,
    ),
  );

  @override
  Future<String?> getSecret(String secretId, {String? versionStage}) async {
    try {
      final response = await secretsManager.getSecretValue(secretId: secretId, versionStage: versionStage);

      return response.secretString;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<BaseUrlModel?> getBaseUrl() async {
    try {
      final baseUrls = await getSecret("BaseUrls", versionStage: "AWSCURRENT");
      if (baseUrls == null) return null;
      final parsedJson = jsonDecode(baseUrls) as List<dynamic>;
      final baseUrlModel = BaseUrlModel.fromJson(parsedJson);
      await localStorageService.saveBaseUrl(baseUrlModel);
      return baseUrlModel;
    } catch (ex) {
      return null;
    }
  }

  @override
  Future<ForceUpdateModel?> getForceUpdateAppVersion() async{
    try {
      final forceUpdate = await getSecret("LeadratServicesForceUpdate", versionStage: "AWSCURRENT");
      if(forceUpdate == null) return null;
      final parsedJson = jsonDecode(forceUpdate);
      final forceUpdateModel = ForceUpdateModel.fromJson(parsedJson);
      return forceUpdateModel;
    } catch (ex) {
      return null;
    }
  }

  @override
  Future<String?> getForceUpdateVersion() async {
    try {
      final forceUpdate = await getSecret("LeadratServicesForceUpdate", versionStage: "AWSCURRENT");
      if (forceUpdate == null) return null;

      final parsedJson = jsonDecode(forceUpdate);

      // Check if it's the simple format with just LeadratServicesForceUpdateVersion
      if (parsedJson is Map<String, dynamic> && parsedJson.containsKey('LeadratServicesForceUpdateVersion')) {
        return parsedJson['LeadratServicesForceUpdateVersion'] as String?;
      }

      // If it's the complex format, try to get the appropriate platform version
      final forceUpdateModel = ForceUpdateModel.fromJson(parsedJson);
      return forceUpdateModel.androidForceUpdateVersion ?? forceUpdateModel.iosForceUpdateVersion;
    } catch (ex) {
      print('Error getting force update version: $ex');
      return null;
    }
  }
}