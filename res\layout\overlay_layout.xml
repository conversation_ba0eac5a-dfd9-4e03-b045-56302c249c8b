<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@android:color/white">

    <ImageView
        android:id="@+id/iv_cancel"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_gravity="end"
        android:layout_margin="8dp"
        android:src="@drawable/ic_close" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_phone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_property_details"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_meeting_schedule"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

</LinearLayout> 