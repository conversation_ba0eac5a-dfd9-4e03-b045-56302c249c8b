part of 'call_recordoings_bloc.dart';

class CallRecordingsState {
  PageState? pageState;
  String? message;
  List<String> recordingsList;
  String? directoryPath;
  final bool? isServiceRunning;
  final bool? isGoogleDialer;
  final bool? openTheInBuiltCallRecorder;
  final bool? isFinalSetupDone;
  final bool? isCallRecordingEnabled;

  CallRecordingsState({
    this.pageState,
    this.message,
    this.recordingsList = const [],
    this.directoryPath,
    this.isServiceRunning,
    this.openTheInBuiltCallRecorder,
    this.isFinalSetupDone,
    this.isGoogleDialer,
    this.isCallRecordingEnabled,
  });

  CallRecordingsState copyWith({
    PageState? pageState,
    String? message,
    List<String>? recordingsList,
    String? directoryPath,
    bool? isServiceRunning,
    bool? openTheInBuiltCallRecorder,
    bool? isFinalSetupDone,
    bool? isGoogleDialer,
    bool? isCallRecordingEnabled,
  }) {
    return CallRecordingsState(
      pageState: pageState,
      message: message,
      recordingsList: recordingsList ?? this.recordingsList,
      directoryPath: directoryPath ?? this.directoryPath,
      isServiceRunning: isServiceRunning ?? this.isServiceRunning,
      openTheInBuiltCallRecorder: openTheInBuiltCallRecorder ?? false,
      isFinalSetupDone: isFinalSetupDone ?? this.isFinalSetupDone,
      isGoogleDialer: isGoogleDialer ?? this.isGoogleDialer,
      isCallRecordingEnabled: isCallRecordingEnabled ?? this.isCallRecordingEnabled,
    );
  }
}
