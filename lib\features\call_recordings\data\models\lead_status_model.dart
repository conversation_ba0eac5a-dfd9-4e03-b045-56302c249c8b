import 'package:call_detection/features/call_recordings/data/models/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'lead_status_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: true)
class LeadStatusModel extends BaseModel {
  final String? baseId;
  final int? level;
  final String? status;
  final String? displayName;
  final String? actionName;
  final LeadStatusModel? childType;

  LeadStatusModel({this.baseId, this.level, this.status, this.displayName, this.actionName, this.childType});

  factory LeadStatusModel.fromJson(Map<String, dynamic> json) =>
      _$LeadStatusModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadStatusModelToJson(this);
}