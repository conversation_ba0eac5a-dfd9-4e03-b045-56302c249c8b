import 'package:permission_handler/permission_handler.dart';

class PermissionUtils {
  /// Request permissions required for starting the call detection service
  /// Returns true if all permissions are granted, false otherwise
  static Future<PermissionRequestResult> requestServicePermissions() async {
    List<Permission> requiredPermissions = [
      Permission.ignoreBatteryOptimizations,
      Permission.systemAlertWindow,
      Permission.notification,
    ];

    List<Permission> deniedPermissions = [];
    List<Permission> permanentlyDeniedPermissions = [];

    for (Permission permission in requiredPermissions) {
      PermissionStatus status = await permission.status;

      if (status.isGranted) {
        continue; // Permission already granted
      } else if (status.isPermanentlyDenied) {
        permanentlyDeniedPermissions.add(permission);
        continue;
      }

      // Request the permission
      PermissionStatus result = await permission.request();

      if (result.isDenied) {
        deniedPermissions.add(permission);
      } else if (result.isPermanentlyDenied) {
        permanentlyDeniedPermissions.add(permission);
      }
    }

    return PermissionRequestResult(
      allGranted:
          deniedPermissions.isEmpty && permanentlyDeniedPermissions.isEmpty,
      deniedPermissions: deniedPermissions,
      permanentlyDeniedPermissions: permanentlyDeniedPermissions,
    );
  }

  /// Request permissions required for starting the call detection service with optional display permission
  /// Display over other apps permission is optional - service can work without it
  /// Returns result indicating which permissions were granted/denied
  static Future<PermissionRequestResult>
      requestServicePermissionsWithOptionalDisplay() async {
    List<Permission> requiredPermissions = [
      Permission.ignoreBatteryOptimizations,
      Permission.notification,
    ];

    List<Permission> optionalPermissions = [
      Permission.systemAlertWindow, // Display over other apps - optional
    ];

    List<Permission> deniedPermissions = [];
    List<Permission> permanentlyDeniedPermissions = [];
    List<Permission> optionalDeniedPermissions = [];

    // Handle required permissions
    for (Permission permission in requiredPermissions) {
      PermissionStatus status = await permission.status;

      if (status.isGranted) {
        continue; // Permission already granted
      } else if (status.isPermanentlyDenied) {
        permanentlyDeniedPermissions.add(permission);
        continue;
      }

      // Request the permission
      PermissionStatus result = await permission.request();

      if (result.isDenied) {
        deniedPermissions.add(permission);
      } else if (result.isPermanentlyDenied) {
        permanentlyDeniedPermissions.add(permission);
      }
    }

    // Handle optional permissions
    for (Permission permission in optionalPermissions) {
      PermissionStatus status = await permission.status;

      if (status.isGranted) {
        continue; // Permission already granted
      } else if (status.isPermanentlyDenied) {
        optionalDeniedPermissions.add(permission);
        continue;
      }

      // Request the permission
      PermissionStatus result = await permission.request();

      if (result.isDenied || result.isPermanentlyDenied) {
        optionalDeniedPermissions.add(permission);
      }
    }

    return PermissionRequestResult(
      allGranted:
          deniedPermissions.isEmpty && permanentlyDeniedPermissions.isEmpty,
      deniedPermissions: deniedPermissions,
      permanentlyDeniedPermissions: permanentlyDeniedPermissions,
      optionalDeniedPermissions: optionalDeniedPermissions,
    );
  }

  /// Request permissions required for accessibility features (microphone and audio)
  /// Returns true if all permissions are granted, false otherwise
  static Future<PermissionRequestResult>
      requestAccessibilityPermissions() async {
    List<Permission> requiredPermissions = [
      Permission.microphone,
    ];

    List<Permission> optionalPermissions = [
      Permission.audio,
    ];

    List<Permission> deniedPermissions = [];
    List<Permission> permanentlyDeniedPermissions = [];
    List<Permission> optionalDeniedPermissions = [];

    for (Permission permission in requiredPermissions) {
      PermissionStatus status = await permission.status;

      if (status.isGranted) {
        continue; // Permission already granted
      } else if (status.isPermanentlyDenied) {
        permanentlyDeniedPermissions.add(permission);
        continue;
      }

      // Request the permission
      PermissionStatus result = await permission.request();

      if (result.isDenied) {
        deniedPermissions.add(permission);
      } else if (result.isPermanentlyDenied) {
        permanentlyDeniedPermissions.add(permission);
      }
    }

    // Handle optional permissions
    for (Permission permission in optionalPermissions) {
      PermissionStatus status = await permission.status;

      if (status.isGranted) {
        continue; // Permission already granted
      } else if (status.isPermanentlyDenied) {
        optionalDeniedPermissions.add(permission);
        continue;
      }

      // Request the permission
      PermissionStatus result = await permission.request();

      if (result.isDenied || result.isPermanentlyDenied) {
        optionalDeniedPermissions.add(permission);
      }
    }

    return PermissionRequestResult(
      allGranted:
          deniedPermissions.isEmpty && permanentlyDeniedPermissions.isEmpty,
      deniedPermissions: deniedPermissions,
      permanentlyDeniedPermissions: permanentlyDeniedPermissions,
    );
  }

  /// Request manage external storage permission for path setup
  /// Returns true if permission is granted, false otherwise
  static Future<PermissionRequestResult> requestStoragePermission() async {
    PermissionStatus status = await Permission.manageExternalStorage.status;

    if (status.isGranted) {
      return PermissionRequestResult(allGranted: true);
    } else if (status.isPermanentlyDenied) {
      return PermissionRequestResult(
        allGranted: false,
        permanentlyDeniedPermissions: [Permission.manageExternalStorage],
      );
    }

    // Request the permission
    PermissionStatus result = await Permission.manageExternalStorage.request();

    if (result.isGranted) {
      return PermissionRequestResult(allGranted: true);
    } else if (result.isDenied) {
      return PermissionRequestResult(
        allGranted: false,
        deniedPermissions: [Permission.manageExternalStorage],
      );
    } else if (result.isPermanentlyDenied) {
      return PermissionRequestResult(
        allGranted: false,
        permanentlyDeniedPermissions: [Permission.manageExternalStorage],
      );
    }

    return PermissionRequestResult(allGranted: false);
  }

  /// Get user-friendly permission name
  static String getPermissionName(Permission permission) {
    switch (permission) {
      case Permission.ignoreBatteryOptimizations:
        return "Battery Optimization";
      case Permission.systemAlertWindow:
        return "Display Over Other Apps";
      case Permission.notification:
        return "Notifications";
      case Permission.microphone:
        return "Microphone";
      case Permission.audio:
        return "Audio";
      case Permission.manageExternalStorage:
        return "Manage External Storage";
      default:
        return permission.toString().split('.').last;
    }
  }

  /// Generate error message for denied permissions
  static String generateErrorMessage(PermissionRequestResult result) {
    if (result.allGranted) return "";

    List<String> deniedNames =
        result.deniedPermissions.map((p) => getPermissionName(p)).toList();

    List<String> permanentlyDeniedNames = result.permanentlyDeniedPermissions
        .map((p) => getPermissionName(p))
        .toList();

    String message = "Please allow the following permissions to continue:\n";

    if (deniedNames.isNotEmpty) {
      message += "• ${deniedNames.join(', ')}\n";
    }

    if (permanentlyDeniedNames.isNotEmpty) {
      message +=
          "• ${permanentlyDeniedNames.join(', ')} (Go to Settings > Apps > Leadrat > Permissions)";
    }

    return message.trim();
  }

  /// Generate error message for denied permissions with optional permissions info
  static String generateErrorMessageWithOptional(
      PermissionRequestResult result) {
    if (result.allGranted && result.optionalDeniedPermissions.isEmpty)
      return "";

    List<String> deniedNames =
        result.deniedPermissions.map((p) => getPermissionName(p)).toList();

    List<String> permanentlyDeniedNames = result.permanentlyDeniedPermissions
        .map((p) => getPermissionName(p))
        .toList();

    List<String> optionalDeniedNames = result.optionalDeniedPermissions
        .map((p) => getPermissionName(p))
        .toList();

    if (deniedNames.isEmpty && permanentlyDeniedNames.isEmpty) {
      // Only optional permissions were denied
      if (optionalDeniedNames.isNotEmpty) {
        return "Service started successfully! Note: ${optionalDeniedNames.join(', ')} permission was not granted - overlay will not be shown during calls, but call detection will work normally.";
      }
      return "";
    }

    String message = "Please allow the following permissions to continue:\n";

    if (deniedNames.isNotEmpty) {
      message += "• ${deniedNames.join(', ')}\n";
    }

    if (permanentlyDeniedNames.isNotEmpty) {
      message +=
          "• ${permanentlyDeniedNames.join(', ')} (Go to Settings > Apps > Leadrat > Permissions)\n";
    }

    if (optionalDeniedNames.isNotEmpty) {
      message +=
          "\nOptional: ${optionalDeniedNames.join(', ')} - overlay will not be shown during calls, but call detection will work normally.";
    }

    return message.trim();
  }
}

class PermissionRequestResult {
  final bool allGranted;
  final List<Permission> deniedPermissions;
  final List<Permission> permanentlyDeniedPermissions;
  final List<Permission> optionalDeniedPermissions;

  PermissionRequestResult({
    required this.allGranted,
    this.deniedPermissions = const [],
    this.permanentlyDeniedPermissions = const [],
    this.optionalDeniedPermissions = const [],
  });
}
