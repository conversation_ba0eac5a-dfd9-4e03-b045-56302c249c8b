name: call_detection
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.2+3

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  permission_handler: 11.3.1
  get_it: ^7.7.0
  flutter_bloc: ^8.1.6
  shared_preferences: ^2.0.15
  json_annotation: ^4.8.1
  hive: ^2.2.3
  aws_client: ^0.6.0
  hive_flutter: ^1.1.0
  phone_state: ^2.1.1
  sentry_flutter: ^8.13.2
  flutter_svg: ^2.0.17
  file_picker: ^9.0.2
  fpdart: ^1.1.1
  http: ^1.1.0
  workmanager: ^0.8.0
  url_launcher: ^6.2.4
  package_info_plus: ^8.3.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of yo
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  build_runner: ^2.4.9
  geocoding: ^2.0.5
  json_serializable: ^6.8.0
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
    - assets/images/

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  fonts:
    - family: LexendDecaThin
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Thin.ttf
          weight: 100
          style: normal

    - family: LexendDecaRegular
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Regular.ttf
          weight: 400
          style: normal

    - family: LexendDecaMedium
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Medium.ttf
          weight: 500
          style: normal

    - family: LexendDecaSemiBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-SemiBold.ttf
          weight: 600
          style: normal

    - family: LexendDecaBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Bold.ttf
          weight: 700
          style: normal

    - family: LexendDecaExtraBold
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-ExtraBold.ttf
          weight: 800
          style: normal

    - family: LexendDecaBlack
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Black.ttf
          weight: 900
          style: normal

    - family: LexendDecaLight
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-Light.ttf
          weight: 300
          style: normal

    - family: LexendDecaExtraLight
      fonts:
        - asset: assets/fonts/lexend/LexendDeca-ExtraLight.ttf
          weight: 200
          style: normal
