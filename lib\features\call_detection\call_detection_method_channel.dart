import 'package:flutter/services.dart';
import '../offline_leads/data/services/offline_leads_search_service.dart';

class CallDetectionMethodChannel {
  static const MethodChannel _methodChannel = MethodChannel('call_detection');
  static bool _isInitialized = false;

  static void initialize() {
    if (_isInitialized) {
      print('📞 [CALL_DETECTION_CHANNEL] Already initialized, skipping...');
      return;
    }

    _methodChannel.setMethodCallHandler(_handleMethodCall);
    _isInitialized = true;
    print('📞 [CALL_DETECTION_CHANNEL] Call detection method channel initialized');
  }

  static Future<dynamic> _handleMethodCall(MethodCall call) async {
    switch (call.method) {
      case 'searchLeadByPhoneNumber':
      case 'searchLeadByPhone': // Support both method names
        final Map<String, dynamic> arguments = call.arguments as Map<String, dynamic>;
        final phoneNumber = arguments['phoneNumber'] as String?;
        if (phoneNumber != null) {
          return await _searchLeadByPhoneNumber(phoneNumber);
        } else {
          throw PlatformException(
            code: 'INVALID_ARGUMENT',
            message: 'Phone number is required',
          );
        }
      default:
        throw PlatformException(
          code: 'UNIMPLEMENTED',
          message: 'Method ${call.method} not implemented',
        );
    }
  }

  static Future<Map<String, dynamic>?> _searchLeadByPhoneNumber(String phoneNumber) async {
    try {
      print('📞 [CALL_DETECTION_CHANNEL] Searching for lead by phone number: $phoneNumber');
      
      // Use the search service to find the lead
      final lead = await OfflineLeadsSearchService.searchLeadByPhoneNumber(phoneNumber);
      
      if (lead != null) {
        final entityType = lead.isLead ? 'Lead' : 'Prospect';
        print('📞 [CALL_DETECTION_CHANNEL] $entityType found: ${lead.name} (${lead.contactNo})');
        return {
          'id': lead.id,
          'name': lead.name,
          'contactNo': lead.contactNo,
          'alternateContactNo': lead.alternateContactNo,
          'assignTo': lead.assignTo,
          'isDeleted': lead.isDeleted,
          'isLead': lead.isLead,
          'lastModifiedOn': lead.lastModifiedOn?.toIso8601String(),
        };
      } else {
        print('📞 [CALL_DETECTION_CHANNEL] No lead or prospect found for phone number: $phoneNumber');
        return null;
      }
    } catch (e) {
      print('❌ [CALL_DETECTION_CHANNEL] Error during search: $e');
      throw PlatformException(
        code: 'SEARCH_ERROR',
        message: 'Failed to search lead: $e',
      );
    }
  }

  static void dispose() {
    _isInitialized = false;
    print('📞 [CALL_DETECTION_CHANNEL] Call detection method channel disposed');
  }
}
