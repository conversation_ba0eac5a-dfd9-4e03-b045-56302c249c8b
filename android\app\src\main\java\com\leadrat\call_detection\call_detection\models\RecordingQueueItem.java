package com.leadrat.call_detection.call_detection.models;

import com.leadrat.call_detection.call_detection.enums.CallDirection;
import com.leadrat.call_detection.call_detection.enums.CallStatus;

/**
 * Model class for recording queue items that need to be uploaded to S3
 */
public class RecordingQueueItem {
    public String id;                    // Unique identifier for this queue item
    public String phoneNumber;           // Phone number associated with the call
    public String recordingFilePath;     // Local file path of the recording
    public String base64Data;            // Base64 encoded recording data (if file is processed)
    public String entityId;              // Lead ID or Prospect ID
    public boolean isLead;               // true if this is a lead, false if prospect
    public CallDirection callDirection;  // Direction of the call
    public CallStatus callStatus;        // Status of the call
    public long callStartTime;           // Call start timestamp
    public long callEndTime;             // Call end timestamp
    public double callDuration;          // Call duration in seconds
    public String notes;                 // Additional notes
    public long createdAt;               // When this queue item was created
    public int retryCount;               // Number of upload attempts
    public long lastRetryAt;             // Last retry timestamp
    public String errorMessage;          // Last error message if upload failed
    public boolean isProcessing;         // Flag to prevent concurrent processing
    
    // Upload status
    public enum UploadStatus {
        PENDING,        // Waiting to be uploaded
        PROCESSING,     // Currently being uploaded
        COMPLETED,      // Successfully uploaded
        FAILED,         // Upload failed (will retry)
        ABANDONED       // Too many failures, giving up
    }
    
    public UploadStatus uploadStatus;
    
    public RecordingQueueItem() {
        this.id = java.util.UUID.randomUUID().toString();
        this.createdAt = System.currentTimeMillis();
        this.retryCount = 0;
        this.uploadStatus = UploadStatus.PENDING;
        this.isProcessing = false;
    }
    
    public RecordingQueueItem(String phoneNumber, String recordingFilePath, String entityId, 
                             boolean isLead, CallDirection callDirection, CallStatus callStatus,
                             long callStartTime, long callEndTime, double callDuration, String notes) {
        this();
        this.phoneNumber = phoneNumber;
        this.recordingFilePath = recordingFilePath;
        this.entityId = entityId;
        this.isLead = isLead;
        this.callDirection = callDirection;
        this.callStatus = callStatus;
        this.callStartTime = callStartTime;
        this.callEndTime = callEndTime;
        this.callDuration = callDuration;
        this.notes = notes;
    }
    
    /**
     * Check if this item should be retried
     */
    public boolean shouldRetry() {
        if (uploadStatus == UploadStatus.COMPLETED || uploadStatus == UploadStatus.ABANDONED) {
            return false;
        }
        
        // Don't retry if currently processing
        if (isProcessing) {
            return false;
        }
        
        // Maximum 5 retry attempts
        if (retryCount >= 5) {
            uploadStatus = UploadStatus.ABANDONED;
            return false;
        }
        
        // Exponential backoff: wait 1min, 2min, 4min, 8min, 16min
        long backoffTime = (long) Math.pow(2, retryCount) * 60 * 1000; // in milliseconds
        long timeSinceLastRetry = System.currentTimeMillis() - lastRetryAt;
        
        return timeSinceLastRetry >= backoffTime;
    }
    
    /**
     * Mark as processing
     */
    public void markAsProcessing() {
        this.isProcessing = true;
        this.uploadStatus = UploadStatus.PROCESSING;
        this.lastRetryAt = System.currentTimeMillis();
    }
    
    /**
     * Mark as completed
     */
    public void markAsCompleted() {
        this.isProcessing = false;
        this.uploadStatus = UploadStatus.COMPLETED;
    }
    
    /**
     * Mark as failed and increment retry count
     */
    public void markAsFailed(String errorMessage) {
        this.isProcessing = false;
        this.uploadStatus = UploadStatus.FAILED;
        this.errorMessage = errorMessage;
        this.retryCount++;
        this.lastRetryAt = System.currentTimeMillis();
        
        // Check if we should abandon after too many failures
        if (retryCount >= 5) {
            this.uploadStatus = UploadStatus.ABANDONED;
        }
    }
    
    @Override
    public String toString() {
        return "RecordingQueueItem{" +
                "id='" + id + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", entityId='" + entityId + '\'' +
                ", isLead=" + isLead +
                ", uploadStatus=" + uploadStatus +
                ", retryCount=" + retryCount +
                ", callDuration=" + callDuration +
                '}';
    }
}
