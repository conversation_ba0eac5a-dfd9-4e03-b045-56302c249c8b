package com.leadrat.call_detection.call_detection.models;

import com.leadrat.call_detection.call_detection.enums.CallDirection;
import com.leadrat.call_detection.call_detection.enums.CallStatus;

import java.util.UUID;

public class ProspectCallLogDTO {
    public String prospectId;
    public UUID userId;
    public long callStartTime;
    public long callEndTime;
    public double callDuration;
    public CallDirection callDirection;
    public String notes;
    public CallStatus callStatus;
    public String callRecordingUrl;
    public long callTimestamp;  // When the actual call happened (for lastModifiedOn)
}