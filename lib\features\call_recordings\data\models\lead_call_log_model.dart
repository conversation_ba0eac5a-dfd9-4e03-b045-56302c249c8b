
import 'package:call_detection/core/enums/call_status_enum.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../../core/enums/call_direction_enum.dart';
part 'lead_call_log_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: true)
class LeadCallLogModel {
  final String? leadId;
  final String? userId;
  final CallDirection? callDirection;
  final double? callDuration;
  final DateTime? callStartTime;
  final DateTime? callEndTime;
  final String? notes;
  final CallStatus? callStatus;

  LeadCallLogModel({
      this.leadId, this.userId, this.callDirection, this.callDuration, this.callStartTime, this.callEndTime, this.notes, this.callStatus});

  factory LeadCallLogModel.fromJson(Map<String, dynamic> json) =>
      _$LeadCallLogModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadCallLogModelToJson(this);
}