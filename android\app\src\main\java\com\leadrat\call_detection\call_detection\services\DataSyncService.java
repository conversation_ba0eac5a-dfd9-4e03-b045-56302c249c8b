package com.leadrat.call_detection.call_detection.services;

import android.app.ActivityManager;
import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.ContentValues;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.os.Build;
import android.os.Handler;
import android.os.IBinder;
import android.os.Looper;
import android.util.Log;
import androidx.core.app.NotificationCompat;
import com.leadrat.call_detection.call_detection.MainActivity;
import com.leadrat.call_detection.call_detection.R;
import io.flutter.plugin.common.MethodChannel;
import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.TimeZone;
import org.json.JSONObject;
import org.json.JSONArray;

/**
 * DataSyncService is a foreground service that performs periodic data synchronization
 * with the remote API every 15 minutes. This ensures continuous background sync
 * even when the app is killed or in background.
 */
public class DataSyncService extends Service {
    private static final String TAG = "DataSyncService";
    private static final String CHANNEL_ID = "data_sync_channel";
    private static final int NOTIFICATION_ID = 2;
    private static final long SYNC_INTERVAL = 60000; // 15 minutes in milliseconds

    // API Configuration
    private static final String BASE_URL = "https://prd-mobile.leadrat.com";
    private static final String API_ENDPOINT_LEAD_INCREMENTAL = "/api/v1/lead/changesbasedonlastmodified"; // Lead incremental sync
    private static final String API_ENDPOINT_PROSPECT_INCREMENTAL = "/api/v1/prospect/changesbasedonlastmodified"; // Prospect incremental sync
    private static final String API_ENDPOINT_OFFLINE = "/api/v1/lead/offline"; // Full offline sync API
    private static final int PAGE_SIZE = 500;
    private static final int CONNECTION_TIMEOUT = 30000; // 30 seconds

    // Dynamic values retrieved from SharedPreferences
    private String userId = null;
    private String tenantId = null;

    // SharedPreferences keys
    private static final String PREFS_NAME = "data_sync_prefs";
    private static final String KEY_LAST_SYNC_TIME = "last_sync_time";
    private static final String KEY_TOTAL_LEADS = "total_leads";
    private static final String KEY_SYNC_IN_PROGRESS = "sync_in_progress";
    private static final String KEY_SYNC_TYPE = "sync_type"; // "flutter" or "native"

    private Handler syncHandler;
    private boolean isRunning = false;
    private boolean isSyncInProgress = false; // Prevent concurrent syncs
    private int syncCount = 0;
    private long lastSyncTime = 0;
    private SharedPreferences prefs;

    // Database synchronization objects to prevent concurrent access
    private static final Object HIVE_DB_LOCK = new Object();
    private static final Object NATIVE_DB_LOCK = new Object();

    private final Runnable syncRunnable = new Runnable() {
        @Override
        public void run() {
            if (isRunning) {
                // Clear stale sync states before each periodic sync attempt
                clearStaleSync();
                performSync();
                // Schedule next sync
                syncHandler.postDelayed(this, SYNC_INTERVAL);
            }
        }
    };

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "DataSyncService created");

        // Initialize SharedPreferences
        prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);

        // Load tenant ID and user ID from SharedPreferences
        loadLeadratData();

        // Clear any stale sync states on service start
        clearStaleSync();

        // Create notification channel for Android 8.0+
        createNotificationChannel();

        // Initialize handler for main thread
        syncHandler = new Handler(Looper.getMainLooper());

        // Skip automatic database clearing on service start to preserve existing data
        // The incremental sync will handle updates efficiently without clearing
        Log.d(TAG, "🔧 [SERVICE_INIT] Skipping automatic database cleanup - using incremental sync instead");
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "DataSyncService started");

        // Check if this is a data refresh request
        if (intent != null && "REFRESH_DATA".equals(intent.getAction())) {
            Log.d(TAG, "🔄 [DATA_REFRESH] Received data refresh request");
            loadLeadratData(); // Reload tenant and user data
            return START_STICKY;
        }

        // Start foreground service
        Notification notification = createNotification();
        startForeground(NOTIFICATION_ID, notification);

        // Clear any stale sync states and ensure clean start
        clearStaleSync();

        // Force clear all sync states on service start (aggressive cleanup)
        isSyncInProgress = false;
        setGlobalSyncInProgress(false, "none");
        Log.d(TAG, "🔧 [DATA_SYNC] Force cleared all sync states on service start");

        // Also clear any stale sync start times
        prefs.edit().remove("sync_start_time").apply();
        Log.d(TAG, "🔧 [DATA_SYNC] Cleared stale sync start time");

        // Load LeadRat data (non-blocking)
        new Thread(() -> {
            try {
                loadLeadratData();
            } catch (Exception e) {
                Log.e(TAG, "Error loading LeadRat data: " + e.getMessage());
            }
        }).start();

        // Check and sync any pending temporary storage (already in background thread)
        checkAndSyncTemporaryStorage();

        // Check and sync native database if it has data (already in background thread)
        checkAndSyncNativeDatabase();

        // Check if we need to sync Hive data to native database (already delayed)
        checkAndSyncHiveToNative();

        // Schedule cleanup of native database after Flutter has had time to initialize
        scheduleNativeDatabaseCleanup();

        // Start periodic sync (non-blocking)
        new Thread(() -> {
            try {
                startPeriodicSync();
            } catch (Exception e) {
                Log.e(TAG, "Error starting periodic sync: " + e.getMessage());
            }
        }).start();

        // Return START_STICKY to restart service if killed
        return START_STICKY;
    }

    @Override
    public void onDestroy() {
        Log.d(TAG, "DataSyncService destroyed");
        stopPeriodicSync();
        super.onDestroy();
    }

    @Override
    public IBinder onBind(Intent intent) {
        return null; // This is not a bound service
    }

    /**
     * Start periodic sync operations
     */
    private void startPeriodicSync() {
        if (!isRunning) {
            Log.d(TAG, "Starting periodic sync (every 15 minutes)");
            isRunning = true;

            // Start with a small delay to avoid blocking service startup
            syncHandler.postDelayed(() -> performSync(), 2000); // 2 second delay

            // Schedule periodic execution
            syncHandler.postDelayed(syncRunnable, SYNC_INTERVAL);
        }
    }

    /**
     * Stop periodic sync operations
     */
    private void stopPeriodicSync() {
        if (isRunning) {
            Log.d(TAG, "Stopping periodic sync");
            isRunning = false;
            syncHandler.removeCallbacks(syncRunnable);
        }
    }

    /**
     * Perform actual sync operation - tries Flutter first, falls back to native
     */
    private void performSync() {
        // Clear any stale sync states first
        clearStaleSync();

        // Check if sync is already in progress (both local and global check)
        if (isSyncInProgress || isGlobalSyncInProgress()) {
            String currentSyncType = prefs.getString(KEY_SYNC_TYPE, "unknown");
            long syncStartTime = prefs.getLong("sync_start_time", 0);
            long timeDiff = System.currentTimeMillis() - syncStartTime;

            // If sync has been running for more than 3 minutes, force clear it
            if (timeDiff > 3 * 60 * 1000) {
                Log.w(TAG, "⚠️ [DATA_SYNC] Sync stuck for " + (timeDiff / 1000) + " seconds - force clearing and proceeding");
                isSyncInProgress = false;
                setGlobalSyncInProgress(false, "none");
            } else {
                Log.w(TAG, "⚠️ [DATA_SYNC] Sync already in progress (" + currentSyncType + ") for " + (timeDiff / 1000) + " seconds, skipping this request");
                return;
            }
        }

        try {
            isSyncInProgress = true;
            setGlobalSyncInProgress(true, "checking");
            syncCount++;
            lastSyncTime = System.currentTimeMillis();

            Log.d(TAG, "🔄 [DATA_SYNC] Starting sync operation #" + syncCount);
            Log.d(TAG, "🔄 [DATA_SYNC] Sync time: " + new java.util.Date(lastSyncTime));

            // Check if Flutter is available and active
            boolean isFlutterAvailable = isFlutterAvailable();

            if (isFlutterAvailable) {
                Log.d(TAG, "🔄 [DATA_SYNC] Flutter is available - attempting Flutter sync...");
                setGlobalSyncInProgress(true, "flutter");

                MainActivity mainActivity = MainActivity.getInstance();
                MethodChannel methodChannel = mainActivity.getDataSyncMethodChannel();

                try {
                    // Call Flutter method to perform sync
                    methodChannel.invokeMethod("performBackgroundSync", null, new MethodChannel.Result() {
                        @Override
                        public void success(Object result) {
                            Log.d(TAG, "✅ [DATA_SYNC] Flutter sync completed successfully: " + result);
                            updateNotification("Syncing the data" + new java.text.SimpleDateFormat("HH:mm").format(new java.util.Date()));
                            isSyncInProgress = false; // Reset flag
                            setGlobalSyncInProgress(false, "none");

                            // Trigger Hive to native sync after successful data sync
                            Log.d(TAG, "🔄 [DATA_SYNC] Triggering Hive to native sync after Flutter sync completion");

                            // Also check current database status before triggering sync (in background)
                            new Thread(() -> checkDatabaseStatus()).start();

                            triggerHiveToNativeSync();
                        }

                        @Override
                        public void error(String errorCode, String errorMessage, Object errorDetails) {
                            Log.e(TAG, "❌ [DATA_SYNC] Flutter sync failed: " + errorCode + " - " + errorMessage);
                            updateNotification("Sync error: " + errorMessage);
                            isSyncInProgress = false; // Reset flag

                            // Fall back to native sync
                            Log.d(TAG, "🔄 [DATA_SYNC] Falling back to native sync...");
                            try {
                                setGlobalSyncInProgress(true, "native");
                                performNativeSync();
                            } catch (Exception e) {
                                Log.e(TAG, "❌ [DATA_SYNC] Native sync fallback also failed: " + e.getMessage());
                                isSyncInProgress = false;
                                setGlobalSyncInProgress(false, "none");
                            }
                        }

                        @Override
                        public void notImplemented() {
                            Log.e(TAG, "❌ [DATA_SYNC] Flutter sync method not implemented");
                            updateNotification("Sync error: Method not implemented");
                            isSyncInProgress = false; // Reset flag

                            // Fall back to native sync
                            Log.d(TAG, "🔄 [DATA_SYNC] Falling back to native sync...");
                            try {
                                setGlobalSyncInProgress(true, "native");
                                performNativeSync();
                            } catch (Exception e) {
                                Log.e(TAG, "❌ [DATA_SYNC] Native sync fallback also failed: " + e.getMessage());
                                isSyncInProgress = false;
                                setGlobalSyncInProgress(false, "none");
                            }
                        }
                    });
                    return; // Exit here if Flutter sync is attempted successfully
                } catch (Exception e) {
                    Log.e(TAG, "❌ [DATA_SYNC] Exception calling Flutter sync: " + e.getMessage());
                    Log.d(TAG, "🔄 [DATA_SYNC] Falling back to native sync...");
                }
            } else {
                Log.d(TAG, "🔄 [DATA_SYNC] Flutter is not available - using native sync...");
            }

            // Use native sync
            setGlobalSyncInProgress(true, "native");
            performNativeSync();

        } catch (Exception e) {
            Log.e(TAG, "❌ [DATA_SYNC] Error during sync operation: " + e.getMessage(), e);
            updateNotification("Sync error: " + e.getMessage());
            isSyncInProgress = false; // Reset flag on error
            setGlobalSyncInProgress(false, "none");
        }
    }

    /**
     * Check if Flutter is available and can handle method calls
     */
    private boolean isFlutterAvailable() {
        try {
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity == null) {
                Log.d(TAG, "🔍 [FLUTTER_CHECK] MainActivity instance is null");
                return false;
            }

            MethodChannel methodChannel = mainActivity.getDataSyncMethodChannel();
            if (methodChannel == null) {
                Log.d(TAG, "🔍 [FLUTTER_CHECK] MethodChannel is null");
                return false;
            }

            // Check if Flutter engine is ready using MainActivity's public method
            if (!mainActivity.isFlutterEngineReady()) {
                Log.d(TAG, "🔍 [FLUTTER_CHECK] Flutter engine is not ready");
                return false;
            }

            Log.d(TAG, "🔍 [FLUTTER_CHECK] Flutter is available and ready");
            return true;

        } catch (Exception e) {
            Log.d(TAG, "🔍 [FLUTTER_CHECK] Flutter availability check failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Perform native sync operation when Flutter is not available
     */
    private void performNativeSync() {
        new Thread(() -> {
            try {
                Log.d(TAG, "🔄 [NATIVE_SYNC] Starting native sync operation...");
                long startTime = System.currentTimeMillis();

                // Get last sync time
                String lastSyncTimeStr = prefs.getString(KEY_LAST_SYNC_TIME, null);
                Log.d(TAG, "🔄 [NATIVE_SYNC] Last sync time: " + (lastSyncTimeStr != null ? lastSyncTimeStr : "Never synced"));

                // Always use incremental sync for Native sync (Flutter handles initial sync)
                int totalLeads = 0;
                String syncFromTime = lastSyncTimeStr;

                if (lastSyncTimeStr == null) {
                    // No previous sync time - use a recent time range (last 24 hours)
                    // This assumes Flutter has already done the initial full sync
                    long twentyFourHoursAgo = System.currentTimeMillis() - (24 * 60 * 60 * 1000);
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US);
                    sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
                    syncFromTime = sdf.format(new Date(twentyFourHoursAgo));
                    Log.d(TAG, "🔄 [NATIVE_SYNC] No previous sync time - using last 24 hours as fallback: " + syncFromTime);
                } else {
                    Log.d(TAG, "🔄 [NATIVE_SYNC] Using stored last sync time: " + syncFromTime);
                }

                // Always use incremental sync API
                Log.d(TAG, "🔄 [NATIVE_SYNC] Using incremental sync API (Native sync never uses full sync)");
                totalLeads = performIncrementalSync(syncFromTime);

                // Update sync time and stats
                String currentTime = getCurrentISOTime();
                prefs.edit()
                    .putString(KEY_LAST_SYNC_TIME, currentTime)
                    .putInt(KEY_TOTAL_LEADS, totalLeads)
                    .apply();

                long duration = System.currentTimeMillis() - startTime;
                Log.d(TAG, "✅ [NATIVE_SYNC] Sync completed successfully after " + (duration / 1000) + "s");
                // Log.d(TAG, "✅ [NATIVE_SYNC] Total pages processed: " + (currentPage - 1) + ", Total leads: " + totalLeads);

                // Create final copy for lambda
                final int finalTotalLeads = totalLeads;

                // Update notification on main thread
                syncHandler.post(() -> {
                    updateNotification("sync completed: " + finalTotalLeads + " leads");
                    isSyncInProgress = false; // Reset flag after successful native sync
                    setGlobalSyncInProgress(false, "none");

                    // Trigger Hive to native sync after successful native sync
                    triggerHiveToNativeSync();
                });

            } catch (Exception e) {
                Log.e(TAG, "❌ [NATIVE_SYNC] Native sync failed: " + e.getMessage(), e);
                syncHandler.post(() -> {
                    updateNotification("sync failed: " + e.getMessage());
                    isSyncInProgress = false; // Reset flag after failed native sync
                    setGlobalSyncInProgress(false, "none");
                });
            }
        }).start();
    }

    /**
     * Perform incremental sync using changes API for both leads and prospects
     */
    private int performIncrementalSync(String lastSyncTimeStr) {
        int totalChanges = 0;

        Log.d(TAG, "🔄 [INCREMENTAL_SYNC] Starting incremental sync operation...");
        Log.d(TAG, "🔄 [INCREMENTAL_SYNC] Last sync time: " + lastSyncTimeStr);

        // Parse last sync time and create current time
        String currentTimeStr = getCurrentISOTime();
        Log.d(TAG, "🔄 [INCREMENTAL_SYNC] Date range: " + lastSyncTimeStr + " to " + currentTimeStr);

        // First, fetch lead incremental changes
        Log.d(TAG, "🔄 [INCREMENTAL_SYNC] ========== STEP 1: FETCHING LEAD CHANGES ==========");
        int leadChanges = performIncrementalSyncForType("lead", API_ENDPOINT_LEAD_INCREMENTAL, lastSyncTimeStr, currentTimeStr);
        totalChanges += leadChanges;
        Log.d(TAG, "🔄 [INCREMENTAL_SYNC] Lead changes completed: " + leadChanges + " changes");

        // Then, fetch prospect incremental changes
        Log.d(TAG, "🔄 [INCREMENTAL_SYNC] ========== STEP 2: FETCHING PROSPECT CHANGES ==========");
        int prospectChanges = performIncrementalSyncForType("prospect", API_ENDPOINT_PROSPECT_INCREMENTAL, lastSyncTimeStr, currentTimeStr);
        totalChanges += prospectChanges;
        Log.d(TAG, "🔄 [INCREMENTAL_SYNC] Prospect changes completed: " + prospectChanges + " changes");

        Log.d(TAG, "✅ [INCREMENTAL_SYNC] Incremental sync completed. Total changes: " + totalChanges + " (leads: " + leadChanges + ", prospects: " + prospectChanges + ")");
        return totalChanges;
    }

    /**
     * Perform incremental sync for a specific type (lead or prospect)
     */
    private int performIncrementalSyncForType(String type, String apiEndpoint, String lastSyncTimeStr, String currentTimeStr) {
        int totalChanges = 0;
        int currentPage = 1;
        boolean hasMoreData = true;

        Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "] Starting " + type + " incremental sync...");

        while (hasMoreData) {
            Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "] Fetching " + type + " page " + currentPage + " (pageSize: " + PAGE_SIZE + ")...");

            try {
                String apiResponse = makeIncrementalSyncApiCall(type, apiEndpoint, lastSyncTimeStr, currentTimeStr, currentPage, PAGE_SIZE);
                if (apiResponse != null) {
                    int changesInPage = processIncrementalApiResponse(apiResponse, type);
                    totalChanges += changesInPage;

                    Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "] " + type + " page " + currentPage + " processed: " + changesInPage + " changes");

                    // If we got fewer changes than page size, we've reached the end
                    if (changesInPage < PAGE_SIZE) {
                        hasMoreData = false;
                        Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "] Reached end of " + type + " data (page " + currentPage + ")");
                    } else {
                        currentPage++;
                    }
                } else {
                    Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "] API call failed for " + type + " page " + currentPage);
                    hasMoreData = false;
                }
            } catch (Exception e) {
                Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "] Error processing " + type + " page " + currentPage + ": " + e.getMessage());
                hasMoreData = false;
            }
        }

        Log.d(TAG, "✅ [INCREMENTAL_" + type.toUpperCase() + "] " + type + " incremental sync completed. Total changes: " + totalChanges);
        return totalChanges;
    }

    /**
     * Check if any sync operation is in progress globally
     */
    private boolean isGlobalSyncInProgress() {
        return prefs.getBoolean(KEY_SYNC_IN_PROGRESS, false);
    }

    /**
     * Set global sync state to coordinate between Flutter and Native sync
     */
    private void setGlobalSyncInProgress(boolean inProgress, String syncType) {
        prefs.edit()
            .putBoolean(KEY_SYNC_IN_PROGRESS, inProgress)
            .putString(KEY_SYNC_TYPE, syncType)
            .putLong("sync_start_time", System.currentTimeMillis())
            .apply();

        Log.d(TAG, "🔄 [SYNC_STATE] Global sync state: " + (inProgress ? "IN_PROGRESS" : "IDLE") + " (type: " + syncType + ")");
    }

    /**
     * Clear stale sync states (if sync was interrupted)
     */
    private void clearStaleSync() {
        long syncStartTime = prefs.getLong("sync_start_time", 0);
        long currentTime = System.currentTimeMillis();
        long timeDiff = currentTime - syncStartTime;

        // If sync has been "in progress" for more than 5 minutes, consider it stale
        // Reduced from 10 minutes to 5 minutes for faster recovery
        if (isGlobalSyncInProgress() && timeDiff > 5 * 60 * 1000) {
            Log.w(TAG, "⚠️ [SYNC_STATE] Clearing stale sync state (was in progress for " + (timeDiff / 1000) + " seconds)");
            setGlobalSyncInProgress(false, "none");
            isSyncInProgress = false; // Also clear local sync flag
        } else if (isGlobalSyncInProgress()) {
            Log.d(TAG, "🔍 [SYNC_STATE] Sync in progress for " + (timeDiff / 1000) + " seconds (not stale yet)");
        }

        // Also clear local sync flag if it's been set for too long
        if (isSyncInProgress && timeDiff > 5 * 60 * 1000) {
            Log.w(TAG, "⚠️ [SYNC_STATE] Clearing stale local sync flag");
            isSyncInProgress = false;
        }
    }

    /**
     * Load tenant ID and user ID from SharedPreferences with comprehensive fallback
     */
    private void loadLeadratData() {
        try {
            Log.d(TAG, "🔧 [LEADRAT_DATA] Loading LeadRat data from SharedPreferences...");

            // Try multiple SharedPreferences sources
            SharedPreferences[] prefsSources = {
                getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE),
                android.preference.PreferenceManager.getDefaultSharedPreferences(this),
                getSharedPreferences("leadrat_prefs", Context.MODE_PRIVATE)
            };

            // List of possible keys for tenant ID
            String[] tenantKeys = {
                "flutter.LeadratGlobal_TenantId",
                "flutter.TenantId",
                "flutter.domain",
                "LeadratGlobal_TenantId",
                "TenantId",
                "domain"
            };

            // List of possible keys for user ID
            String[] userKeys = {
                "flutter.LeadratGlobal_UserId",
                "flutter.UserId",
                "flutter.user_id",
                "LeadratGlobal_UserId",
                "UserId",
                "user_id"
            };

            // Try to find tenant ID
            for (SharedPreferences prefs : prefsSources) {
                if (tenantId == null || tenantId.isEmpty() || "null".equals(tenantId)) {
                    for (String key : tenantKeys) {
                        String value = prefs.getString(key, null);
                        if (value != null && !value.isEmpty() && !"null".equals(value)) {
                            tenantId = value;
                            Log.d(TAG, "🔧 [LEADRAT_DATA] Found tenant ID from key '" + key + "': " + tenantId);
                            break;
                        }
                    }
                }
            }

            // Try to find user ID
            for (SharedPreferences prefs : prefsSources) {
                if (userId == null || userId.isEmpty() || "null".equals(userId)) {
                    for (String key : userKeys) {
                        String value = prefs.getString(key, null);
                        if (value != null && !value.isEmpty() && !"null".equals(value)) {
                            userId = value;
                            Log.d(TAG, "🔧 [LEADRAT_DATA] Found user ID from key '" + key + "': " + userId);
                            break;
                        }
                    }
                }
            }

            // Debug: Log all available keys from FlutterSharedPreferences
            SharedPreferences flutterPrefs = getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE);
            Log.d(TAG, "🔍 [LEADRAT_DATA] Available keys in FlutterSharedPreferences:");
            for (String key : flutterPrefs.getAll().keySet()) {
                if (key.toLowerCase().contains("tenant") || key.toLowerCase().contains("user") ||
                    key.toLowerCase().contains("domain") || key.toLowerCase().contains("leadrat")) {
                    String value = flutterPrefs.getString(key, "");
                    Log.d(TAG, "🔍 [LEADRAT_DATA] - " + key + " = " + value);
                }
            }

            Log.d(TAG, "🔧 [LEADRAT_DATA] Final result:");
            Log.d(TAG, "🔧 [LEADRAT_DATA] - Tenant ID: " + (tenantId != null ? tenantId : "null"));
            Log.d(TAG, "🔧 [LEADRAT_DATA] - User ID: " + (userId != null ? userId : "null"));

            if (tenantId == null || userId == null || tenantId.isEmpty() || userId.isEmpty() ||
                "null".equals(tenantId) || "null".equals(userId)) {
                Log.w(TAG, "⚠️ [LEADRAT_DATA] Missing tenant ID or user ID - API calls may fail");
                Log.w(TAG, "⚠️ [LEADRAT_DATA] Please ensure LeadRat app is logged in and data is available");
            } else {
                Log.d(TAG, "✅ [LEADRAT_DATA] Successfully loaded tenant and user data");
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [LEADRAT_DATA] Error loading LeadRat data: " + e.getMessage());
        }
    }

    /**
     * Make API call to fetch incremental changes for specific type
     */
    private String makeIncrementalSyncApiCall(String type, String apiEndpoint, String dateRangeFrom, String dateRangeTo, int page, int pageSize) {
        try {
            // Reload data before each API call to ensure we have latest values
            loadLeadratData();

            // Check if we have required data
            if (userId == null || userId.isEmpty() || "null".equals(userId)) {
                Log.e(TAG, "❌ [INCREMENTAL_API] User ID is required but not available");
                return null;
            }
            if (tenantId == null || tenantId.isEmpty() || "null".equals(tenantId)) {
                Log.e(TAG, "❌ [INCREMENTAL_API] Tenant ID is required but not available");
                return null;
            }

            // Get shouldViewOnlyAssigned with automatic global settings refresh
            boolean shouldViewOnlyAssigned = getShouldViewOnlyAssignedWithRefresh();

            String urlString = BASE_URL + apiEndpoint +
                "?DateRangeFrom=" + java.net.URLEncoder.encode(dateRangeFrom, "UTF-8") +
                "&DateRangeTo=" + java.net.URLEncoder.encode(dateRangeTo, "UTF-8") +
                "&UserId=" + userId +
                "&PageNumber=" + page +
                "&PageSize=" + pageSize +
                "&SendOnlyAssignedLeads=" + shouldViewOnlyAssigned;

            Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "_API] Starting " + type + " incremental sync API request...");
            Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "_API] URL: " + urlString);
            Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "_API] Headers: {accept: application/json, tenant: " + tenantId + "}");
            Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "_API] Parameters: {DateRangeFrom: " + dateRangeFrom + ", DateRangeTo: " + dateRangeTo + ", UserId: " + userId + ", PageNumber: " + page + ", PageSize: " + pageSize + ", SendOnlyAssignedLeads: " + shouldViewOnlyAssigned + "}");

            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // Set request properties
            connection.setRequestMethod("GET");
            connection.setRequestProperty("accept", "application/json");
            connection.setRequestProperty("tenant", tenantId);
            connection.setConnectTimeout(CONNECTION_TIMEOUT);
            connection.setReadTimeout(CONNECTION_TIMEOUT);

            long apiStartTime = System.currentTimeMillis();
            int responseCode = connection.getResponseCode();
            long apiDuration = System.currentTimeMillis() - apiStartTime;

            Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "_API] Response code: " + responseCode + " (took " + apiDuration + "ms)");

            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;

                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                String responseBody = response.toString();
                Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "_API] Response received: " + responseBody.length() + " characters");

                return responseBody;
            } else {
                Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "_API] HTTP error: " + responseCode);
                return null;
            }

        } catch (IOException e) {
            Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "_API] Network error: " + e.getMessage());
            return null;
        } catch (Exception e) {
            Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "_API] Unexpected error: " + e.getMessage());
            return null;
        }
    }

    /**
     * Process incremental API response and extract change count
     */
    private int processIncrementalApiResponse(String jsonResponse, String type) {
        try {
            JSONObject response = new JSONObject(jsonResponse);

            // Check if response has the expected structure
            if (response.has("data")) {
                JSONArray dataArray = response.getJSONArray("data");
                int changeCount = dataArray.length();

                Log.d(TAG, "📋 [INCREMENTAL_" + type.toUpperCase() + "] Processed " + changeCount + " " + type + " changes from API response");

                if (changeCount > 0) {
                    // Check if Flutter is available for incremental changes
                    MainActivity mainActivity = MainActivity.getInstance();
                    MethodChannel methodChannel = null;
                    if (mainActivity != null) {
                        methodChannel = mainActivity.getDataSyncMethodChannel();
                    }

                    if (methodChannel != null && isFlutterAvailable()) {
                        // App is running - use Flutter to process incremental changes
                        Log.d(TAG, "💾 [INCREMENTAL_" + type.toUpperCase() + "] App is running - processing " + type + " incremental changes via Flutter...");
                        processIncrementalChangesViaFlutter(methodChannel, dataArray, type);
                    } else {
                        // App is killed - process incremental changes directly to native SQLite database
                        Log.d(TAG, "💾 [INCREMENTAL_" + type.toUpperCase() + "] App is killed - processing " + type + " incremental changes to native database...");
                        processIncrementalChangesToNativeDatabase(dataArray, type);
                    }
                }

                // Process incremental changes (insert/update/delete based on isDeleted or isArchived flags)
                for (int i = 0; i < dataArray.length(); i++) {
                    JSONObject change = dataArray.getJSONObject(i);
                    boolean isDeleted = change.optBoolean("isDeleted", false);
                    boolean isArchived = change.optBoolean("isArchived", false);
                    String entityId = change.optString("id", "");
                    String entityName = change.optString("name", "");

                    if (isDeleted || isArchived) {
                        String reason = isDeleted ? "DELETE" : "ARCHIVE";
                        Log.d(TAG, "📋 [INCREMENTAL_" + type.toUpperCase() + "] Change " + (i + 1) + ": " + reason + " - " + entityName + " (ID: " + entityId + ")");
                    } else {
                        Log.d(TAG, "📋 [INCREMENTAL_" + type.toUpperCase() + "] Change " + (i + 1) + ": UPSERT - " + entityName + " (ID: " + entityId + ")");
                    }
                }

                return changeCount;
            } else {
                Log.w(TAG, "⚠️ [INCREMENTAL_" + type.toUpperCase() + "] API response missing 'data' field");
                return 0;
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "] Error processing incremental API response: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Process incremental changes via Flutter when app is running
     */
    private void processIncrementalChangesViaFlutter(MethodChannel methodChannel, JSONArray changesArray, String type) {
        try {
            Log.d(TAG, "💾 [INCREMENTAL_" + type.toUpperCase() + "_FLUTTER] Processing " + changesArray.length() + " " + type + " incremental changes via Flutter...");

            // Convert JSONArray to List for Flutter
            List<Map<String, Object>> changesList = new ArrayList<>();
            for (int i = 0; i < changesArray.length(); i++) {
                JSONObject change = changesArray.getJSONObject(i);
                Map<String, Object> changeMap = new HashMap<>();

                // Convert JSONObject to Map
                Iterator<String> keys = change.keys();
                while (keys.hasNext()) {
                    String key = keys.next();
                    changeMap.put(key, change.get(key));
                }
                changesList.add(changeMap);
            }

            // Prepare arguments with type information
            Map<String, Object> arguments = new HashMap<>();
            arguments.put("changes", changesList);
            arguments.put("type", type);

            // Call Flutter method to process incremental changes
            methodChannel.invokeMethod("processIncrementalChanges", arguments, new MethodChannel.Result() {
                @Override
                public void success(Object result) {
                    Log.d(TAG, "✅ [INCREMENTAL_" + type.toUpperCase() + "_FLUTTER] Successfully processed " + type + " incremental changes via Flutter");
                }

                @Override
                public void error(String errorCode, String errorMessage, Object errorDetails) {
                    Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "_FLUTTER] Error processing " + type + " incremental changes via Flutter: " + errorMessage);
                }

                @Override
                public void notImplemented() {
                    Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "_FLUTTER] Flutter " + type + " incremental changes method not implemented");
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "_FLUTTER] Error processing " + type + " incremental changes via Flutter: " + e.getMessage());
        }
    }

    /**
     * Process incremental changes to native database when app is killed
     */
    private void processIncrementalChangesToNativeDatabase(JSONArray changesArray, String type) {
        try {
            Log.d(TAG, "💾 [INCREMENTAL_" + type.toUpperCase() + "_NATIVE] Processing " + changesArray.length() + " " + type + " incremental changes to native database...");

            NativeLeadsDbHelper dbHelper = new NativeLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            db.beginTransaction();
            try {
                int upsertCount = 0;
                int deleteCount = 0;

                for (int i = 0; i < changesArray.length(); i++) {
                    JSONObject change = changesArray.getJSONObject(i);
                    boolean isDeleted = change.optBoolean("isDeleted", false);
                    boolean isArchived = change.optBoolean("isArchived", false);
                    String entityId = change.optString("id", "");

                    if (isDeleted || isArchived) {
                        // Delete the entity (either deleted or archived)
                        int deletedRows = db.delete(NativeLeadsDbHelper.TABLE_LEADS, "id = ?", new String[]{entityId});
                        if (deletedRows > 0) {
                            deleteCount++;
                            String reason = isDeleted ? "deleted" : "archived";
                            Log.d(TAG, "🗑️ [INCREMENTAL_" + type.toUpperCase() + "_NATIVE] Removed " + type + " (" + reason + "): " + entityId);
                        }
                    } else {
                        // Insert or update the entity
                        ContentValues values = new ContentValues();
                        values.put("id", change.optString("id", ""));
                        values.put("name", change.optString("name", ""));
                        values.put("contactNo", change.optString("contactNo", ""));
                        values.put("alternateContactNo", change.optString("alternateContactNo", ""));
                        values.put("assignTo", change.optString("assignTo", ""));
                        values.put("isDeleted", change.optBoolean("isDeleted", false) ? 1 : 0);
                        values.put("isArchived", change.optBoolean("isArchived", false) ? 1 : 0);
                        values.put("lastModifiedOn", change.optString("lastModifiedOn", ""));
                        values.put("lastSyncedAt", getCurrentISOTime());

                        // Add isLead field to distinguish between leads and prospects
                        values.put("isLead", "lead".equals(type) ? 1 : 0);

                        // Use INSERT OR REPLACE for upsert functionality
                        long result = db.insertWithOnConflict(NativeLeadsDbHelper.TABLE_LEADS, null, values, SQLiteDatabase.CONFLICT_REPLACE);
                        if (result != -1) {
                            upsertCount++;
                            Log.d(TAG, "💾 [INCREMENTAL_" + type.toUpperCase() + "_NATIVE] Upserted " + type + ": " + change.optString("name", "") + " (ID: " + entityId + ")");
                        }
                    }
                }

                db.setTransactionSuccessful();
                Log.d(TAG, "✅ [INCREMENTAL_" + type.toUpperCase() + "_NATIVE] Successfully processed " + type + " incremental changes: " + upsertCount + " upserts, " + deleteCount + " deletes");

            } finally {
                db.endTransaction();
                db.close();
            }

            // Update metadata
            updateNativeDatabaseMetadata();

            // IMPORTANT: Trigger incremental sync to Hive Native DB for call detection
            // This ensures call detection can find newly added/updated leads/prospects
            Log.d(TAG, "🔄 [INCREMENTAL_" + type.toUpperCase() + "_NATIVE] Triggering incremental sync to Hive Native DB for call detection...");
            triggerIncrementalSyncToHiveNative(changesArray, type);

        } catch (Exception e) {
            Log.e(TAG, "❌ [INCREMENTAL_" + type.toUpperCase() + "_NATIVE] Error processing " + type + " incremental changes to native database: " + e.getMessage());
        }
    }

    /**
     * Update Hive Native database metadata after incremental changes
     */
    private void updateHiveNativeDatabaseMetadata() {
        try {
            HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            // Get current count from database
            Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + HiveLeadsDbHelper.TABLE_LEADS, null);
            int totalLeads = 0;
            if (cursor.moveToFirst()) {
                totalLeads = cursor.getInt(0);
            }
            cursor.close();
            db.close();

            // Update metadata
            SharedPreferences prefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("has_data", totalLeads > 0)
                .putInt("total_leads", totalLeads)
                .putLong("last_update_time", System.currentTimeMillis())
                .apply();

            Log.d(TAG, "🔧 [HIVE_NATIVE_METADATA] Updated Hive Native DB metadata: " + totalLeads + " total leads");

        } catch (Exception e) {
            Log.e(TAG, "❌ [HIVE_NATIVE_METADATA] Error updating Hive Native database metadata: " + e.getMessage());
        }
    }

    /**
     * Update native database metadata after incremental changes
     */
    private void updateNativeDatabaseMetadata() {
        try {
            NativeLeadsDbHelper dbHelper = new NativeLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            // Count total leads in database (exclude deleted and archived)
            Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + NativeLeadsDbHelper.TABLE_LEADS + " WHERE isDeleted = 0 AND isArchived = 0", null);
            int totalLeads = 0;
            if (cursor.moveToFirst()) {
                totalLeads = cursor.getInt(0);
            }
            cursor.close();
            db.close();

            // Update metadata
            SharedPreferences prefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putLong("last_update_time", System.currentTimeMillis())
                .putInt("total_leads", totalLeads)
                .putBoolean("has_data", totalLeads > 0)
                .apply();

            Log.d(TAG, "🔧 [INCREMENTAL_NATIVE] Updated metadata: total leads = " + totalLeads);

        } catch (Exception e) {
            Log.e(TAG, "❌ [INCREMENTAL_NATIVE] Error updating native database metadata: " + e.getMessage());
        }
    }

    /**
     * Get shouldViewOnlyAssigned with automatic global settings refresh
     */
    private boolean getShouldViewOnlyAssignedWithRefresh() {
        try {
            SharedPreferences defaultPrefs = android.preference.PreferenceManager.getDefaultSharedPreferences(this);

            // Check cache first
            long lastFetchTime = defaultPrefs.getLong("global_settings_last_fetch_" + tenantId, 0);
            long currentTime = System.currentTimeMillis();
            long cacheValidityDuration = 60 * 1000; // 1 minute in milliseconds

            boolean shouldViewOnlyAssigned = false;

            if (currentTime - lastFetchTime < cacheValidityDuration) {
                // Use cached data
                shouldViewOnlyAssigned = defaultPrefs.getBoolean("cached_shouldViewOnlyAssigned", false);
                Log.d(TAG, "🔧 [GLOBAL_SETTINGS] Using cached shouldViewOnlyAssigned: " + shouldViewOnlyAssigned);
            } else {
                // Cache expired, fetch fresh data from API
                Log.d(TAG, "🔧 [GLOBAL_SETTINGS] Cache expired, fetching fresh global settings from API...");

                // Check if cache exists (to avoid false positive change detection on first run)
                boolean cacheExists = defaultPrefs.contains("cached_shouldViewOnlyAssigned");
                boolean previousShouldViewOnlyAssigned = false;

                if (cacheExists) {
                    // Get previous value only if cache exists
                    previousShouldViewOnlyAssigned = defaultPrefs.getBoolean("cached_shouldViewOnlyAssigned", false);
                    Log.d(TAG, "🔧 [GLOBAL_SETTINGS] Previous cached value: " + previousShouldViewOnlyAssigned);
                } else {
                    Log.d(TAG, "🔧 [GLOBAL_SETTINGS] No cached value found (first run)");
                }

                shouldViewOnlyAssigned = fetchGlobalSettingsFromApi();

                // Check if the value has changed (only if cache existed before)
                if (cacheExists && previousShouldViewOnlyAssigned != shouldViewOnlyAssigned) {
                    Log.d(TAG, "🔄 [GLOBAL_SETTINGS] shouldViewOnlyAssigned value changed from " + previousShouldViewOnlyAssigned + " to " + shouldViewOnlyAssigned);

                    // Set flag in SharedPreferences to indicate Hive database should be cleared on next native sync
                    SharedPreferences flutterPrefs = getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE);
                    flutterPrefs.edit().putBoolean("flutter.should_clear_hive_for_settings_change", true).apply();
                    Log.d(TAG, "🔄 [GLOBAL_SETTINGS] Set flag to clear Hive database on next native sync");

                    Log.d(TAG, "🗑️ [GLOBAL_SETTINGS] Clearing database and reloading with new shouldViewOnlyAssigned value...");

                    // Clear database and reload with new value
                    clearDatabaseAndReloadWithNewSettings(shouldViewOnlyAssigned);
                } else if (cacheExists) {
                    Log.d(TAG, "✅ [GLOBAL_SETTINGS] shouldViewOnlyAssigned value unchanged: " + shouldViewOnlyAssigned);
                } else {
                    Log.d(TAG, "✅ [GLOBAL_SETTINGS] First time fetching shouldViewOnlyAssigned: " + shouldViewOnlyAssigned);
                }

                // Update cache
                defaultPrefs.edit()
                    .putBoolean("cached_shouldViewOnlyAssigned", shouldViewOnlyAssigned)
                    .putLong("global_settings_last_fetch_" + tenantId, currentTime)
                    .putBoolean("shouldViewOnlyAssigned", shouldViewOnlyAssigned) // For backward compatibility
                    .apply();

                Log.d(TAG, "✅ [GLOBAL_SETTINGS] Updated cache with fresh data: shouldViewOnlyAssigned=" + shouldViewOnlyAssigned);
            }

            return shouldViewOnlyAssigned;

        } catch (Exception e) {
            Log.w(TAG, "⚠️ [GLOBAL_SETTINGS] Failed to get shouldViewOnlyAssigned: " + e.getMessage());
            return false; // Default to false if unable to retrieve
        }
    }

    /**
     * Fetch global settings from API
     */
    private boolean fetchGlobalSettingsFromApi() {
        try {
            String apiUrl = "https://prd-mobile.leadrat.com/api/v1/globalsettings/callsettings/anonymous";

            Log.d(TAG, "🌐 [GLOBAL_SETTINGS_API] Fetching global settings from: " + apiUrl);
            Log.d(TAG, "🌐 [GLOBAL_SETTINGS_API] Headers: {accept: application/json, tenant: " + tenantId + "}");

            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // Set request properties
            connection.setRequestMethod("GET");
            connection.setRequestProperty("accept", "application/json");
            connection.setRequestProperty("tenant", tenantId);
            connection.setConnectTimeout(10000); // 10 seconds timeout
            connection.setReadTimeout(10000);

            long apiStartTime = System.currentTimeMillis();
            int responseCode = connection.getResponseCode();
            long apiDuration = System.currentTimeMillis() - apiStartTime;

            Log.d(TAG, "🌐 [GLOBAL_SETTINGS_API] Response code: " + responseCode + " (took " + apiDuration + "ms)");

            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;

                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                String responseBody = response.toString();
                Log.d(TAG, "🌐 [GLOBAL_SETTINGS_API] Response received: " + responseBody.length() + " characters");

                // Parse JSON response
                JSONObject jsonResponse = new JSONObject(responseBody);
                if (jsonResponse.has("data")) {
                    JSONObject data = jsonResponse.getJSONObject("data");
                    boolean shouldViewOnlyAssigned = data.optBoolean("shouldViewOnlyAssigned", false);

                    Log.d(TAG, "✅ [GLOBAL_SETTINGS_API] Successfully parsed: shouldViewOnlyAssigned=" + shouldViewOnlyAssigned);
                    return shouldViewOnlyAssigned;
                } else {
                    Log.w(TAG, "⚠️ [GLOBAL_SETTINGS_API] Response missing 'data' field");
                    return false;
                }
            } else {
                Log.e(TAG, "❌ [GLOBAL_SETTINGS_API] HTTP error: " + responseCode);
                return false;
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [GLOBAL_SETTINGS_API] Error fetching global settings: " + e.getMessage());
            return false;
        }
    }

    /**
     * Clear database and reload with new shouldViewOnlyAssigned setting
     */
    private void clearDatabaseAndReloadWithNewSettings(boolean newShouldViewOnlyAssigned) {
        new Thread(() -> {
            try {
                Log.d(TAG, "🗑️ [DATABASE_RESET] Starting database clear and reload process...");

                // Clear Flutter database by calling Flutter method
                try {
                    MainActivity mainActivity = MainActivity.getInstance();
                    MethodChannel methodChannel = null;
                    if (mainActivity != null) {
                        methodChannel = mainActivity.getDataSyncMethodChannel();
                    }

                    if (methodChannel != null) {
                        Log.d(TAG, "🗑️ [DATABASE_RESET] Calling Flutter to clear database...");
                        methodChannel.invokeMethod("clearDatabase", null, new MethodChannel.Result() {
                            @Override
                            public void success(Object result) {
                                Log.d(TAG, "✅ [DATABASE_RESET] Flutter database cleared successfully");
                                // After clearing, reload with offline API
                                reloadDatabaseWithOfflineApi(newShouldViewOnlyAssigned);
                            }

                            @Override
                            public void error(String errorCode, String errorMessage, Object errorDetails) {
                                Log.e(TAG, "❌ [DATABASE_RESET] Failed to clear Flutter database: " + errorCode + " - " + errorMessage);
                                // Still try to reload even if clear failed
                                reloadDatabaseWithOfflineApi(newShouldViewOnlyAssigned);
                            }

                            @Override
                            public void notImplemented() {
                                Log.w(TAG, "⚠️ [DATABASE_RESET] Flutter clearDatabase method not implemented");
                                // Still try to reload
                                reloadDatabaseWithOfflineApi(newShouldViewOnlyAssigned);
                            }
                        });
                    } else {
                        Log.w(TAG, "⚠️ [DATABASE_RESET] MethodChannel not available, using native database reset...");
                        performNativeDatabaseReset(newShouldViewOnlyAssigned);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "❌ [DATABASE_RESET] Exception clearing database: " + e.getMessage());
                    // Fallback to native database reset
                    performNativeDatabaseReset(newShouldViewOnlyAssigned);
                }

            } catch (Exception e) {
                Log.e(TAG, "❌ [DATABASE_RESET] Error in clearDatabaseAndReloadWithNewSettings: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Reload database using offline API with new shouldViewOnlyAssigned value
     */
    private void reloadDatabaseWithOfflineApi(boolean shouldViewOnlyAssigned) {
        try {
            Log.d(TAG, "🔄 [DATABASE_RELOAD] Starting database reload with offline API...");
            Log.d(TAG, "🔄 [DATABASE_RELOAD] Using shouldViewOnlyAssigned: " + shouldViewOnlyAssigned);

            // Reset sync time to force full reload
            prefs.edit().remove(KEY_LAST_SYNC_TIME).apply();
            Log.d(TAG, "🔄 [DATABASE_RELOAD] Reset sync time to force full reload");

            // Call Flutter to perform full sync with new settings
            MainActivity mainActivity = MainActivity.getInstance();
            MethodChannel methodChannel = null;
            if (mainActivity != null) {
                methodChannel = mainActivity.getDataSyncMethodChannel();
            }

            if (methodChannel != null) {
                Log.d(TAG, "🔄 [DATABASE_RELOAD] Calling Flutter to perform full sync...");
                methodChannel.invokeMethod("performFullSync", null, new MethodChannel.Result() {
                    @Override
                    public void success(Object result) {
                        Log.d(TAG, "✅ [DATABASE_RELOAD] Flutter full sync completed successfully: " + result);
                    }

                    @Override
                    public void error(String errorCode, String errorMessage, Object errorDetails) {
                        Log.e(TAG, "❌ [DATABASE_RELOAD] Flutter full sync failed: " + errorCode + " - " + errorMessage);
                    }

                    @Override
                    public void notImplemented() {
                        Log.w(TAG, "⚠️ [DATABASE_RELOAD] Flutter performFullSync method not implemented");
                    }
                });
            } else {
                Log.w(TAG, "⚠️ [DATABASE_RELOAD] MethodChannel not available, using native database reset...");
                performNativeDatabaseReset(shouldViewOnlyAssigned);
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [DATABASE_RELOAD] Error in reloadDatabaseWithOfflineApi: " + e.getMessage());
        }
    }

    /**
     * Perform native database reset when Flutter is not available
     */
    private void performNativeDatabaseReset(boolean shouldViewOnlyAssigned) {
        new Thread(() -> {
            try {
                Log.d(TAG, "🗑️ [NATIVE_DATABASE_RESET] Starting native database reset...");
                Log.d(TAG, "🗑️ [NATIVE_DATABASE_RESET] Using shouldViewOnlyAssigned: " + shouldViewOnlyAssigned);

                // Clear sync time to force full reload
                prefs.edit().remove(KEY_LAST_SYNC_TIME).apply();
                Log.d(TAG, "🗑️ [NATIVE_DATABASE_RESET] Reset sync time to force full reload");

                // Call offline API to reload data with new settings
                int totalLeads = performOfflineSync(shouldViewOnlyAssigned);

                // Update sync time and stats after successful reload
                String currentTime = getCurrentISOTime();
                prefs.edit()
                    .putString(KEY_LAST_SYNC_TIME, currentTime)
                    .putInt(KEY_TOTAL_LEADS, totalLeads)
                    .apply();

                // Mark the reset time to prevent Flutter from duplicating the reset
                SharedPreferences flutterPrefs = getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE);
                flutterPrefs.edit()
                    .putLong("flutter.last_native_database_reset_time", System.currentTimeMillis())
                    .apply();

                Log.d(TAG, "✅ [NATIVE_DATABASE_RESET] Database reset completed successfully");
                Log.d(TAG, "✅ [NATIVE_DATABASE_RESET] Total leads loaded: " + totalLeads);
                Log.d(TAG, "🔧 [NATIVE_DATABASE_RESET] Marked reset timestamp to prevent Flutter duplication");

            } catch (Exception e) {
                Log.e(TAG, "❌ [NATIVE_DATABASE_RESET] Error in native database reset: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Perform offline sync using full offline API
     */
    private int performOfflineSync(boolean shouldViewOnlyAssigned) {
        int totalLeads = 0;
        int currentPage = 1;
        boolean hasMoreData = true;

        Log.d(TAG, "🔄 [OFFLINE_SYNC] Starting offline sync operation...");
        Log.d(TAG, "🔄 [OFFLINE_SYNC] Using shouldViewOnlyAssigned: " + shouldViewOnlyAssigned);

        // Reset sync progress and total count for new sync
        SharedPreferences nativeDbPrefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
        nativeDbPrefs.edit()
            .putBoolean("sync_in_progress", false)
            .putInt("total_leads", 0)
            .apply();
        Log.d(TAG, "🔄 [OFFLINE_SYNC] Reset native database counters for new sync");

        while (hasMoreData) {
            Log.d(TAG, "🔄 [OFFLINE_SYNC] Fetching offline page " + currentPage + " (pageSize: " + PAGE_SIZE + ")...");

            try {
                String apiResponse = makeOfflineSyncApiCall(shouldViewOnlyAssigned, currentPage, PAGE_SIZE);
                if (apiResponse != null) {
                    int leadsInPage = processOfflineApiResponse(apiResponse);
                    totalLeads += leadsInPage;

                    Log.d(TAG, "🔄 [OFFLINE_SYNC] Offline page " + currentPage + " processed: " + leadsInPage + " leads");

                    // If we got fewer leads than page size, we've reached the end
                    if (leadsInPage < PAGE_SIZE) {
                        hasMoreData = false;
                        Log.d(TAG, "🔄 [OFFLINE_SYNC] Reached end of offline data (page " + currentPage + ")");
                    } else {
                        currentPage++;
                    }
                } else {
                    Log.e(TAG, "❌ [OFFLINE_SYNC] API call failed for offline page " + currentPage);
                    hasMoreData = false;
                }
            } catch (Exception e) {
                Log.e(TAG, "❌ [OFFLINE_SYNC] Error processing offline page " + currentPage + ": " + e.getMessage());
                hasMoreData = false;
            }
        }

        // Mark sync as complete
        nativeDbPrefs.edit().putBoolean("sync_in_progress", false).apply();

        Log.d(TAG, "✅ [OFFLINE_SYNC] Offline sync completed. Total leads: " + totalLeads);
        Log.d(TAG, "🔧 [OFFLINE_SYNC] Marked sync as complete in native database");
        return totalLeads;
    }

    /**
     * Make API call to fetch offline leads
     */
    private String makeOfflineSyncApiCall(boolean shouldViewOnlyAssigned, int page, int pageSize) {
        try {
            // Reload data before each API call to ensure we have latest values
            loadLeadratData();

            // Check if we have required data
            if (userId == null || userId.isEmpty() || "null".equals(userId)) {
                Log.e(TAG, "❌ [OFFLINE_API] User ID is required but not available");
                return null;
            }
            if (tenantId == null || tenantId.isEmpty() || "null".equals(tenantId)) {
                Log.e(TAG, "❌ [OFFLINE_API] Tenant ID is required but not available");
                return null;
            }

            String urlString = BASE_URL + API_ENDPOINT_OFFLINE +
                "?UserId=" + java.net.URLEncoder.encode(userId, "UTF-8") +
                "&PageNumber=" + page +
                "&PageSize=" + pageSize +
                "&SendOnlyAssignedLeads=" + shouldViewOnlyAssigned;

            Log.d(TAG, "🔄 [OFFLINE_API] Starting offline sync API request...");
            Log.d(TAG, "🔄 [OFFLINE_API] URL: " + urlString);
            Log.d(TAG, "🔄 [OFFLINE_API] Headers: {accept: application/json, tenant: " + tenantId + "}");
            Log.d(TAG, "🔄 [OFFLINE_API] Parameters: {UserId: " + userId + ", PageNumber: " + page + ", PageSize: " + pageSize + ", SendOnlyAssignedLeads: " + shouldViewOnlyAssigned + "}");

            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("accept", "application/json");
            connection.setRequestProperty("tenant", tenantId);
            connection.setConnectTimeout(CONNECTION_TIMEOUT);
            connection.setReadTimeout(CONNECTION_TIMEOUT);

            long startTime = System.currentTimeMillis();
            int responseCode = connection.getResponseCode();
            long duration = System.currentTimeMillis() - startTime;

            Log.d(TAG, "🔄 [OFFLINE_API] Response code: " + responseCode + " (took " + duration + "ms)");

            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();

                String responseBody = response.toString();
                Log.d(TAG, "🔄 [OFFLINE_API] Response received: " + responseBody.length() + " characters");

                return responseBody;
            } else {
                Log.e(TAG, "❌ [OFFLINE_API] HTTP error: " + responseCode);
                return null;
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_API] Error making offline API call: " + e.getMessage());
            return null;
        }
    }

    /**
     * Process offline API response and return number of leads processed
     */
    private int processOfflineApiResponse(String apiResponse) {
        try {
            JSONObject jsonResponse = new JSONObject(apiResponse);

            if (jsonResponse.has("data")) {
                JSONArray dataArray = jsonResponse.getJSONArray("data");
                int leadCount = dataArray.length();

                Log.d(TAG, "📋 [OFFLINE_SYNC] Processing " + leadCount + " leads from API response");

                // Try to save leads via Flutter first
                MainActivity mainActivity = MainActivity.getInstance();
                MethodChannel methodChannel = null;
                if (mainActivity != null) {
                    methodChannel = mainActivity.getDataSyncMethodChannel();
                }

                if (methodChannel != null) {
                    // App is running - use Flutter to save leads
                    Log.d(TAG, "💾 [OFFLINE_SYNC] App is running - saving leads via Flutter...");
                    saveLeadsViaFlutter(methodChannel, dataArray);
                } else {
                    // App is killed - save leads directly to native SQLite database
                    Log.d(TAG, "💾 [OFFLINE_SYNC] App is killed - saving leads to native database...");
                    saveLeadsToNativeDatabase(dataArray);
                }

                Log.d(TAG, "📋 [OFFLINE_SYNC] Processed " + leadCount + " leads from offline API response");
                return leadCount;
            } else {
                Log.w(TAG, "⚠️ [OFFLINE_SYNC] API response missing 'data' field");
                return 0;
            }

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_SYNC] Error processing offline API response: " + e.getMessage());
            return 0;
        }
    }

    /**
     * Save leads via Flutter method channel
     */
    private void saveLeadsViaFlutter(MethodChannel methodChannel, JSONArray leadsArray) {
        try {
            // Convert JSONArray to List<Map> for Flutter
            List<Map<String, Object>> leadsList = new ArrayList<>();

            for (int i = 0; i < leadsArray.length(); i++) {
                JSONObject lead = leadsArray.getJSONObject(i);
                Map<String, Object> leadMap = new HashMap<>();

                leadMap.put("id", lead.optString("id", ""));
                leadMap.put("name", lead.optString("name", ""));
                leadMap.put("contactNo", lead.optString("contactNo", ""));
                leadMap.put("alternateContactNo", lead.optString("alternateContactNo", ""));
                leadMap.put("assignTo", lead.optString("assignTo", ""));
                leadMap.put("isDeleted", lead.optBoolean("isDeleted", false));

                // Handle lastModifiedOn date
                String lastModifiedOnStr = lead.optString("lastModifiedOn", "");
                if (!lastModifiedOnStr.isEmpty()) {
                    leadMap.put("lastModifiedOn", lastModifiedOnStr);
                }

                leadsList.add(leadMap);
            }

            Log.d(TAG, "💾 [OFFLINE_SYNC] Calling Flutter to save " + leadsList.size() + " leads...");

            methodChannel.invokeMethod("saveLeadsToDatabase", leadsList, new MethodChannel.Result() {
                @Override
                public void success(Object result) {
                    Log.d(TAG, "✅ [OFFLINE_SYNC] Flutter successfully saved leads to database: " + result);
                }

                @Override
                public void error(String errorCode, String errorMessage, Object errorDetails) {
                    Log.e(TAG, "❌ [OFFLINE_SYNC] Flutter failed to save leads: " + errorCode + " - " + errorMessage);
                    // Fallback to temporary storage
                    try {
                        saveLeadsToTemporaryStorage(leadsArray);
                    } catch (Exception e) {
                        Log.e(TAG, "❌ [OFFLINE_SYNC] Fallback to temporary storage also failed: " + e.getMessage());
                    }
                }

                @Override
                public void notImplemented() {
                    Log.w(TAG, "⚠️ [OFFLINE_SYNC] Flutter saveLeadsToDatabase method not implemented");
                    // Fallback to temporary storage
                    try {
                        saveLeadsToTemporaryStorage(leadsArray);
                    } catch (Exception e) {
                        Log.e(TAG, "❌ [OFFLINE_SYNC] Fallback to temporary storage failed: " + e.getMessage());
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "❌ [OFFLINE_SYNC] Error saving leads via Flutter: " + e.getMessage());
            // Fallback to temporary storage
            try {
                saveLeadsToTemporaryStorage(leadsArray);
            } catch (Exception fallbackError) {
                Log.e(TAG, "❌ [OFFLINE_SYNC] Fallback to temporary storage failed: " + fallbackError.getMessage());
            }
        }
    }

    /**
     * Save leads to temporary storage (SharedPreferences) when Flutter is not available
     */
    private void saveLeadsToTemporaryStorage(JSONArray leadsArray) {
        try {
            Log.d(TAG, "💾 [TEMP_STORAGE] Saving " + leadsArray.length() + " leads to temporary storage...");

            SharedPreferences tempPrefs = getSharedPreferences("temp_leads_storage", Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = tempPrefs.edit();

            // Clear previous temporary data
            editor.clear();

            // Save leads data
            editor.putString("temp_leads_data", leadsArray.toString());
            editor.putLong("temp_leads_timestamp", System.currentTimeMillis());
            editor.putInt("temp_leads_count", leadsArray.length());
            editor.putBoolean("temp_leads_pending", true);

            editor.apply();

            Log.d(TAG, "✅ [TEMP_STORAGE] Successfully saved " + leadsArray.length() + " leads to temporary storage");
            Log.d(TAG, "🔄 [TEMP_STORAGE] Leads will be synced to Flutter database when app becomes available");

        } catch (Exception e) {
            Log.e(TAG, "❌ [TEMP_STORAGE] Error saving leads to temporary storage: " + e.getMessage());
        }
    }

    /**
     * Check and sync any pending temporary storage when app becomes available
     */
    private void checkAndSyncTemporaryStorage() {
        new Thread(() -> {
            try {
                SharedPreferences tempPrefs = getSharedPreferences("temp_leads_storage", Context.MODE_PRIVATE);
                boolean hasPendingLeads = tempPrefs.getBoolean("temp_leads_pending", false);

                if (!hasPendingLeads) {
                    Log.d(TAG, "🔍 [TEMP_STORAGE] No pending temporary storage to sync");
                    return;
                }

                String tempLeadsData = tempPrefs.getString("temp_leads_data", null);
                int tempLeadsCount = tempPrefs.getInt("temp_leads_count", 0);
                long tempTimestamp = tempPrefs.getLong("temp_leads_timestamp", 0);

                if (tempLeadsData == null || tempLeadsData.isEmpty()) {
                    Log.w(TAG, "⚠️ [TEMP_STORAGE] Pending flag set but no data found, clearing flag");
                    tempPrefs.edit().putBoolean("temp_leads_pending", false).apply();
                    return;
                }

                Log.d(TAG, "🔄 [TEMP_STORAGE] Found pending temporary storage: " + tempLeadsCount + " leads from " + new java.util.Date(tempTimestamp));

                // Try to sync to Flutter database
                MainActivity mainActivity = MainActivity.getInstance();
                MethodChannel methodChannel = null;
                if (mainActivity != null) {
                    methodChannel = mainActivity.getDataSyncMethodChannel();
                }

                if (methodChannel != null) {
                    Log.d(TAG, "💾 [TEMP_STORAGE] Flutter is available - syncing temporary storage...");

                    try {
                        JSONArray leadsArray = new JSONArray(tempLeadsData);

                        // Make variables effectively final for lambda
                        final MethodChannel finalMethodChannel = methodChannel;
                        final JSONArray finalLeadsArray = leadsArray;

                        // Post to main thread for MethodChannel call
                        syncHandler.post(() -> {
                            saveLeadsViaFlutter(finalMethodChannel, finalLeadsArray);
                        });

                        // Clear temporary storage after successful sync attempt
                        tempPrefs.edit().clear().apply();
                        Log.d(TAG, "✅ [TEMP_STORAGE] Temporary storage sync initiated and cleared");

                    } catch (Exception e) {
                        Log.e(TAG, "❌ [TEMP_STORAGE] Error parsing temporary storage data: " + e.getMessage());
                        // Clear corrupted data
                        tempPrefs.edit().clear().apply();
                    }
                } else {
                    Log.d(TAG, "⚠️ [TEMP_STORAGE] Flutter not available yet, will retry later");
                }

            } catch (Exception e) {
                Log.e(TAG, "❌ [TEMP_STORAGE] Error checking temporary storage: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Check and sync native database if it has data
     */
    private void checkAndSyncNativeDatabase() {
        new Thread(() -> {
            try {
                SharedPreferences nativeDbPrefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
                boolean hasData = nativeDbPrefs.getBoolean("has_data", false);
                int totalLeads = nativeDbPrefs.getInt("total_leads", 0);
                long lastUpdateTime = nativeDbPrefs.getLong("last_update_time", 0);

                if (!hasData || totalLeads == 0) {
                    Log.d(TAG, "🔍 [NATIVE_DB_SYNC] No data in native database to sync");
                    return;
                }

                Log.d(TAG, "🔄 [NATIVE_DB_SYNC] Found native database with " + totalLeads + " leads from " + new java.util.Date(lastUpdateTime));

                // Try to sync to Flutter database
                MainActivity mainActivity = MainActivity.getInstance();
                MethodChannel methodChannel = null;
                if (mainActivity != null) {
                    methodChannel = mainActivity.getDataSyncMethodChannel();
                }

                if (methodChannel != null) {
                    Log.d(TAG, "💾 [NATIVE_DB_SYNC] Flutter is available - syncing native database...");

                    // Make variable effectively final for lambda
                    final MethodChannel finalMethodChannel = methodChannel;

                    // Post to main thread for MethodChannel call
                    syncHandler.post(() -> {
                        finalMethodChannel.invokeMethod("syncNativeDatabase", null, new MethodChannel.Result() {
                            @Override
                            public void success(Object result) {
                                Log.d(TAG, "✅ [NATIVE_DB_SYNC] Native database sync completed successfully: " + result);
                                // Clear any sync state that might be blocking future syncs
                                setGlobalSyncInProgress(false, "none");
                                Log.d(TAG, "🔧 [NATIVE_DB_SYNC] Cleared sync state after native database sync");
                            }

                            @Override
                            public void error(String errorCode, String errorMessage, Object errorDetails) {
                                Log.e(TAG, "❌ [NATIVE_DB_SYNC] Native database sync failed: " + errorCode + " - " + errorMessage);
                                // Clear sync state even on error to prevent blocking
                                setGlobalSyncInProgress(false, "none");
                                Log.d(TAG, "🔧 [NATIVE_DB_SYNC] Cleared sync state after native database sync error");
                            }

                            @Override
                            public void notImplemented() {
                                Log.w(TAG, "⚠️ [NATIVE_DB_SYNC] Native database sync method not implemented");
                                // Clear sync state even if not implemented
                                setGlobalSyncInProgress(false, "none");
                                Log.d(TAG, "🔧 [NATIVE_DB_SYNC] Cleared sync state after native database sync not implemented");
                            }
                        });
                    });
                } else {
                    Log.d(TAG, "⚠️ [NATIVE_DB_SYNC] Flutter not available yet, will retry later");
                }

            } catch (Exception e) {
                Log.e(TAG, "❌ [NATIVE_DB_SYNC] Error checking native database: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Trigger incremental sync to Hive Native DB for specific changes
     * This only syncs the changed records, not the entire database
     */
    private void triggerIncrementalSyncToHiveNative(JSONArray changesArray, String type) {
        new Thread(() -> {
            // Use synchronization to prevent concurrent access to Hive Native DB
            synchronized (HIVE_DB_LOCK) {
                try {
                    Log.d(TAG, "🔄 [INCREMENTAL_HIVE_SYNC] Starting incremental sync of " + changesArray.length() + " " + type + " changes to Hive Native DB...");

                    HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(this);
                    SQLiteDatabase db = dbHelper.getWritableDatabase();

                db.beginTransaction();
                try {
                    int upsertCount = 0;
                    int deleteCount = 0;

                    for (int i = 0; i < changesArray.length(); i++) {
                        JSONObject change = changesArray.getJSONObject(i);

                        String entityId = change.optString("id", "");
                        boolean isDeleted = change.optBoolean("isDeleted", false);
                        boolean isArchived = change.optBoolean("isArchived", false);

                        if (isDeleted || isArchived) {
                            // Delete from Hive Native DB
                            int deletedRows = db.delete(HiveLeadsDbHelper.TABLE_LEADS, "id = ?", new String[]{entityId});
                            // Also delete from phone lookup table
                            db.delete(HiveLeadsDbHelper.TABLE_PHONE_LOOKUP, "lead_id = ?", new String[]{entityId});

                            if (deletedRows > 0) {
                                deleteCount++;
                                Log.d(TAG, "🗑️ [INCREMENTAL_HIVE_SYNC] Deleted " + type + ": " + change.optString("name", "") + " (ID: " + entityId + ")");
                            }
                        } else {
                            // Upsert to Hive Native DB
                            ContentValues values = new ContentValues();
                            values.put("id", entityId);
                            values.put("name", change.optString("name", ""));
                            values.put("contactNo", change.optString("contactNo", ""));
                            values.put("alternateContactNo", change.optString("alternateContactNo", ""));
                            values.put("assignTo", change.optString("assignTo", ""));
                            values.put("isDeleted", isDeleted ? 1 : 0);
                            values.put("isArchived", isArchived ? 1 : 0);
                            values.put("isLead", "lead".equals(type) ? 1 : 0);
                            values.put("lastModifiedOn", change.optString("lastModifiedOn", ""));
                            values.put("lastSyncedAt", getCurrentISOTime());

                            // Use INSERT OR REPLACE for upsert functionality
                            long result = db.insertWithOnConflict(HiveLeadsDbHelper.TABLE_LEADS, null, values, SQLiteDatabase.CONFLICT_REPLACE);
                            if (result != -1) {
                                upsertCount++;
                                Log.d(TAG, "💾 [INCREMENTAL_HIVE_SYNC] Upserted " + type + ": " + change.optString("name", "") + " (ID: " + entityId + ")");

                                // Update phone lookup entries for fast search
                                if (!isDeleted && !isArchived) {
                                    // First delete existing lookup entries for this lead
                                    db.delete(HiveLeadsDbHelper.TABLE_PHONE_LOOKUP, "lead_id = ?", new String[]{entityId});

                                    // Then insert new lookup entries
                                    String contactNo = change.optString("contactNo", "");
                                    String alternateContactNo = change.optString("alternateContactNo", "");
                                    insertPhoneLookupEntries(db, entityId, contactNo, alternateContactNo);
                                }
                            }
                        }
                    }

                    db.setTransactionSuccessful();
                    Log.d(TAG, "✅ [INCREMENTAL_HIVE_SYNC] Successfully processed " + type + " incremental changes: " + upsertCount + " upserts, " + deleteCount + " deletes");

                } finally {
                    db.endTransaction();
                    db.close();
                }

                    // Update metadata (get current count from database)
                    updateHiveNativeDatabaseMetadata();

                } catch (Exception e) {
                    Log.e(TAG, "❌ [INCREMENTAL_HIVE_SYNC] Error during incremental sync to Hive Native DB: " + e.getMessage());
                }
            } // End synchronized block
        }).start();
    }

    /**
     * Trigger sync from Native SQLite DB to Hive Native DB for call detection
     * This ensures newly added leads/prospects are available for call detection
     */
    private void triggerNativeToHiveNativeSync() {
        new Thread(() -> {
            try {
                Log.d(TAG, "🔄 [NATIVE_TO_HIVE_NATIVE] Starting sync from Native SQLite DB to Hive Native DB...");

                // Get all leads from Native SQLite database
                List<Map<String, Object>> nativeLeads = getNativeLeadsData();

                if (nativeLeads.isEmpty()) {
                    Log.d(TAG, "⚠️ [NATIVE_TO_HIVE_NATIVE] No leads in Native SQLite DB to sync");
                    return;
                }

                Log.d(TAG, "🔄 [NATIVE_TO_HIVE_NATIVE] Found " + nativeLeads.size() + " leads in Native SQLite DB");

                // Sync to Hive Native database
                boolean success = syncNativeLeadsToHiveNativeDatabase(nativeLeads);

                if (success) {
                    Log.d(TAG, "✅ [NATIVE_TO_HIVE_NATIVE] Successfully synced " + nativeLeads.size() + " leads to Hive Native DB");
                } else {
                    Log.e(TAG, "❌ [NATIVE_TO_HIVE_NATIVE] Failed to sync leads to Hive Native DB");
                }

            } catch (Exception e) {
                Log.e(TAG, "❌ [NATIVE_TO_HIVE_NATIVE] Error during Native to Hive Native sync: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Trigger Hive to native sync immediately
     */
    private void triggerHiveToNativeSync() {
        syncHandler.postDelayed(() -> {
            try {
                Log.d(TAG, "🔄 [HIVE_TO_NATIVE_TRIGGER] Triggering immediate Hive to native sync...");

                // Check database status before sync (in background)
                new Thread(() -> checkDatabaseStatus()).start();

                // Try to sync Hive data to native database
                MainActivity mainActivity = MainActivity.getInstance();
                MethodChannel methodChannel = null;
                if (mainActivity != null) {
                    methodChannel = mainActivity.getDataSyncMethodChannel();
                }

                if (methodChannel != null) {
                    Log.d(TAG, "🔄 [HIVE_TO_NATIVE_TRIGGER] Flutter is available - requesting Hive to native sync...");

                    // Make variable effectively final for lambda
                    final MethodChannel finalMethodChannel = methodChannel;

                    // Post to main thread for MethodChannel call
                    syncHandler.post(() -> {
                        finalMethodChannel.invokeMethod("syncHiveToNative", null, new MethodChannel.Result() {
                            @Override
                            public void success(Object result) {
                                Log.d(TAG, "✅ [HIVE_TO_NATIVE_TRIGGER] Hive to native sync completed successfully: " + result);

                                // Check database status after sync (in background)
                                syncHandler.postDelayed(() -> {
                                    Log.d(TAG, "🔍 [HIVE_TO_NATIVE_TRIGGER] Checking database status after sync...");
                                    new Thread(() -> checkDatabaseStatus()).start();
                                }, 1000); // 1 second delay to allow sync to complete
                            }

                            @Override
                            public void error(String errorCode, String errorMessage, Object errorDetails) {
                                Log.e(TAG, "❌ [HIVE_TO_NATIVE_TRIGGER] Hive to native sync failed: " + errorCode + " - " + errorMessage);
                                Log.e(TAG, "❌ [HIVE_TO_NATIVE_TRIGGER] Error details: " + errorDetails);
                            }

                            @Override
                            public void notImplemented() {
                                Log.e(TAG, "❌ [HIVE_TO_NATIVE_TRIGGER] Hive to native sync method not implemented");
                            }
                        });
                    });
                } else {
                    Log.d(TAG, "⚠️ [HIVE_TO_NATIVE_TRIGGER] Flutter is not available - cannot sync Hive to native");
                    Log.d(TAG, "⚠️ [HIVE_TO_NATIVE_TRIGGER] MainActivity instance: " + (MainActivity.getInstance() != null ? "Available" : "Null"));
                }

            } catch (Exception e) {
                Log.e(TAG, "❌ [HIVE_TO_NATIVE_TRIGGER] Error during Hive to native trigger: " + e.getMessage());
            }
        }, 2000); // 2 seconds delay to allow data to be saved to Hive first
    }

    /**
     * Check if we need to sync Hive data to native database
     */
    private void checkAndSyncHiveToNative() {
        syncHandler.postDelayed(() -> {
            try {
                Log.d(TAG, "🔄 [HIVE_TO_NATIVE_CHECK] Checking if Hive to native sync is needed...");

                // Check if Hive native database is empty
                SharedPreferences hiveDbPrefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
                boolean hiveHasData = hiveDbPrefs.getBoolean("has_data", false);
                int hiveTotalLeads = hiveDbPrefs.getInt("total_leads", 0);

                if (hiveHasData && hiveTotalLeads > 0) {
                    Log.d(TAG, "ℹ️ [HIVE_TO_NATIVE_CHECK] Hive native database already has " + hiveTotalLeads + " leads - no sync needed");
                    return;
                }

                Log.d(TAG, "🔄 [HIVE_TO_NATIVE_CHECK] Hive native database is empty - checking if Flutter/Hive has data...");

                // Try to sync Hive data to native database
                MainActivity mainActivity = MainActivity.getInstance();
                MethodChannel methodChannel = null;
                if (mainActivity != null) {
                    methodChannel = mainActivity.getDataSyncMethodChannel();
                }

                if (methodChannel != null) {
                    Log.d(TAG, "🔄 [HIVE_TO_NATIVE_CHECK] Flutter is available - requesting Hive to native sync...");

                    // Make variable effectively final for lambda
                    final MethodChannel finalMethodChannel = methodChannel;

                    // Post to main thread for MethodChannel call
                    syncHandler.post(() -> {
                        finalMethodChannel.invokeMethod("syncHiveToNative", null, new MethodChannel.Result() {
                            @Override
                            public void success(Object result) {
                                Log.d(TAG, "✅ [HIVE_TO_NATIVE_CHECK] Hive to native sync completed successfully: " + result);
                            }

                            @Override
                            public void error(String errorCode, String errorMessage, Object errorDetails) {
                                Log.e(TAG, "❌ [HIVE_TO_NATIVE_CHECK] Hive to native sync failed: " + errorCode + " - " + errorMessage);
                            }

                            @Override
                            public void notImplemented() {
                                Log.e(TAG, "❌ [HIVE_TO_NATIVE_CHECK] Hive to native sync method not implemented");
                            }
                        });
                    });
                } else {
                    Log.d(TAG, "⚠️ [HIVE_TO_NATIVE_CHECK] Flutter is not available - cannot sync Hive to native");
                }

            } catch (Exception e) {
                Log.e(TAG, "❌ [HIVE_TO_NATIVE_CHECK] Error during Hive to native check: " + e.getMessage());
            }
        }, 10000); // 10 seconds delay to allow Flutter to initialize
    }

    /**
     * Schedule cleanup of native database after Flutter has had time to initialize
     */
    private void scheduleNativeDatabaseCleanup() {
        // Wait 30 seconds after app startup to allow Flutter to fully initialize
        syncHandler.postDelayed(() -> {
            try {
                SharedPreferences nativeDbPrefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
                boolean hasData = nativeDbPrefs.getBoolean("has_data", false);

                if (!hasData) {
                    Log.d(TAG, "🧹 [NATIVE_DB_CLEANUP] No native database data to clean up");
                    return;
                }

                // Check if Flutter has marked the sync as complete
                SharedPreferences flutterPrefs = getSharedPreferences("FlutterSharedPreferences", Context.MODE_PRIVATE);
                boolean nativeDbSynced = flutterPrefs.getBoolean("flutter.native_db_synced", false);

                if (nativeDbSynced) {
                    Log.d(TAG, "🧹 [NATIVE_DB_CLEANUP] Flutter has synced native database, cleaning up...");

                    // Clear the native database
                    clearNativeLeadsData();

                    // Clear the sync flag
                    flutterPrefs.edit().remove("flutter.native_db_synced").apply();

                    Log.d(TAG, "✅ [NATIVE_DB_CLEANUP] Native database cleanup completed");
                } else {
                    Log.d(TAG, "⚠️ [NATIVE_DB_CLEANUP] Flutter sync not yet complete, keeping native database");
                }

            } catch (Exception e) {
                Log.e(TAG, "❌ [NATIVE_DB_CLEANUP] Error during cleanup: " + e.getMessage());
            }
        }, 30000); // 30 seconds delay
    }

    /**
     * Save leads directly to native SQLite database when Flutter is not available
     */
    private void saveLeadsToNativeDatabase(JSONArray leadsArray) {
        try {
            Log.d(TAG, "💾 [NATIVE_DB] Saving " + leadsArray.length() + " leads to native SQLite database...");

            NativeLeadsDbHelper dbHelper = new NativeLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            // Check if this is the first page (clear database only once)
            SharedPreferences nativeDbPrefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
            boolean isFirstPage = !nativeDbPrefs.getBoolean("sync_in_progress", false);

            if (isFirstPage) {
                // Clear existing data only for the first page
                db.delete(NativeLeadsDbHelper.TABLE_LEADS, null, null);
                Log.d(TAG, "🗑️ [NATIVE_DB] Cleared existing leads from native database (first page)");

                // Mark sync as in progress
                nativeDbPrefs.edit().putBoolean("sync_in_progress", true).apply();
            } else {
                Log.d(TAG, "📝 [NATIVE_DB] Appending to existing native database (page continuation)");
            }

            // Log first few leads to see what data we're saving
            Log.d(TAG, "💾 [NATIVE_DB] === SAMPLE DATA BEING SAVED ===");
            for (int i = 0; i < Math.min(5, leadsArray.length()); i++) {
                try {
                    JSONObject sampleLead = leadsArray.getJSONObject(i);
                    Log.d(TAG, "💾 [NATIVE_DB] Sample Lead " + (i+1) + ": {");
                    Log.d(TAG, "💾 [NATIVE_DB]   id: " + sampleLead.optString("id", ""));
                    Log.d(TAG, "💾 [NATIVE_DB]   name: " + sampleLead.optString("name", ""));
                    Log.d(TAG, "💾 [NATIVE_DB]   contactNo: " + sampleLead.optString("contactNo", ""));
                    Log.d(TAG, "💾 [NATIVE_DB]   alternateContactNo: " + sampleLead.optString("alternateContactNo", ""));
                    Log.d(TAG, "💾 [NATIVE_DB] }");
                } catch (Exception e) {
                    Log.e(TAG, "💾 [NATIVE_DB] Error logging sample lead " + i + ": " + e.getMessage());
                }
            }
            Log.d(TAG, "💾 [NATIVE_DB] === END SAMPLE DATA ===");

            // Insert new leads
            db.beginTransaction();
            try {
                for (int i = 0; i < leadsArray.length(); i++) {
                    JSONObject lead = leadsArray.getJSONObject(i);

                    String insertSql = "INSERT OR REPLACE INTO " + NativeLeadsDbHelper.TABLE_LEADS +
                        " (id, name, contactNo, alternateContactNo, assignTo, isDeleted, isLead, lastModifiedOn, lastSyncedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    String id = lead.optString("id", "");
                    String name = lead.optString("name", "");
                    String contactNo = lead.optString("contactNo", "");
                    String alternateContactNo = lead.optString("alternateContactNo", "");
                    String assignTo = lead.optString("assignTo", "");
                    boolean isDeleted = lead.optBoolean("isDeleted", false);
                    String lastModifiedOn = lead.optString("lastModifiedOn", "");
                    String lastSyncedAt = getCurrentISOTime();

                    db.execSQL(insertSql, new Object[]{
                        id, name, contactNo, alternateContactNo, assignTo,
                        isDeleted ? 1 : 0, 1, lastModifiedOn, lastSyncedAt  // isLead = 1 for leads from offline API
                    });

                    if (i % 100 == 0) {
                        Log.d(TAG, "💾 [NATIVE_DB] Inserted " + (i + 1) + "/" + leadsArray.length() + " leads");
                    }
                }

                db.setTransactionSuccessful();
                Log.d(TAG, "✅ [NATIVE_DB] Successfully saved " + leadsArray.length() + " leads to native database");

            } finally {
                db.endTransaction();
                db.close();
            }

            // Update metadata - accumulate total leads count
            SharedPreferences prefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
            int currentTotal = prefs.getInt("total_leads", 0);
            int newTotal = currentTotal + leadsArray.length();

            prefs.edit()
                .putLong("last_update_time", System.currentTimeMillis())
                .putInt("total_leads", newTotal)
                .putBoolean("has_data", true)
                .apply();

            Log.d(TAG, "🔧 [NATIVE_DB] Updated metadata: " + leadsArray.length() + " leads added, total now: " + newTotal);

            // IMPORTANT: Trigger sync from Native SQLite DB to Hive Native DB
            // This ensures call detection can find newly added leads/prospects
            Log.d(TAG, "🔄 [NATIVE_DB] Triggering sync to Hive Native DB for call detection...");
            triggerNativeToHiveNativeSync();

        } catch (Exception e) {
            Log.e(TAG, "❌ [NATIVE_DB] Error saving leads to native database: " + e.getMessage());
        }
    }

    /**
     * SQLite helper class for native leads storage (original native DB)
     */
    public static class NativeLeadsDbHelper extends SQLiteOpenHelper {
        private static final String DATABASE_NAME = "native_leads.db";
        private static final int DATABASE_VERSION = 2; // Increased for isLead field
        public static final String TABLE_LEADS = "leads";

        private static final String CREATE_TABLE_LEADS =
            "CREATE TABLE " + TABLE_LEADS + " (" +
            "id TEXT PRIMARY KEY," +
            "name TEXT," +
            "contactNo TEXT," +
            "alternateContactNo TEXT," +
            "assignTo TEXT," +
            "isDeleted INTEGER DEFAULT 0," +
            "isArchived INTEGER DEFAULT 0," +
            "isLead INTEGER DEFAULT 1," +
            "lastModifiedOn TEXT," +
            "lastSyncedAt TEXT" +
            ")";

        public NativeLeadsDbHelper(Context context) {
            super(context, DATABASE_NAME, null, DATABASE_VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            db.execSQL(CREATE_TABLE_LEADS);
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            if (oldVersion < 2) {
                // Add isLead column for version 2
                try {
                    db.execSQL("ALTER TABLE " + TABLE_LEADS + " ADD COLUMN isLead INTEGER DEFAULT 1");
                    Log.d("NativeLeadsDbHelper", "✅ Added isLead column to native database");
                } catch (Exception e) {
                    Log.e("NativeLeadsDbHelper", "❌ Error adding isLead column: " + e.getMessage());
                    // If ALTER fails, recreate the table
                    db.execSQL("DROP TABLE IF EXISTS " + TABLE_LEADS);
                    onCreate(db);
                }
            }
        }
    }

    /**
     * SQLite helper class for Hive leads storage (separate DB for call detection)
     */
    public static class HiveLeadsDbHelper extends SQLiteOpenHelper {
        private static final String DATABASE_NAME = "hive_leads.db";
        private static final int DATABASE_VERSION = 3; // Increased for isLead field
        public static final String TABLE_LEADS = "hive_leads";
        public static final String TABLE_PHONE_LOOKUP = "phone_lookup";

        private static final String CREATE_TABLE_LEADS =
            "CREATE TABLE " + TABLE_LEADS + " (" +
            "id TEXT PRIMARY KEY," +
            "name TEXT," +
            "contactNo TEXT," +
            "alternateContactNo TEXT," +
            "assignTo TEXT," +
            "isDeleted INTEGER DEFAULT 0," +
            "isArchived INTEGER DEFAULT 0," +
            "isLead INTEGER DEFAULT 1," +
            "lastModifiedOn TEXT," +
            "lastSyncedAt TEXT" +
            ")";

        private static final String CREATE_TABLE_PHONE_LOOKUP =
            "CREATE TABLE " + TABLE_PHONE_LOOKUP + " (" +
            "phone_clean TEXT PRIMARY KEY," +
            "lead_id TEXT," +
            "phone_type TEXT," +
            "FOREIGN KEY(lead_id) REFERENCES " + TABLE_LEADS + "(id)" +
            ")";

        public HiveLeadsDbHelper(Context context) {
            super(context, DATABASE_NAME, null, DATABASE_VERSION);
        }

        @Override
        public void onCreate(SQLiteDatabase db) {
            // Create main leads table
            db.execSQL(CREATE_TABLE_LEADS);

            // Create phone lookup table for ultra-fast searches
            db.execSQL(CREATE_TABLE_PHONE_LOOKUP);

            // Create indexes for lightning-fast lookups
            db.execSQL("CREATE INDEX IF NOT EXISTS idx_contactNo ON " + TABLE_LEADS + "(contactNo)");
            db.execSQL("CREATE INDEX IF NOT EXISTS idx_alternateContactNo ON " + TABLE_LEADS + "(alternateContactNo)");
            db.execSQL("CREATE INDEX IF NOT EXISTS idx_isDeleted ON " + TABLE_LEADS + "(isDeleted)");
            db.execSQL("CREATE INDEX IF NOT EXISTS idx_phone_lookup ON " + TABLE_PHONE_LOOKUP + "(phone_clean)");
        }

        @Override
        public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
            Log.d("HiveLeadsDbHelper", "🔄 Upgrading Hive database from version " + oldVersion + " to " + newVersion);

            if (oldVersion < 3) {
                // Add isLead column for version 3
                try {
                    // Check if column already exists
                    Cursor cursor = db.rawQuery("PRAGMA table_info(" + TABLE_LEADS + ")", null);
                    boolean columnExists = false;
                    while (cursor.moveToNext()) {
                        String columnName = cursor.getString(1); // Column name is at index 1
                        if ("isLead".equals(columnName)) {
                            columnExists = true;
                            break;
                        }
                    }
                    cursor.close();

                    if (!columnExists) {
                        db.execSQL("ALTER TABLE " + TABLE_LEADS + " ADD COLUMN isLead INTEGER DEFAULT 1");
                        Log.d("HiveLeadsDbHelper", "✅ Added isLead column to Hive database");

                        // Update existing records to have isLead = 1 (true) by default
                        db.execSQL("UPDATE " + TABLE_LEADS + " SET isLead = 1 WHERE isLead IS NULL");
                        Log.d("HiveLeadsDbHelper", "✅ Updated existing records with isLead = 1");
                    } else {
                        Log.d("HiveLeadsDbHelper", "✅ isLead column already exists in Hive database");
                    }
                } catch (Exception e) {
                    Log.e("HiveLeadsDbHelper", "❌ Error adding isLead column: " + e.getMessage());
                    // If ALTER fails, recreate the tables
                    Log.d("HiveLeadsDbHelper", "🔄 Recreating tables due to upgrade error");
                    db.execSQL("DROP TABLE IF EXISTS " + TABLE_PHONE_LOOKUP);
                    db.execSQL("DROP TABLE IF EXISTS " + TABLE_LEADS);
                    onCreate(db);
                }
            }
        }
    }

    /**
     * Clear Hive Native database to remove duplicates
     */
    public boolean clearHiveNativeDatabase() {
        try {
            Log.d(TAG, "🗑️ [HIVE_DB_CLEAR] Clearing Hive Native database to remove duplicates...");

            HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            // Clear both tables
            int deletedLeads = db.delete(HiveLeadsDbHelper.TABLE_LEADS, null, null);
            int deletedLookups = db.delete(HiveLeadsDbHelper.TABLE_PHONE_LOOKUP, null, null);
            db.close();

            // Clear metadata
            SharedPreferences prefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("has_data", false)
                .putInt("total_leads", 0)
                .putLong("last_update_time", 0)
                .apply();

            Log.d(TAG, "✅ [HIVE_DB_CLEAR] Cleared " + deletedLeads + " leads and " + deletedLookups + " phone lookup entries");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "❌ [HIVE_DB_CLEAR] Error clearing Hive Native database: " + e.getMessage());
            return false;
        }
    }

    /**
     * Check and log current database status for debugging
     */
    private void checkDatabaseStatus() {
        try {
            Log.d(TAG, "🔍 [DATABASE_STATUS] === CHECKING ALL DATABASE STATUS ===");

            // Check Hive native database status
            SharedPreferences hiveDbPrefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            boolean hiveHasData = hiveDbPrefs.getBoolean("has_data", false);
            int hiveTotalLeads = hiveDbPrefs.getInt("total_leads", 0);
            long hiveLastUpdate = hiveDbPrefs.getLong("last_update_time", 0);

            Log.d(TAG, "🔍 [DATABASE_STATUS] Hive Native DB: hasData=" + hiveHasData + ", totalLeads=" + hiveTotalLeads + ", lastUpdate=" + new java.util.Date(hiveLastUpdate));

            // Check original native database status
            SharedPreferences nativeDbPrefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
            boolean nativeHasData = nativeDbPrefs.getBoolean("has_data", false);
            int nativeTotalLeads = nativeDbPrefs.getInt("total_leads", 0);
            long nativeLastUpdate = nativeDbPrefs.getLong("last_update_time", 0);

            Log.d(TAG, "🔍 [DATABASE_STATUS] Native SQLite DB: hasData=" + nativeHasData + ", totalLeads=" + nativeTotalLeads + ", lastUpdate=" + new java.util.Date(nativeLastUpdate));

            // Check if Flutter has data
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity != null) {
                Log.d(TAG, "🔍 [DATABASE_STATUS] Flutter is available - can trigger sync");
            } else {
                Log.d(TAG, "🔍 [DATABASE_STATUS] Flutter is NOT available - cannot trigger sync");
            }

            Log.d(TAG, "🔍 [DATABASE_STATUS] === END DATABASE STATUS CHECK ===");

        } catch (Exception e) {
            Log.e(TAG, "❌ [DATABASE_STATUS] Error checking database status: " + e.getMessage());
        }
    }

    /**
     * Force trigger Hive to native sync for testing
     */
    public static void forceTriggerHiveToNativeSync(Context context) {
        try {
            Log.d(TAG, "🔧 [FORCE_SYNC] Manually triggering Hive to native sync...");

            // Try to get the running service instance first
            MainActivity mainActivity = MainActivity.getInstance();
            if (mainActivity != null) {
                MethodChannel methodChannel = mainActivity.getDataSyncMethodChannel();
                if (methodChannel != null) {
                    Log.d(TAG, "🔧 [FORCE_SYNC] Using Flutter method channel to trigger sync...");
                    methodChannel.invokeMethod("syncHiveToNative", null, new MethodChannel.Result() {
                        @Override
                        public void success(Object result) {
                            Log.d(TAG, "✅ [FORCE_SYNC] Hive to native sync completed successfully: " + result);
                        }

                        @Override
                        public void error(String errorCode, String errorMessage, Object errorDetails) {
                            Log.e(TAG, "❌ [FORCE_SYNC] Hive to native sync failed: " + errorCode + " - " + errorMessage);
                        }

                        @Override
                        public void notImplemented() {
                            Log.e(TAG, "❌ [FORCE_SYNC] Hive to native sync method not implemented");
                        }
                    });
                    return;
                }
            }

            Log.d(TAG, "⚠️ [FORCE_SYNC] Flutter not available - cannot trigger Hive to native sync");

        } catch (Exception e) {
            Log.e(TAG, "❌ [FORCE_SYNC] Error triggering Hive to native sync: " + e.getMessage());
        }
    }

    /**
     * Debug method to check and fix isLead field for specific contact
     */
    public static void debugCheckAndFixIsLeadField(Context context, String contactName, String phoneNumber) {
        try {
            HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(context);
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            Log.d(TAG, "🔍 [DEBUG] Checking isLead field for contact: " + contactName + " (" + phoneNumber + ")");

            // First, check if the isLead column exists
            Cursor cursor = db.rawQuery("PRAGMA table_info(" + HiveLeadsDbHelper.TABLE_LEADS + ")", null);
            boolean isLeadColumnExists = false;
            while (cursor.moveToNext()) {
                String columnName = cursor.getString(1);
                if ("isLead".equals(columnName)) {
                    isLeadColumnExists = true;
                    break;
                }
            }
            cursor.close();

            Log.d(TAG, "🔍 [DEBUG] isLead column exists: " + isLeadColumnExists);

            if (!isLeadColumnExists) {
                Log.d(TAG, "🔧 [DEBUG] Adding isLead column to database");
                db.execSQL("ALTER TABLE " + HiveLeadsDbHelper.TABLE_LEADS + " ADD COLUMN isLead INTEGER DEFAULT 1");
                Log.d(TAG, "✅ [DEBUG] isLead column added successfully");
            }

            // Now search for the specific contact
            String query = "SELECT id, name, contactNo, isLead FROM " + HiveLeadsDbHelper.TABLE_LEADS +
                          " WHERE name LIKE ? OR contactNo LIKE ?";
            cursor = db.rawQuery(query, new String[]{"%" + contactName + "%", "%" + phoneNumber + "%"});

            if (cursor.moveToFirst()) {
                do {
                    String id = cursor.getString(0);
                    String name = cursor.getString(1);
                    String contact = cursor.getString(2);
                    int isLeadValue = cursor.getInt(3);

                    Log.d(TAG, "🔍 [DEBUG] Found contact: ID=" + id + ", Name=" + name +
                          ", Contact=" + contact + ", isLead=" + isLeadValue);

                    // For testing purposes, let's set "Rohan karfa data" as a prospect (isLead = 0)
                    if (name != null && name.toLowerCase().contains("rohan")) {
                        Log.d(TAG, "🔧 [DEBUG] Setting Rohan as prospect (isLead = 0)");
                        db.execSQL("UPDATE " + HiveLeadsDbHelper.TABLE_LEADS + " SET isLead = 0 WHERE id = ?", new String[]{id});
                        Log.d(TAG, "✅ [DEBUG] Updated Rohan to be a prospect");
                    }

                } while (cursor.moveToNext());
            } else {
                Log.d(TAG, "❌ [DEBUG] Contact not found in database");
            }
            cursor.close();

        } catch (Exception e) {
            Log.e(TAG, "❌ [DEBUG] Error checking isLead field: " + e.getMessage());
        }
    }

    /**
     * Get leads data from native SQLite database for Flutter sync
     */
    public List<Map<String, Object>> getNativeLeadsData() {
        List<Map<String, Object>> leadsList = new ArrayList<>();

        try {
            NativeLeadsDbHelper dbHelper = new NativeLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            Cursor cursor = db.query(
                NativeLeadsDbHelper.TABLE_LEADS,
                null, // Select all columns
                null, // No WHERE clause
                null, // No WHERE args
                null, // No GROUP BY
                null, // No HAVING
                null  // No ORDER BY
            );

            Log.d(TAG, "🔍 [NATIVE_DB] Found " + cursor.getCount() + " leads in native database");

            while (cursor.moveToNext()) {
                Map<String, Object> leadMap = new HashMap<>();

                leadMap.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                leadMap.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                leadMap.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                leadMap.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                leadMap.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));
                leadMap.put("isDeleted", cursor.getInt(cursor.getColumnIndexOrThrow("isDeleted")) == 1);

                // Add isLead field - check if column exists first (for backward compatibility)
                try {
                    int isLeadIndex = cursor.getColumnIndex("isLead");
                    if (isLeadIndex >= 0) {
                        leadMap.put("isLead", cursor.getInt(isLeadIndex) == 1);
                    } else {
                        // Default to true for backward compatibility
                        leadMap.put("isLead", true);
                    }
                } catch (Exception e) {
                    // Default to true for backward compatibility
                    leadMap.put("isLead", true);
                }

                leadMap.put("lastModifiedOn", cursor.getString(cursor.getColumnIndexOrThrow("lastModifiedOn")));
                leadMap.put("lastSyncedAt", cursor.getString(cursor.getColumnIndexOrThrow("lastSyncedAt")));

                leadsList.add(leadMap);
            }

            cursor.close();
            db.close();

            Log.d(TAG, "✅ [NATIVE_DB] Retrieved " + leadsList.size() + " leads from native database");

        } catch (Exception e) {
            Log.e(TAG, "❌ [NATIVE_DB] Error reading from native database: " + e.getMessage());
        }

        return leadsList;
    }

    /**
     * Clear native SQLite database after successful sync to Hive
     */
    public boolean clearNativeLeadsData() {
        try {
            NativeLeadsDbHelper dbHelper = new NativeLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            int deletedRows = db.delete(NativeLeadsDbHelper.TABLE_LEADS, null, null);
            db.close();

            // Clear metadata
            SharedPreferences prefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("has_data", false)
                .putInt("total_leads", 0)
                .apply();

            Log.d(TAG, "✅ [NATIVE_DB] Cleared " + deletedRows + " leads from native database");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "❌ [NATIVE_DB] Error clearing native database: " + e.getMessage());
            return false;
        }
    }

    /**
     * Sync Native SQLite leads to Hive Native database for call detection (Incremental Update)
     */
    private boolean syncNativeLeadsToHiveNativeDatabase(List<Map<String, Object>> nativeLeads) {
        // Use synchronization to prevent concurrent access to Hive Native DB
        synchronized (HIVE_DB_LOCK) {
            try {
                Log.d(TAG, "💾 [NATIVE_TO_HIVE_NATIVE] Incrementally syncing " + nativeLeads.size() + " leads from Native SQLite to Hive Native DB...");

                HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(this);
                SQLiteDatabase db = dbHelper.getWritableDatabase();

            // Use incremental update instead of clearing all data
            Log.d(TAG, "🔄 [NATIVE_TO_HIVE_NATIVE] Using incremental update - checking existing records...");

            // Insert/Update leads from Native SQLite database using incremental approach
            db.beginTransaction();
            try {
                int leadsCount = 0;
                int prospectsCount = 0;
                int updatedCount = 0;
                int insertedCount = 0;

                for (Map<String, Object> leadData : nativeLeads) {
                    String id = leadData.get("id") != null ? leadData.get("id").toString() : "";
                    String name = leadData.get("name") != null ? leadData.get("name").toString() : "";
                    String contactNo = leadData.get("contactNo") != null ? leadData.get("contactNo").toString() : "";
                    String alternateContactNo = leadData.get("alternateContactNo") != null ? leadData.get("alternateContactNo").toString() : "";
                    String assignTo = leadData.get("assignTo") != null ? leadData.get("assignTo").toString() : "";
                    boolean isDeleted = leadData.get("isDeleted") != null ? (Boolean) leadData.get("isDeleted") : false;
                    boolean isLead = leadData.get("isLead") != null ? (Boolean) leadData.get("isLead") : true;
                    String lastModifiedOn = leadData.get("lastModifiedOn") != null ? leadData.get("lastModifiedOn").toString() : "";
                    String lastSyncedAt = getCurrentISOTime();

                    // Count leads vs prospects
                    if (isLead) {
                        leadsCount++;
                    } else {
                        prospectsCount++;
                    }

                    // Check if record already exists
                    Cursor existingCursor = db.query(
                        HiveLeadsDbHelper.TABLE_LEADS,
                        new String[]{"id"},
                        "id = ?",
                        new String[]{id},
                        null, null, null
                    );

                    boolean recordExists = existingCursor.moveToFirst();
                    existingCursor.close();

                    // Use INSERT OR REPLACE for upsert functionality
                    String upsertLeadSql = "INSERT OR REPLACE INTO " + HiveLeadsDbHelper.TABLE_LEADS +
                        " (id, name, contactNo, alternateContactNo, assignTo, isDeleted, isLead, lastModifiedOn, lastSyncedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    db.execSQL(upsertLeadSql, new Object[]{
                        id, name, contactNo, alternateContactNo, assignTo,
                        isDeleted ? 1 : 0, isLead ? 1 : 0, lastModifiedOn, lastSyncedAt
                    });

                    if (recordExists) {
                        updatedCount++;
                    } else {
                        insertedCount++;
                    }

                    // Update phone lookup entries (remove old ones for this ID first, then insert new ones)
                    if (!isDeleted) {
                        // Remove existing phone lookup entries for this lead
                        db.delete(HiveLeadsDbHelper.TABLE_PHONE_LOOKUP, "lead_id = ?", new String[]{id});
                        // Insert new phone lookup entries
                        insertPhoneLookupEntries(db, id, contactNo, alternateContactNo);
                    } else {
                        // If deleted, remove all phone lookup entries for this lead
                        db.delete(HiveLeadsDbHelper.TABLE_PHONE_LOOKUP, "lead_id = ?", new String[]{id});
                    }
                }

                db.setTransactionSuccessful();
                Log.d(TAG, "✅ [NATIVE_TO_HIVE_NATIVE] Successfully synced " + nativeLeads.size() + " records (incremental)");
                Log.d(TAG, "📊 [NATIVE_TO_HIVE_NATIVE] Breakdown: " + leadsCount + " leads, " + prospectsCount + " prospects");
                Log.d(TAG, "📊 [NATIVE_TO_HIVE_NATIVE] Operations: " + insertedCount + " inserted, " + updatedCount + " updated");

            } finally {
                db.endTransaction();
            }

            db.close();

            // Update metadata for Hive database
            SharedPreferences prefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("has_data", nativeLeads.size() > 0)
                .putInt("total_leads", nativeLeads.size())
                .putLong("last_update_time", System.currentTimeMillis())
                .apply();

                Log.d(TAG, "✅ [NATIVE_TO_HIVE_NATIVE] Successfully synced " + nativeLeads.size() + " leads from Native SQLite to Hive Native DB");
                return true;

            } catch (Exception e) {
                Log.e(TAG, "❌ [NATIVE_TO_HIVE_NATIVE] Error syncing Native SQLite to Hive Native DB: " + e.getMessage());
                return false;
            }
        } // End synchronized block
    }

    /**
     * Save Hive data to separate Hive native database for call detection (Incremental Update)
     */
    public boolean saveHiveDataToNativeDatabase(List<Map<String, Object>> hiveData) {
        try {
            Log.d(TAG, "💾 [HIVE_DB] Starting incremental update of " + hiveData.size() + " leads from Hive to separate native database...");
            Log.d(TAG, "💾 [HIVE_DB] Current time: " + new java.util.Date().toString());

            HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getWritableDatabase();

            // Use incremental update instead of clearing all data
            Log.d(TAG, "🔄 [HIVE_DB] Using incremental update - checking existing records...");

            // Insert/Update leads from Hive with phone lookup optimization (incremental)
            db.beginTransaction();
            int hiveLeadsCount = 0;
            int hiveProspectsCount = 0;
            int updatedCount = 0;
            int insertedCount = 0;
            try {
                for (Map<String, Object> leadData : hiveData) {
                    String id = leadData.get("id") != null ? leadData.get("id").toString() : "";
                    String name = leadData.get("name") != null ? leadData.get("name").toString() : "";
                    String contactNo = leadData.get("contactNo") != null ? leadData.get("contactNo").toString() : "";
                    String alternateContactNo = leadData.get("alternateContactNo") != null ? leadData.get("alternateContactNo").toString() : "";
                    String assignTo = leadData.get("assignTo") != null ? leadData.get("assignTo").toString() : "";
                    boolean isDeleted = leadData.get("isDeleted") != null ? (Boolean) leadData.get("isDeleted") : false;
                    String lastModifiedOn = leadData.get("lastModifiedOn") != null ? leadData.get("lastModifiedOn").toString() : "";
                    String lastSyncedAt = getCurrentISOTime();

                    // Get isLead field from data (default to true for backward compatibility)
                    boolean isLead = leadData.get("isLead") != null ? (Boolean) leadData.get("isLead") : true;

                    // Count leads vs prospects
                    if (isLead) {
                        hiveLeadsCount++;
                    } else {
                        hiveProspectsCount++;
                    }

                    // Check if record already exists
                    Cursor existingCursor = db.query(
                        HiveLeadsDbHelper.TABLE_LEADS,
                        new String[]{"id"},
                        "id = ?",
                        new String[]{id},
                        null, null, null
                    );

                    boolean recordExists = existingCursor.moveToFirst();
                    existingCursor.close();

                    // Use INSERT OR REPLACE for upsert functionality
                    String upsertLeadSql = "INSERT OR REPLACE INTO " + HiveLeadsDbHelper.TABLE_LEADS +
                        " (id, name, contactNo, alternateContactNo, assignTo, isDeleted, isLead, lastModifiedOn, lastSyncedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    db.execSQL(upsertLeadSql, new Object[]{
                        id, name, contactNo, alternateContactNo, assignTo,
                        isDeleted ? 1 : 0, isLead ? 1 : 0, lastModifiedOn, lastSyncedAt
                    });

                    if (recordExists) {
                        updatedCount++;
                    } else {
                        insertedCount++;
                    }

                    // Update phone lookup entries (remove old ones for this ID first, then insert new ones)
                    if (!isDeleted) {
                        // Remove existing phone lookup entries for this lead
                        db.delete(HiveLeadsDbHelper.TABLE_PHONE_LOOKUP, "lead_id = ?", new String[]{id});
                        // Insert new phone lookup entries
                        insertPhoneLookupEntries(db, id, contactNo, alternateContactNo);
                    } else {
                        // If deleted, remove all phone lookup entries for this lead
                        db.delete(HiveLeadsDbHelper.TABLE_PHONE_LOOKUP, "lead_id = ?", new String[]{id});
                    }
                }

                db.setTransactionSuccessful();
                Log.d(TAG, "✅ [HIVE_DB] Successfully processed " + hiveData.size() + " Hive records with phone lookup optimization (incremental)");
                Log.d(TAG, "📊 [HIVE_DB] Hive data breakdown: " + hiveLeadsCount + " leads, " + hiveProspectsCount + " prospects");
                Log.d(TAG, "📊 [HIVE_DB] Operations: " + insertedCount + " inserted, " + updatedCount + " updated");

            } finally {
                db.endTransaction();
            }

            db.close();

            int totalLeads = hiveData.size();

            // Update metadata for Hive database
            SharedPreferences prefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("has_data", totalLeads > 0)
                .putInt("total_leads", totalLeads)
                .putLong("last_update_time", System.currentTimeMillis())
                .apply();

            Log.d(TAG, "✅ [HIVE_DB] Successfully synced " + totalLeads + " records from Hive to separate native database (incremental)");
            Log.d(TAG, "📊 [HIVE_DB] Final breakdown: " + hiveLeadsCount + " leads, " + hiveProspectsCount + " prospects");
            Log.d(TAG, "📊 [HIVE_DB] Final operations: " + insertedCount + " inserted, " + updatedCount + " updated");
            Log.d(TAG, "🔍 [HIVE_DB] Database should now be available for call detection with " + totalLeads + " records");
            Log.d(TAG, "ℹ️ [HIVE_DB] Note: Using incremental update instead of clearing entire database");
            return true;

        } catch (Exception e) {
            Log.e(TAG, "❌ [HIVE_DB] Error saving Hive data to separate native database: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get leads data from Hive native database for call detection
     */
    public List<Map<String, Object>> getHiveLeadsData() {
        List<Map<String, Object>> leadsList = new ArrayList<>();

        try {
            HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            Cursor cursor = db.query(
                HiveLeadsDbHelper.TABLE_LEADS,
                null, // Select all columns
                "isDeleted = 0 AND isArchived = 0", // Only non-deleted and non-archived leads
                null, // No WHERE args
                null, // No GROUP BY
                null, // No HAVING
                null  // No ORDER BY
            );

            Log.d(TAG, "🔍 [HIVE_DB] Found " + cursor.getCount() + " leads in Hive native database");

            while (cursor.moveToNext()) {
                Map<String, Object> leadMap = new HashMap<>();

                leadMap.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                leadMap.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                leadMap.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                leadMap.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                leadMap.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));
                leadMap.put("isDeleted", cursor.getInt(cursor.getColumnIndexOrThrow("isDeleted")) == 1);

                // Add isLead field - check if column exists first (for backward compatibility)
                try {
                    int isLeadIndex = cursor.getColumnIndex("isLead");
                    if (isLeadIndex >= 0) {
                        leadMap.put("isLead", cursor.getInt(isLeadIndex) == 1);
                    } else {
                        // Default to true for backward compatibility
                        leadMap.put("isLead", true);
                    }
                } catch (Exception e) {
                    // Default to true for backward compatibility
                    leadMap.put("isLead", true);
                }

                leadMap.put("lastModifiedOn", cursor.getString(cursor.getColumnIndexOrThrow("lastModifiedOn")));
                leadMap.put("lastSyncedAt", cursor.getString(cursor.getColumnIndexOrThrow("lastSyncedAt")));

                leadsList.add(leadMap);
            }

            cursor.close();
            db.close();

            Log.d(TAG, "✅ [HIVE_DB] Retrieved " + leadsList.size() + " leads from Hive native database");

        } catch (Exception e) {
            Log.e(TAG, "❌ [HIVE_DB] Error reading from Hive native database: " + e.getMessage());
        }

        return leadsList;
    }

    /**
     * Insert phone lookup entries for ultra-fast search
     */
    private void insertPhoneLookupEntries(SQLiteDatabase db, String leadId, String contactNo, String alternateContactNo) {
        try {
            // Insert primary contact number
            if (contactNo != null && !contactNo.trim().isEmpty()) {
                insertPhoneLookupEntry(db, leadId, contactNo, "primary");
            }

            // Insert alternate contact number
            if (alternateContactNo != null && !alternateContactNo.trim().isEmpty()) {
                insertPhoneLookupEntry(db, leadId, alternateContactNo, "alternate");
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ [PHONE_LOOKUP] Error inserting phone lookup entries: " + e.getMessage());
        }
    }

    /**
     * Insert a single phone lookup entry with all possible variations
     */
    private void insertPhoneLookupEntry(SQLiteDatabase db, String leadId, String phoneNumber, String phoneType) {
        try {
            // Generate all possible phone number variations for instant lookup
            String[] phoneVariations = generatePhoneVariations(phoneNumber);

            for (String variation : phoneVariations) {
                if (variation != null && !variation.trim().isEmpty()) {
                    String insertSql = "INSERT OR REPLACE INTO " + HiveLeadsDbHelper.TABLE_PHONE_LOOKUP +
                        " (phone_clean, lead_id, phone_type) VALUES (?, ?, ?)";
                    db.execSQL(insertSql, new Object[]{variation, leadId, phoneType});
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "❌ [PHONE_LOOKUP] Error inserting phone lookup entry: " + e.getMessage());
        }
    }

    /**
     * Generate all possible phone number variations for comprehensive matching
     */
    private String[] generatePhoneVariations(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return new String[0];
        }

        java.util.Set<String> variations = new java.util.HashSet<>();

        // Clean the phone number (remove all non-digits except +)
        String cleaned = phoneNumber.replaceAll("[^\\d+]", "");
        variations.add(cleaned);

        // Add original number
        variations.add(phoneNumber);

        // Add without country code variations
        if (cleaned.startsWith("+91")) {
            variations.add(cleaned.substring(3)); // Remove +91
            variations.add("91" + cleaned.substring(3)); // 91 prefix
        } else if (cleaned.startsWith("91") && cleaned.length() > 10) {
            variations.add(cleaned.substring(2)); // Remove 91
            variations.add("+" + cleaned); // Add + prefix
        }

        // Add last 10 digits (most common matching pattern)
        if (cleaned.length() >= 10) {
            variations.add(cleaned.substring(cleaned.length() - 10));
        }

        // Add with different prefixes for the base number
        String baseNumber = cleaned.replaceFirst("^(\\+91|91)", "");
        if (baseNumber.length() >= 10) {
            variations.add(baseNumber);
            variations.add("91" + baseNumber);
            variations.add("+91" + baseNumber);
        }

        return variations.toArray(new String[0]);
    }

    /**
     * Static ultra-fast search for lead by phone number using lookup table (for external access)
     */
    public static Map<String, Object> searchLeadInHiveDatabaseStatic(Context context, String phoneNumber) {
        long startTime = System.currentTimeMillis();

        try {
            Log.d(TAG, "⚡ [STATIC_ULTRA_FAST_SEARCH] Searching for phone number: " + phoneNumber);

            HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(context);
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            // STEP 1: Try ultra-fast lookup table search first
            Map<String, Object> result = searchInPhoneLookupTableStatic(db, phoneNumber);

            if (result != null) {
                long endTime = System.currentTimeMillis();
                Log.d(TAG, "⚡ [STATIC_ULTRA_FAST_SEARCH] ✅ Found via lookup table in " + (endTime - startTime) + "ms");
                db.close();
                return result;
            }

            // STEP 2: Fallback to indexed search if lookup table fails
            result = searchWithIndexedQueryStatic(db, phoneNumber);

            db.close();

            long endTime = System.currentTimeMillis();
            if (result != null) {
                Log.d(TAG, "⚡ [STATIC_ULTRA_FAST_SEARCH] ✅ Found via indexed search in " + (endTime - startTime) + "ms");
            } else {
                Log.d(TAG, "⚡ [STATIC_ULTRA_FAST_SEARCH] ❌ No lead found in " + (endTime - startTime) + "ms");
            }

            return result;

        } catch (Exception e) {
            Log.e(TAG, "❌ [STATIC_ULTRA_FAST_SEARCH] Error in ultra-fast search: " + e.getMessage());
            return null;
        }
    }

    /**
     * Ultra-fast search for lead by phone number using lookup table (instance method)
     */
    public Map<String, Object> searchLeadInHiveDatabase(String phoneNumber) {
        long startTime = System.currentTimeMillis();

        try {
            Log.d(TAG, "⚡ [ULTRA_FAST_SEARCH] Searching for phone number: " + phoneNumber);

            HiveLeadsDbHelper dbHelper = new HiveLeadsDbHelper(this);
            SQLiteDatabase db = dbHelper.getReadableDatabase();

            // STEP 1: Try ultra-fast lookup table search first
            Map<String, Object> result = searchInPhoneLookupTable(db, phoneNumber);

            if (result != null) {
                long endTime = System.currentTimeMillis();
                Log.d(TAG, "⚡ [ULTRA_FAST_SEARCH] ✅ Found via lookup table in " + (endTime - startTime) + "ms");
                db.close();
                return result;
            }

            // STEP 2: Fallback to indexed search if lookup table fails
            result = searchWithIndexedQuery(db, phoneNumber);

            db.close();

            long endTime = System.currentTimeMillis();
            if (result != null) {
                Log.d(TAG, "⚡ [ULTRA_FAST_SEARCH] ✅ Found via indexed search in " + (endTime - startTime) + "ms");
            } else {
                Log.d(TAG, "⚡ [ULTRA_FAST_SEARCH] ❌ No lead found in " + (endTime - startTime) + "ms");
            }

            return result;

        } catch (Exception e) {
            Log.e(TAG, "❌ [ULTRA_FAST_SEARCH] Error in ultra-fast search: " + e.getMessage());
            return null;
        }
    }

    /**
     * Static search in phone lookup table for instant results
     */
    private static Map<String, Object> searchInPhoneLookupTableStatic(SQLiteDatabase db, String phoneNumber) {
        try {
            // Generate phone variations to search
            String[] phoneVariations = generatePhoneVariationsStatic(phoneNumber);

            for (String variation : phoneVariations) {
                if (variation == null || variation.trim().isEmpty()) continue;

                // Query lookup table
                String lookupSql = "SELECT l.* FROM " + HiveLeadsDbHelper.TABLE_LEADS + " l " +
                    "INNER JOIN " + HiveLeadsDbHelper.TABLE_PHONE_LOOKUP + " p ON l.id = p.lead_id " +
                    "WHERE p.phone_clean = ? AND l.isDeleted = 0 AND l.isArchived = 0 LIMIT 1";

                Cursor cursor = db.rawQuery(lookupSql, new String[]{variation});

                if (cursor.moveToFirst()) {
                    Map<String, Object> leadData = new HashMap<>();
                    leadData.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                    leadData.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                    leadData.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                    leadData.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                    leadData.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));

                    // Add isLead field - check if column exists first (for backward compatibility)
                    try {
                        int isLeadIndex = cursor.getColumnIndex("isLead");
                        if (isLeadIndex >= 0) {
                            boolean isLead = cursor.getInt(isLeadIndex) == 1;
                            leadData.put("isLead", isLead);
                            Log.d(TAG, "⚡ [STATIC_LOOKUP_TABLE] Found match with isLead=" + isLead + " for variation: " + variation);
                        } else {
                            // Default to true for backward compatibility
                            leadData.put("isLead", true);
                            Log.d(TAG, "⚡ [STATIC_LOOKUP_TABLE] Found match (isLead column not found, defaulting to true) for variation: " + variation);
                        }
                    } catch (Exception e) {
                        // Default to true for backward compatibility
                        leadData.put("isLead", true);
                        Log.d(TAG, "⚡ [STATIC_LOOKUP_TABLE] Found match (error getting isLead: " + e.getMessage() + ", defaulting to true) for variation: " + variation);
                    }

                    cursor.close();
                    return leadData;
                }

                cursor.close();
            }

            return null;

        } catch (Exception e) {
            Log.e(TAG, "❌ [STATIC_LOOKUP_TABLE] Error in lookup table search: " + e.getMessage());
            return null;
        }
    }

    /**
     * Static fallback indexed search method
     */
    private static Map<String, Object> searchWithIndexedQueryStatic(SQLiteDatabase db, String phoneNumber) {
        try {
            // Generate phone variations
            String[] phoneVariations = generatePhoneVariationsStatic(phoneNumber);

            // Build dynamic WHERE clause for all variations
            StringBuilder whereClause = new StringBuilder("(");
            String[] selectionArgs = new String[phoneVariations.length * 2];

            for (int i = 0; i < phoneVariations.length; i++) {
                if (i > 0) whereClause.append(" OR ");
                whereClause.append("contactNo = ? OR alternateContactNo = ?");
                selectionArgs[i * 2] = phoneVariations[i];
                selectionArgs[i * 2 + 1] = phoneVariations[i];
            }
            whereClause.append(") AND isDeleted = 0 AND isArchived = 0");

            Cursor cursor = db.query(
                HiveLeadsDbHelper.TABLE_LEADS,
                null, // Select all columns
                whereClause.toString(),
                selectionArgs,
                null, // No GROUP BY
                null, // No HAVING
                null, // No ORDER BY
                "1"   // Limit to 1 result
            );

            Map<String, Object> leadData = null;

            if (cursor.moveToFirst()) {
                leadData = new HashMap<>();
                leadData.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                leadData.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                leadData.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                leadData.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                leadData.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));

                // Add isLead field - check if column exists first (for backward compatibility)
                try {
                    int isLeadIndex = cursor.getColumnIndex("isLead");
                    if (isLeadIndex >= 0) {
                        boolean isLead = cursor.getInt(isLeadIndex) == 1;
                        leadData.put("isLead", isLead);
                        Log.d(TAG, "⚡ [STATIC_INDEXED_SEARCH] Found match with isLead=" + isLead);
                    } else {
                        // Default to true for backward compatibility
                        leadData.put("isLead", true);
                        Log.d(TAG, "⚡ [STATIC_INDEXED_SEARCH] Found match (defaulting isLead=true)");
                    }
                } catch (Exception e) {
                    // Default to true for backward compatibility
                    leadData.put("isLead", true);
                    Log.d(TAG, "⚡ [STATIC_INDEXED_SEARCH] Found match (error getting isLead, defaulting to true)");
                }
            }

            cursor.close();
            return leadData;

        } catch (Exception e) {
            Log.e(TAG, "❌ [STATIC_INDEXED_SEARCH] Error in indexed search: " + e.getMessage());
            return null;
        }
    }

    /**
     * Static version of phone variations generator
     */
    private static String[] generatePhoneVariationsStatic(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return new String[0];
        }

        java.util.Set<String> variations = new java.util.HashSet<>();

        // Clean the phone number (remove all non-digits except +)
        String cleaned = phoneNumber.replaceAll("[^\\d+]", "");
        variations.add(cleaned);

        // Add original number
        variations.add(phoneNumber);

        // Add without country code variations
        if (cleaned.startsWith("+91")) {
            variations.add(cleaned.substring(3)); // Remove +91
            variations.add("91" + cleaned.substring(3)); // 91 prefix
        } else if (cleaned.startsWith("91") && cleaned.length() > 10) {
            variations.add(cleaned.substring(2)); // Remove 91
            variations.add("+" + cleaned); // Add + prefix
        }

        // Add last 10 digits (most common matching pattern)
        if (cleaned.length() >= 10) {
            variations.add(cleaned.substring(cleaned.length() - 10));
        }

        // Add with different prefixes for the base number
        String baseNumber = cleaned.replaceFirst("^(\\+91|91)", "");
        if (baseNumber.length() >= 10) {
            variations.add(baseNumber);
            variations.add("91" + baseNumber);
            variations.add("+91" + baseNumber);
        }

        return variations.toArray(new String[0]);
    }

    /**
     * Search in phone lookup table for instant results (instance method)
     */
    private Map<String, Object> searchInPhoneLookupTable(SQLiteDatabase db, String phoneNumber) {
        try {
            // Generate phone variations to search
            String[] phoneVariations = generatePhoneVariations(phoneNumber);

            for (String variation : phoneVariations) {
                if (variation == null || variation.trim().isEmpty()) continue;

                // Query lookup table
                String lookupSql = "SELECT l.* FROM " + HiveLeadsDbHelper.TABLE_LEADS + " l " +
                    "INNER JOIN " + HiveLeadsDbHelper.TABLE_PHONE_LOOKUP + " p ON l.id = p.lead_id " +
                    "WHERE p.phone_clean = ? AND l.isDeleted = 0 AND l.isArchived = 0 LIMIT 1";

                Cursor cursor = db.rawQuery(lookupSql, new String[]{variation});

                if (cursor.moveToFirst()) {
                    Map<String, Object> leadData = new HashMap<>();
                    leadData.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                    leadData.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                    leadData.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                    leadData.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                    leadData.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));

                    // Add isLead field - check if column exists first (for backward compatibility)
                    try {
                        int isLeadIndex = cursor.getColumnIndex("isLead");
                        if (isLeadIndex >= 0) {
                            boolean isLead = cursor.getInt(isLeadIndex) == 1;
                            leadData.put("isLead", isLead);
                            Log.d(TAG, "⚡ [LOOKUP_TABLE] Found match with isLead=" + isLead + " for variation: " + variation);
                        } else {
                            // Default to true for backward compatibility
                            leadData.put("isLead", true);
                            Log.d(TAG, "⚡ [LOOKUP_TABLE] Found match (defaulting isLead=true) for variation: " + variation);
                        }
                    } catch (Exception e) {
                        // Default to true for backward compatibility
                        leadData.put("isLead", true);
                        Log.d(TAG, "⚡ [LOOKUP_TABLE] Found match (error getting isLead, defaulting to true) for variation: " + variation);
                    }

                    cursor.close();
                    return leadData;
                }

                cursor.close();
            }

            return null;

        } catch (Exception e) {
            Log.e(TAG, "❌ [LOOKUP_TABLE] Error in lookup table search: " + e.getMessage());
            return null;
        }
    }

    /**
     * Fallback indexed search method
     */
    private Map<String, Object> searchWithIndexedQuery(SQLiteDatabase db, String phoneNumber) {
        try {
            // Generate phone variations
            String[] phoneVariations = generatePhoneVariations(phoneNumber);

            // Build dynamic WHERE clause for all variations
            StringBuilder whereClause = new StringBuilder("(");
            String[] selectionArgs = new String[phoneVariations.length * 2];

            for (int i = 0; i < phoneVariations.length; i++) {
                if (i > 0) whereClause.append(" OR ");
                whereClause.append("contactNo = ? OR alternateContactNo = ?");
                selectionArgs[i * 2] = phoneVariations[i];
                selectionArgs[i * 2 + 1] = phoneVariations[i];
            }
            whereClause.append(") AND isDeleted = 0 AND isArchived = 0");

            Cursor cursor = db.query(
                HiveLeadsDbHelper.TABLE_LEADS,
                null, // Select all columns
                whereClause.toString(),
                selectionArgs,
                null, // No GROUP BY
                null, // No HAVING
                null, // No ORDER BY
                "1"   // Limit to 1 result
            );

            Map<String, Object> leadData = null;

            if (cursor.moveToFirst()) {
                leadData = new HashMap<>();
                leadData.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                leadData.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                leadData.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                leadData.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                leadData.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));

                // Add isLead field - check if column exists first (for backward compatibility)
                try {
                    int isLeadIndex = cursor.getColumnIndex("isLead");
                    if (isLeadIndex >= 0) {
                        boolean isLead = cursor.getInt(isLeadIndex) == 1;
                        leadData.put("isLead", isLead);
                        Log.d(TAG, "⚡ [INDEXED_SEARCH] Found match with isLead=" + isLead);
                    } else {
                        // Default to true for backward compatibility
                        leadData.put("isLead", true);
                        Log.d(TAG, "⚡ [INDEXED_SEARCH] Found match (defaulting isLead=true)");
                    }
                } catch (Exception e) {
                    // Default to true for backward compatibility
                    leadData.put("isLead", true);
                    Log.d(TAG, "⚡ [INDEXED_SEARCH] Found match (error getting isLead, defaulting to true)");
                }
            }

            cursor.close();
            return leadData;

        } catch (Exception e) {
            Log.e(TAG, "❌ [INDEXED_SEARCH] Error in indexed search: " + e.getMessage());
            return null;
        }
    }

    /**
     * Get current time in ISO format (UTC, simple format without microseconds)
     */
    private String getCurrentISOTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'", Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        return sdf.format(new Date());
    }

    /**
     * Create notification channel for Android 8.0+
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "Data Sync Service",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("Background data synchronization service");
            channel.setShowBadge(false);

            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
                Log.d(TAG, "Notification channel created");
            }
        }
    }

    /**
     * Create notification for foreground service
     */
    private Notification createNotification() {
        return createNotification("Data sync service running");
    }

    /**
     * Create notification with custom message
     */
    private Notification createNotification(String message) {
        Intent notificationIntent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this,
            0,
            notificationIntent,
            PendingIntent.FLAG_IMMUTABLE
        );

        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Syncing the data")
            .setContentText(message)
            .setSmallIcon(android.R.drawable.ic_dialog_info) // Using system icon for now
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .setCategory(NotificationCompat.CATEGORY_SERVICE)
            .build();
    }

    /**
     * Update notification with new message
     */
    private void updateNotification(String message) {
        try {
            NotificationManager manager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            if (manager != null) {
                Notification notification = createNotification(message);
                manager.notify(NOTIFICATION_ID, notification);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error updating notification: " + e.getMessage());
        }
    }

    /**
     * Check if the service is currently running
     */
    public static boolean isServiceRunning(Context context) {
        android.app.ActivityManager manager = (android.app.ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        if (manager != null) {
            for (android.app.ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
                if (DataSyncService.class.getName().equals(service.service.getClassName())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * Start the data sync service
     */
    public static void startService(Context context) {
        try {
            Intent serviceIntent = new Intent(context, DataSyncService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
            Log.d(TAG, "Data sync service start requested");
        } catch (Exception e) {
            Log.e(TAG, "Error starting data sync service: " + e.getMessage());
        }
    }

    /**
     * Stop the data sync service
     */
    public static void stopService(Context context) {
        try {
            Intent serviceIntent = new Intent(context, DataSyncService.class);
            context.stopService(serviceIntent);
            Log.d(TAG, "Data sync service stop requested");
        } catch (Exception e) {
            Log.e(TAG, "Error stopping data sync service: " + e.getMessage());
        }
    }

    /**
     * Get sync statistics
     */
    public static String getSyncStats(Context context) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
            String lastSyncTime = prefs.getString(KEY_LAST_SYNC_TIME, "Never");
            int totalLeads = prefs.getInt(KEY_TOTAL_LEADS, 0);

            return "Last sync: " + lastSyncTime + ", Total leads: " + totalLeads;
        } catch (Exception e) {
            Log.e(TAG, "Error getting sync stats: " + e.getMessage());
            return "Stats unavailable";
        }
    }
}
