package com.leadrat.call_detection.call_detection;

import android.Manifest;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import androidx.core.content.ContextCompat;

import com.leadrat.call_detection.call_detection.services.CallDetectionService;

public class <PERSON>otReceiver extends BroadcastReceiver {
    private static final String TAG = "BootReceiver";

    @Override
    public void onReceive(Context context, Intent intent) {
        if (intent.getAction() != null &&
            (intent.getAction().equals(Intent.ACTION_BOOT_COMPLETED) ||
             intent.getAction().equals(Intent.ACTION_MY_PACKAGE_REPLACED) ||
             intent.getAction().equals("android.intent.action.QUICKBOOT_POWERON"))) {

            Log.d(TAG, "📱 [BOOT] Boot completed or app updated, will start CallDetectionService with delay");

            // Check for required permissions before starting service
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                if (ContextCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED ||
                    ContextCompat.checkSelfPermission(context, Manifest.permission.READ_CALL_LOG) != PackageManager.PERMISSION_GRANTED) {

                    Log.e(TAG, "❌ [BOOT] Required permissions not granted. Cannot start CallDetectionService from BootReceiver.");
                    return; // Don't start service if permissions are not granted
                }
            }

            // Add a delay to ensure system is ready and prevent ANR
            // Use Handler to delay service start by 3 seconds
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                try {
                    Log.d(TAG, "🚀 [BOOT] Starting CallDetectionService after delay");
                    Intent serviceIntent = new Intent(context, CallDetectionService.class);
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                        context.startForegroundService(serviceIntent);
                        Log.d(TAG, "✅ [BOOT] CallDetectionService started as foreground service");
                    } else {
                        context.startService(serviceIntent);
                        Log.d(TAG, "✅ [BOOT] CallDetectionService started as regular service");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "❌ [BOOT] Failed to start CallDetectionService: " + e.getMessage());
                }
            }, 3000); // 3 second delay
        }
    }
}
