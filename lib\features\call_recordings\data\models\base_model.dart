import 'package:json_annotation/json_annotation.dart';
part 'base_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: true)
class BaseModel {
  final String? id;
  final DateTime? createdOn;
  final String? createdBy;
  final DateTime? lastModifiedOn;
  final String? lastModifiedBy;
  final bool? isNew;

  BaseModel({this.id, this.createdOn, this.createdBy, this.lastModifiedOn, this.lastModifiedBy, this.isNew});

  factory BaseModel.fromJson(Map<String, dynamic> json) =>
      _$BaseModelFromJson(json);

  Map<String, dynamic> toJson() => _$BaseModelToJson(this);
}