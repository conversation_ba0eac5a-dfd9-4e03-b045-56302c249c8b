import 'package:flutter/material.dart';

class TermsContentWidget extends StatelessWidget {
  final ScrollController scrollController;

  const TermsContentWidget({
    super.key,
    required this.scrollController,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Scrollbar(
        controller: scrollController,
        thumbVisibility: true,
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Terms and Conditions',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1A1A1A),
                ),
              ),
              const SizedBox(height: 16),
              const Text(
                'Last updated: $_lastUpdated',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                  fontStyle: FontStyle.italic,
                ),
              ),
              const SizedBox(height: 24),
              _buildSection(
                'Welcome to LeadRat Call Detection',
                'By using this application, you agree to the following terms and conditions. Please read them carefully before proceeding.',
              ),
              _buildSection(
                '1. Data Collection and Usage',
                '''We collect and process the following data to provide our call detection services:

• Call logs and phone numbers for lead tracking
• Call recordings (when enabled) for quality assurance
• Device information for service optimization
• Usage analytics to improve app performance

All data is stored locally on your device and is not transmitted to external servers without your explicit consent.''',
              ),
              _buildSection(
                '2. Permissions Required',
                '''This app requires the following permissions to function properly:

• Phone Access: To detect incoming and outgoing calls
• Call Log Access: To read call history for lead management
• Storage Access: To save call recordings and app data
• Microphone Access: For call recording functionality (when enabled)
• Background App Refresh: To maintain call detection service
• Battery Optimization Exemption: To ensure continuous operation

These permissions are essential for the app's core functionality.''',
              ),
              _buildSection(
                '3. Call Detection Service',
                '''Our call detection service:

• Runs in the background to monitor calls
• Automatically detects calls from leads in your database
• Provides real-time notifications about lead interactions
• Maintains call history for business analysis

The service operates locally on your device and does not share call information with third parties.''',
              ),
              _buildSection(
                '4. Data Security and Privacy',
                '''We are committed to protecting your privacy:

• All data is encrypted and stored securely on your device
• No personal information is shared without your consent
• You can delete all app data at any time
• Call recordings are stored locally and uploaded to cloud services
• We comply with applicable data protection regulations''',
              ),
              _buildSection(
                '5. User Responsibilities',
                '''By using this app, you agree to:

• Use the app in compliance with applicable laws
• Ensure you have proper consent for call recording where required
• Maintain the security of your device and app data
• Not attempt to reverse engineer or modify the app
• Report any security vulnerabilities to our support team''',
              ),
              _buildSection(
                '6. Service Availability',
                '''While we strive to provide reliable service:

• The app may experience occasional downtime for maintenance
• Features may be updated or modified without prior notice
• We are not liable for any business losses due to service interruptions
• Background services depend on device settings and OS limitations''',
              ),
              _buildSection(
                '7. Changes to Terms',
                '''We may update these terms from time to time:

• You will be notified of significant changes
• Continued use of the app constitutes acceptance of new terms
• You can review the latest terms within the app settings
• If you disagree with changes, you may discontinue using the app''',
              ),
              _buildSection(
                '8. Contact Information',
                '''If you have questions about these terms or our privacy practices:

• Email: <EMAIL>
• Website: www.leadrat.com
• Address: 1596 level, 3, 20th Main Rd, Agara Village, 1st Sector, HSR Layout, Bengaluru, Karnataka 560102

We are committed to addressing your concerns promptly.''',
              ),
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: const Color(0xFFE0E0E0)),
                ),
                child: const Text(
                  'By clicking "I Accept" below, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(String title, String content) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1A1A1A),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            content,
            style: const TextStyle(
              fontSize: 14,
              height: 1.5,
              color: Color(0xFF444444),
            ),
          ),
        ],
      ),
    );
  }

  static const String _lastUpdated = 'June 16, 2025';
}
