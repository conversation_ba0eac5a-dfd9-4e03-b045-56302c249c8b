abstract class LocalStorageService {
  /// Initializes the local storage service.
  Future<void> initialize();

  /// Clears all data from all storage containers.
  Future<void> clearAllData();

  /// Adds a single item to the specified storage container.
  Future<void> addItem<T>(String containerName, T item);

  /// Adds multiple items to the specified storage container.
  Future<void> addItems<T>(String containerName, List<T> items);

  /// Retrieves all items from the specified storage container.
  List<T> getAllItems<T>(String containerName);

  /// Removes a single item from the specified storage container.
  Future<void> removeItem<T>(String containerName, dynamic key);

  /// Clears all items from the specified storage container.
  Future<void> clearContainer(String containerName);

}