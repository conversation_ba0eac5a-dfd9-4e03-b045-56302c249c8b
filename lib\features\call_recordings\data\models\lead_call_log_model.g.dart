// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_call_log_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadCallLogModel _$LeadCallLogModelFromJson(Map<String, dynamic> json) =>
    LeadCallLogModel(
      leadId: json['leadId'] as String?,
      userId: json['userId'] as String?,
      callDirection:
          $enumDecodeNullable(_$CallDirectionEnumMap, json['callDirection']),
      callDuration: (json['callDuration'] as num?)?.toDouble(),
      callStartTime: json['callStartTime'] == null
          ? null
          : DateTime.parse(json['callStartTime'] as String),
      callEndTime: json['callEndTime'] == null
          ? null
          : DateTime.parse(json['callEndTime'] as String),
      notes: json['notes'] as String?,
      callStatus: $enumDecodeNullable(_$CallStatusEnumMap, json['callStatus']),
    );

Map<String, dynamic> _$LeadCallLogModelToJson(LeadCallLogModel instance) =>
    <String, dynamic>{
      'leadId': instance.leadId,
      'userId': instance.userId,
      'callDirection': _$CallDirectionEnumMap[instance.callDirection],
      'callDuration': instance.callDuration,
      'callStartTime': instance.callStartTime?.toIso8601String(),
      'callEndTime': instance.callEndTime?.toIso8601String(),
      'notes': instance.notes,
      'callStatus': _$CallStatusEnumMap[instance.callStatus],
    };

const _$CallDirectionEnumMap = {
  CallDirection.none: 'none',
  CallDirection.incoming: 'incoming',
  CallDirection.outgoing: 'outgoing',
};

const _$CallStatusEnumMap = {
  CallStatus.none: 'none',
  CallStatus.missed: 'missed',
  CallStatus.disconnected: 'disconnected',
  CallStatus.answered: 'answered',
  CallStatus.notConnected: 'notConnected',
};
