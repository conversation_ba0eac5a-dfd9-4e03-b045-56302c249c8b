# Offline Leads Debugging Guide

This guide explains how to monitor and debug the offline leads background sync system using the comprehensive logging that has been added throughout the codebase.

## 🔍 Debug Log Categories

All debug logs are prefixed with emojis and categories to make them easy to identify in the terminal:

### 🌐 [API CALL] - Remote Data Source
- API request details (URL, headers, parameters)
- Response status codes and timing
- Response body previews
- Error handling and network issues

### 💾 [DATABASE] - Local Data Source  
- Database operations (insert, update, delete, query)
- Record counts and data changes
- Sync time tracking
- Hive box operations

### 🔄 [REPOSITORY] - Repository Layer
- Sync operation coordination
- Data flow between remote and local sources
- Error handling and success reporting

### 🚀 [BACKGROUND_SYNC] - Background Service
- WorkManager initialization and task registration
- Periodic sync scheduling (every 1 minute)
- Background task execution
- Service configuration

### 🚀 [BACKGROUND_TASK] - Background Task Execution
- Individual background task runs
- Task success/failure status
- Execution timing

### 🔄 [BLOC] - Presentation Layer
- User-triggered sync operations
- UI state changes
- Manual sync requests

### 🔧 [SERVICE_INIT] - Service Initialization
- App startup initialization
- Service lifecycle management
- One-time sync triggers

## 📱 How to Monitor in Terminal

### 1. **Start the App with Debugging**
```bash
flutter run --debug
```

### 2. **Watch for Initialization Logs**
When the app starts, you'll see:
```
🚀 [SERVICE_INIT] Initializing offline leads background sync service...
🚀 [BACKGROUND_SYNC] Initializing WorkManager...
✅ [BACKGROUND_SYNC] WorkManager initialized successfully
⏰ [BACKGROUND_SYNC] Starting periodic sync (every 1 minute)...
✅ [BACKGROUND_SYNC] Periodic sync registered successfully
✅ [SERVICE_INIT] Periodic sync started
🎉 [SERVICE_INIT] Offline leads background sync service initialized successfully
```

### 3. **Monitor Background Sync (Every 1 Minute)**
```
🚀 [BACKGROUND_TASK] Background task started: offline_leads_sync
🚀 [BACKGROUND_TASK] Task execution time: 2024-01-01T12:00:00.000Z
🚀 [BACKGROUND_TASK] Executing sync task...
🔄 [BACKGROUND_SYNC] Starting background sync operation...
🔄 [BACKGROUND_SYNC] Initializing Hive adapters...
🔄 [BACKGROUND_SYNC] Creating data sources...
🔧 [BACKGROUND_SYNC] Using base URL: https://prd-mobile.leadrat.com
🔧 [BACKGROUND_SYNC] Using tenant ID: sleep
🔄 [BACKGROUND_SYNC] Creating repository...
🔄 [BACKGROUND_SYNC] Executing sync operation...
```

### 4. **Watch API Calls**
```
🌐 [API CALL] Starting API request...
🌐 [API CALL] URL: https://prd-mobile.leadrat.com/api/v1/lead/offline?UserId=df0a3af3-7d3d-41b5-8419-e1a6532cea8c&PageNumber=1&PageSize=500
🌐 [API CALL] Headers: {accept: application/json, tenant: sleep}
🌐 [API CALL] Response received in 1250ms
🌐 [API CALL] Status Code: 200
🌐 [API CALL] Response Body Length: 15420 characters
🌐 [API CALL] Response Preview: [{"id":"54891fb8-e193-4131-b7ef-2f88e22a0d7c","name":"Prathibha Bhaskar"...
✅ [API CALL] JSON parsed successfully. Found 150 leads
📋 [API CALL] Lead 1: {id: 54891fb8-e193-4131-b7ef-2f88e22a0d7c, name: Prathibha Bhaskar, contactNo: +919945012443, assignTo: 00000000-0000-0000-0000-000000000000, isDeleted: false}
```

### 5. **Monitor Database Operations**
```
💾 [DATABASE] Starting insertOrUpdateLeads with 150 leads
🔄 [DATABASE] Updating lead: {id: 54891fb8-e193-4131-b7ef-2f88e22a0d7c, name: Prathibha Bhaskar, contactNo: +919945012443}
➕ [DATABASE] Inserting new lead: {id: b27f2ea0-7886-4726-acc7-9a01d3ecceda, name: B-Dhruish EnterprisesHadapsar, contactNo: +919823331982}
✅ [DATABASE] insertOrUpdateLeads completed: 45 inserted, 105 updated
💾 [DATABASE] Total leads in database: 150
⏰ [DATABASE] Updating last sync time to: 2024-01-01T12:00:30.000Z
⏰ [DATABASE] Last sync time updated successfully
```

### 6. **Track Sync Completion**
```
🔄 [REPOSITORY] Received 150 leads from API
🔄 [REPOSITORY] Updating local database with 150 leads
🔄 [REPOSITORY] Sync completed successfully at 2024-01-01T12:00:30.000Z
✅ [REPOSITORY] Sync operation completed successfully. Total leads in database: 150
✅ [BACKGROUND_SYNC] Sync successful after 3s: 150 leads in database
🚀 [BACKGROUND_TASK] Sync task completed: SUCCESS
```

## 🔧 Manual Testing Commands

### Trigger Manual Sync from UI
When you tap the sync button in the UI:
```
🔄 [BLOC] Manual sync triggered by user
🔄 [BLOC] Executing sync leads use case...
✅ [BLOC] Sync successful: 150 leads received
```

### Start/Stop Background Sync
```
🚀 [BLOC] Starting background sync service...
✅ [BLOC] Background sync service started successfully

🛑 [BLOC] Stopping background sync service...
✅ [BLOC] Background sync service stopped successfully
```

### One-Time Sync
```
🔄 [SERVICE_INIT] Triggering one-time sync...
🔄 [BACKGROUND_SYNC] Triggering one-time sync (Task ID: one_time_sync_1704110430000)...
✅ [BACKGROUND_SYNC] One-time sync task registered
✅ [SERVICE_INIT] One-time sync triggered successfully
```

## ❌ Error Monitoring

### Network Errors
```
❌ [API CALL] Network error: Failed host lookup: 'lrb-qa-mobile.leadrat.info'
❌ [REPOSITORY] Sync operation failed: Network error: Failed host lookup
❌ [BACKGROUND_SYNC] Sync failed after 5s: Network error: Failed host lookup
🚀 [BACKGROUND_TASK] Sync task completed: FAILED
```

### API Errors
```
❌ [API CALL] Failed with status 401
❌ [API CALL] Error response: {"error": "Unauthorized"}
❌ [REPOSITORY] Sync operation failed: Failed to fetch leads: 401 - {"error": "Unauthorized"}
```

### Database Errors
```
❌ [DATABASE] Failed to insert lead: HiveError: Box not found
❌ [REPOSITORY] Sync operation failed: Failed to insert lead: HiveError: Box not found
```

## 📊 Performance Monitoring

### Timing Information
- API response times: `Response received in 1250ms`
- Sync duration: `Sync successful after 3s`
- Background task execution time: `Task execution time: 2024-01-01T12:00:00.000Z`

### Data Volume
- API response size: `Response Body Length: 15420 characters`
- Lead counts: `Found 150 leads`, `45 inserted, 105 updated`
- Database size: `Total leads in database: 150`

## 🛠️ Troubleshooting Common Issues

### 1. **Background Sync Not Running**
Look for:
```
✅ [BACKGROUND_SYNC] Periodic sync registered successfully
```
If missing, check WorkManager initialization.

### 2. **API Calls Failing**
Look for:
```
🌐 [API CALL] Status Code: 200
```
If you see 4xx/5xx codes, check API endpoint and credentials.

### 3. **Database Not Updating**
Look for:
```
✅ [DATABASE] insertOrUpdateLeads completed: X inserted, Y updated
```
If missing, check Hive initialization and permissions.

### 4. **No Background Tasks**
Look for:
```
🚀 [BACKGROUND_TASK] Background task started: offline_leads_sync
```
If missing every minute, check device battery optimization settings.

## 📝 Log Filtering

To filter logs in terminal, use grep:

```bash
# Only API calls
flutter logs | grep "API CALL"

# Only database operations  
flutter logs | grep "DATABASE"

# Only background sync
flutter logs | grep "BACKGROUND"

# Only errors
flutter logs | grep "❌"

# Only successful operations
flutter logs | grep "✅"
```

This comprehensive logging system will help you monitor every aspect of the offline leads synchronization process in real-time through your terminal.
