import 'package:json_annotation/json_annotation.dart';
part 'is_lead_or_data_model.g.dart';

@JsonSerializable(explicitToJson: true, includeIfNull: true)
class IsLeadOrDataModel {
  final bool? isLead;
  final bool? isData;

  IsLeadOrDataModel({this.isLead, this.isData});

  factory IsLeadOrDataModel.fromJson(Map<String, dynamic> json) =>
      _$IsLeadOrDataModelFromJson(json);

  Map<String, dynamic> toJson() => _$IsLeadOrDataModelToJson(this);

}