import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../constants/color_pallete.dart';
import '../constants/text_styles.dart';

class ForceUpdateDialog extends StatefulWidget {
  final String currentVersion;
  final String requiredVersion;

  const ForceUpdateDialog({
    super.key,
    required this.currentVersion,
    required this.requiredVersion,
  });

  @override
  State<ForceUpdateDialog> createState() => _ForceUpdateDialogState();
}

class _ForceUpdateDialogState extends State<ForceUpdateDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false, // Prevent back button from closing the dialog
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Dialog(
                backgroundColor: Colors.transparent,
                child: Container(
                  constraints: const BoxConstraints(maxWidth: 400),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        ColorPalette.primaryColor,        // #292A2B
                        ColorPalette.lightDarkBackground, // #2A2D2F
                        ColorPalette.primaryDarkColor,    // #0D0D0D
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: ColorPalette.primaryGreen.withValues(alpha: 0.3),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: ColorPalette.primaryGreen.withValues(alpha: 0.2),
                        blurRadius: 25,
                        spreadRadius: 2,
                        offset: const Offset(0, 8),
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.4),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Icon with animated glow effect
                        Container(
                          width: 85,
                          height: 85,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: const LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                ColorPalette.primaryGreen,     // #50BEA7
                                ColorPalette.leadratGreen,     // #3C8979
                                ColorPalette.leadratDarkGreen, // #2E6559
                              ],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: ColorPalette.primaryGreen.withValues(alpha: 0.4),
                                blurRadius: 25,
                                spreadRadius: 3,
                              ),
                              BoxShadow(
                                color: ColorPalette.leadratGreen.withValues(alpha: 0.3),
                                blurRadius: 15,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.system_update_alt_rounded,
                            color: ColorPalette.primaryTextColor,
                            size: 42,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Title
                        Text(
                          'Update Required',
                          style: LexendTextStyles.lexend18ExtraBold.copyWith(
                            fontSize: 24,
                            color: ColorPalette.primaryTextColor,
                            letterSpacing: 0.5,
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Subtitle
                        Text(
                          'A new version is available and required to continue using the app.',
                          textAlign: TextAlign.center,
                          style: LexendTextStyles.lexend16Regular.copyWith(
                            color: ColorPalette.secondaryTextColor,
                            height: 1.5,
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Version info card
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(22),
                          decoration: BoxDecoration(
                            color: ColorPalette.lightBackground.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: ColorPalette.primaryGreen.withValues(alpha: 0.2),
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: ColorPalette.primaryGreen.withValues(alpha: 0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Current',
                                        style: LexendTextStyles.lexend12Medium.copyWith(
                                          color: ColorPalette.tertiaryTextColor,
                                        ),
                                      ),
                                      const SizedBox(height: 6),
                                      Text(
                                        widget.currentVersion,
                                        style: LexendTextStyles.lexend16Bold.copyWith(
                                          color: ColorPalette.primaryTextColor,
                                        ),
                                      ),
                                    ],
                                  ),
                                  Icon(
                                    Icons.arrow_forward_rounded,
                                    color: ColorPalette.primaryGreen.withValues(alpha: 0.7),
                                    size: 22,
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Text(
                                        'Required',
                                        style: LexendTextStyles.lexend12Medium.copyWith(
                                          color: ColorPalette.tertiaryTextColor,
                                        ),
                                      ),
                                      const SizedBox(height: 6),
                                      Text(
                                        widget.requiredVersion,
                                        style: LexendTextStyles.lexend16Bold.copyWith(
                                          color: ColorPalette.primaryGreen,
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 32),

                        // Update button
                        Container(
                          width: double.infinity,
                          height: 58,
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                              colors: [
                                ColorPalette.primaryGreen,     // #50BEA7
                                ColorPalette.leadratBgGreen,   // #49AC97
                                ColorPalette.leadratGreen,     // #3C8979
                              ],
                            ),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: ColorPalette.primaryGreen.withValues(alpha: 0.4),
                                blurRadius: 15,
                                spreadRadius: 1,
                                offset: const Offset(0, 6),
                              ),
                              BoxShadow(
                                color: ColorPalette.leadratDarkGreen.withValues(alpha: 0.3),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ElevatedButton(
                            onPressed: _openUpdateUrl,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                const Icon(
                                  Icons.system_update_alt_rounded,
                                  color: ColorPalette.primaryTextColor,
                                  size: 26,
                                ),
                                const SizedBox(width: 14),
                                Text(
                                  'Update Now',
                                  style: LexendTextStyles.lexend18Bold.copyWith(
                                    color: ColorPalette.primaryTextColor,
                                    letterSpacing: 0.5,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _openUpdateUrl() async {
    const updateUrl = 'https://dev.azure.com/gharoffice/Leadrat-Black/_git/leadrat-calldetection-flutter';

    try {
      final uri = Uri.parse(updateUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        print('Could not launch $updateUrl');
      }
    } catch (e) {
      print('Error launching URL: $e');
    }
  }
}
