package com.leadrat.call_detection.call_detection.database;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.leadrat.call_detection.call_detection.enums.CallDirection;
import com.leadrat.call_detection.call_detection.enums.CallStatus;
import com.leadrat.call_detection.call_detection.models.CallLogQueueItem;
import com.leadrat.call_detection.call_detection.models.RecordingQueueItem;

import java.util.ArrayList;
import java.util.List;

/**
 * SQLite database helper for managing offline recording and call log queues
 */
public class OfflineQueueDatabaseHelper extends SQLiteOpenHelper {
    private static final String TAG = "OfflineQueueDB";

    private static final String DATABASE_NAME = "offline_queue.db";
    private static final int DATABASE_VERSION = 1;

    // Recording Queue Table
    private static final String TABLE_RECORDING_QUEUE = "recording_queue";
    private static final String RECORDING_COL_ID = "id";
    private static final String RECORDING_COL_PHONE_NUMBER = "phone_number";
    private static final String RECORDING_COL_RECORDING_FILE_PATH = "recording_file_path";
    private static final String RECORDING_COL_BASE64_DATA = "base64_data";
    private static final String RECORDING_COL_ENTITY_ID = "entity_id";
    private static final String RECORDING_COL_IS_LEAD = "is_lead";
    private static final String RECORDING_COL_CALL_DIRECTION = "call_direction";
    private static final String RECORDING_COL_CALL_STATUS = "call_status";
    private static final String RECORDING_COL_CALL_START_TIME = "call_start_time";
    private static final String RECORDING_COL_CALL_END_TIME = "call_end_time";
    private static final String RECORDING_COL_CALL_DURATION = "call_duration";
    private static final String RECORDING_COL_NOTES = "notes";
    private static final String RECORDING_COL_CREATED_AT = "created_at";
    private static final String RECORDING_COL_RETRY_COUNT = "retry_count";
    private static final String RECORDING_COL_LAST_RETRY_AT = "last_retry_at";
    private static final String RECORDING_COL_ERROR_MESSAGE = "error_message";
    private static final String RECORDING_COL_UPLOAD_STATUS = "upload_status";
    private static final String RECORDING_COL_IS_PROCESSING = "is_processing";

    // Call Log Queue Table
    private static final String TABLE_CALL_LOG_QUEUE = "call_log_queue";
    private static final String CALL_LOG_COL_ID = "id";
    private static final String CALL_LOG_COL_PHONE_NUMBER = "phone_number";
    private static final String CALL_LOG_COL_ENTITY_ID = "entity_id";
    private static final String CALL_LOG_COL_IS_LEAD = "is_lead";
    private static final String CALL_LOG_COL_CALL_DIRECTION = "call_direction";
    private static final String CALL_LOG_COL_CALL_STATUS = "call_status";
    private static final String CALL_LOG_COL_CALL_START_TIME = "call_start_time";
    private static final String CALL_LOG_COL_CALL_END_TIME = "call_end_time";
    private static final String CALL_LOG_COL_CALL_DURATION = "call_duration";
    private static final String CALL_LOG_COL_NOTES = "notes";
    private static final String CALL_LOG_COL_CALL_RECORDING_URL = "call_recording_url";
    private static final String CALL_LOG_COL_RECORDING_QUEUE_ITEM_ID = "recording_queue_item_id";
    private static final String CALL_LOG_COL_CREATED_AT = "created_at";
    private static final String CALL_LOG_COL_RETRY_COUNT = "retry_count";
    private static final String CALL_LOG_COL_LAST_RETRY_AT = "last_retry_at";
    private static final String CALL_LOG_COL_ERROR_MESSAGE = "error_message";
    private static final String CALL_LOG_COL_SYNC_STATUS = "sync_status";
    private static final String CALL_LOG_COL_IS_PROCESSING = "is_processing";

    private static OfflineQueueDatabaseHelper instance;

    public static synchronized OfflineQueueDatabaseHelper getInstance(Context context) {
        if (instance == null) {
            instance = new OfflineQueueDatabaseHelper(context.getApplicationContext());
        }
        return instance;
    }

    private OfflineQueueDatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        Log.d(TAG, "Creating offline queue database tables");

        // Create Recording Queue Table
        String createRecordingQueueTable = "CREATE TABLE " + TABLE_RECORDING_QUEUE + " (" +
                RECORDING_COL_ID + " TEXT PRIMARY KEY, " +
                RECORDING_COL_PHONE_NUMBER + " TEXT NOT NULL, " +
                RECORDING_COL_RECORDING_FILE_PATH + " TEXT, " +
                RECORDING_COL_BASE64_DATA + " TEXT, " +
                RECORDING_COL_ENTITY_ID + " TEXT, " +
                RECORDING_COL_IS_LEAD + " INTEGER NOT NULL, " +
                RECORDING_COL_CALL_DIRECTION + " INTEGER NOT NULL, " +
                RECORDING_COL_CALL_STATUS + " INTEGER NOT NULL, " +
                RECORDING_COL_CALL_START_TIME + " INTEGER NOT NULL, " +
                RECORDING_COL_CALL_END_TIME + " INTEGER NOT NULL, " +
                RECORDING_COL_CALL_DURATION + " REAL NOT NULL, " +
                RECORDING_COL_NOTES + " TEXT, " +
                RECORDING_COL_CREATED_AT + " INTEGER NOT NULL, " +
                RECORDING_COL_RETRY_COUNT + " INTEGER DEFAULT 0, " +
                RECORDING_COL_LAST_RETRY_AT + " INTEGER DEFAULT 0, " +
                RECORDING_COL_ERROR_MESSAGE + " TEXT, " +
                RECORDING_COL_UPLOAD_STATUS + " TEXT NOT NULL, " +
                RECORDING_COL_IS_PROCESSING + " INTEGER DEFAULT 0" +
                ")";

        // Create Call Log Queue Table
        String createCallLogQueueTable = "CREATE TABLE " + TABLE_CALL_LOG_QUEUE + " (" +
                CALL_LOG_COL_ID + " TEXT PRIMARY KEY, " +
                CALL_LOG_COL_PHONE_NUMBER + " TEXT NOT NULL, " +
                CALL_LOG_COL_ENTITY_ID + " TEXT, " +
                CALL_LOG_COL_IS_LEAD + " INTEGER NOT NULL, " +
                CALL_LOG_COL_CALL_DIRECTION + " INTEGER NOT NULL, " +
                CALL_LOG_COL_CALL_STATUS + " INTEGER NOT NULL, " +
                CALL_LOG_COL_CALL_START_TIME + " INTEGER NOT NULL, " +
                CALL_LOG_COL_CALL_END_TIME + " INTEGER NOT NULL, " +
                CALL_LOG_COL_CALL_DURATION + " REAL NOT NULL, " +
                CALL_LOG_COL_NOTES + " TEXT, " +
                CALL_LOG_COL_CALL_RECORDING_URL + " TEXT, " +
                CALL_LOG_COL_RECORDING_QUEUE_ITEM_ID + " TEXT, " +
                CALL_LOG_COL_CREATED_AT + " INTEGER NOT NULL, " +
                CALL_LOG_COL_RETRY_COUNT + " INTEGER DEFAULT 0, " +
                CALL_LOG_COL_LAST_RETRY_AT + " INTEGER DEFAULT 0, " +
                CALL_LOG_COL_ERROR_MESSAGE + " TEXT, " +
                CALL_LOG_COL_SYNC_STATUS + " TEXT NOT NULL, " +
                CALL_LOG_COL_IS_PROCESSING + " INTEGER DEFAULT 0" +
                ")";

        db.execSQL(createRecordingQueueTable);
        db.execSQL(createCallLogQueueTable);

        Log.d(TAG, "Offline queue database tables created successfully");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        Log.d(TAG, "Upgrading offline queue database from version " + oldVersion + " to " + newVersion);

        // For now, just drop and recreate tables
        // In production, you might want to migrate data
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_RECORDING_QUEUE);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_CALL_LOG_QUEUE);
        onCreate(db);
    }

    /**
     * Insert a recording queue item
     */
    public long insertRecordingQueueItem(RecordingQueueItem item) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();

        values.put(RECORDING_COL_ID, item.id);
        values.put(RECORDING_COL_PHONE_NUMBER, item.phoneNumber);
        values.put(RECORDING_COL_RECORDING_FILE_PATH, item.recordingFilePath);
        values.put(RECORDING_COL_BASE64_DATA, item.base64Data);
        values.put(RECORDING_COL_ENTITY_ID, item.entityId);
        values.put(RECORDING_COL_IS_LEAD, item.isLead ? 1 : 0);
        values.put(RECORDING_COL_CALL_DIRECTION, item.callDirection.ordinal());
        values.put(RECORDING_COL_CALL_STATUS, item.callStatus.ordinal());
        values.put(RECORDING_COL_CALL_START_TIME, item.callStartTime);
        values.put(RECORDING_COL_CALL_END_TIME, item.callEndTime);
        values.put(RECORDING_COL_CALL_DURATION, item.callDuration);
        values.put(RECORDING_COL_NOTES, item.notes);
        values.put(RECORDING_COL_CREATED_AT, item.createdAt);
        values.put(RECORDING_COL_RETRY_COUNT, item.retryCount);
        values.put(RECORDING_COL_LAST_RETRY_AT, item.lastRetryAt);
        values.put(RECORDING_COL_ERROR_MESSAGE, item.errorMessage);
        values.put(RECORDING_COL_UPLOAD_STATUS, item.uploadStatus.name());
        values.put(RECORDING_COL_IS_PROCESSING, item.isProcessing ? 1 : 0);

        long result = db.insert(TABLE_RECORDING_QUEUE, null, values);
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Inserted recording queue item: " + item.id + " (result: " + result + ")");

        return result;
    }

    /**
     * Insert a call log queue item
     */
    public long insertCallLogQueueItem(CallLogQueueItem item) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();

        values.put(CALL_LOG_COL_ID, item.id);
        values.put(CALL_LOG_COL_PHONE_NUMBER, item.phoneNumber);
        values.put(CALL_LOG_COL_ENTITY_ID, item.entityId);
        values.put(CALL_LOG_COL_IS_LEAD, item.isLead ? 1 : 0);
        values.put(CALL_LOG_COL_CALL_DIRECTION, item.callDirection.ordinal());
        values.put(CALL_LOG_COL_CALL_STATUS, item.callStatus.ordinal());
        values.put(CALL_LOG_COL_CALL_START_TIME, item.callStartTime);
        values.put(CALL_LOG_COL_CALL_END_TIME, item.callEndTime);
        values.put(CALL_LOG_COL_CALL_DURATION, item.callDuration);
        values.put(CALL_LOG_COL_NOTES, item.notes);
        values.put(CALL_LOG_COL_CALL_RECORDING_URL, item.callRecordingUrl);
        values.put(CALL_LOG_COL_RECORDING_QUEUE_ITEM_ID, item.recordingQueueItemId);
        values.put(CALL_LOG_COL_CREATED_AT, item.createdAt);
        values.put(CALL_LOG_COL_RETRY_COUNT, item.retryCount);
        values.put(CALL_LOG_COL_LAST_RETRY_AT, item.lastRetryAt);
        values.put(CALL_LOG_COL_ERROR_MESSAGE, item.errorMessage);
        values.put(CALL_LOG_COL_SYNC_STATUS, item.syncStatus.name());
        values.put(CALL_LOG_COL_IS_PROCESSING, item.isProcessing ? 1 : 0);

        long result = db.insert(TABLE_CALL_LOG_QUEUE, null, values);
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Inserted call log queue item: " + item.id + " (result: " + result + ")");

        return result;
    }

    /**
     * Get all pending recording queue items that need to be uploaded
     */
    public List<RecordingQueueItem> getPendingRecordingQueueItems() {
        List<RecordingQueueItem> items = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();

        String selection = RECORDING_COL_UPLOAD_STATUS + " IN (?, ?) AND " + RECORDING_COL_IS_PROCESSING + " = 0";
        String[] selectionArgs = {
            RecordingQueueItem.UploadStatus.PENDING.name(),
            RecordingQueueItem.UploadStatus.FAILED.name()
        };
        String orderBy = RECORDING_COL_CREATED_AT + " ASC";

        Cursor cursor = db.query(TABLE_RECORDING_QUEUE, null, selection, selectionArgs, null, null, orderBy);

        if (cursor != null) {
            while (cursor.moveToNext()) {
                RecordingQueueItem item = createRecordingQueueItemFromCursor(cursor);
                if (item.shouldRetry()) {
                    items.add(item);
                }
            }
            cursor.close();
        }

        Log.d(TAG, "📁 [OFFLINE_QUEUE] Found " + items.size() + " pending recording queue items");
        return items;
    }

    /**
     * Get all pending call log queue items that need to be synced
     */
    public List<CallLogQueueItem> getPendingCallLogQueueItems() {
        List<CallLogQueueItem> items = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();

        String selection = CALL_LOG_COL_SYNC_STATUS + " IN (?, ?) AND " + CALL_LOG_COL_IS_PROCESSING + " = 0";
        String[] selectionArgs = {
            CallLogQueueItem.SyncStatus.PENDING.name(),
            CallLogQueueItem.SyncStatus.FAILED.name()
        };
        String orderBy = CALL_LOG_COL_CREATED_AT + " ASC";

        Cursor cursor = db.query(TABLE_CALL_LOG_QUEUE, null, selection, selectionArgs, null, null, orderBy);

        if (cursor != null) {
            while (cursor.moveToNext()) {
                CallLogQueueItem item = createCallLogQueueItemFromCursor(cursor);
                if (item.shouldRetry()) {
                    Log.d(TAG, "Calllog isLead or Data: " + item.isLead);
                    items.add(item);
                }
            }
            cursor.close();
        }

        Log.d(TAG, "📞 [OFFLINE_QUEUE] Found " + items.size() + " pending call log queue items");
        return items;
    }

    /**
     * Update recording queue item
     */
    public int updateRecordingQueueItem(RecordingQueueItem item) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();

        values.put(RECORDING_COL_BASE64_DATA, item.base64Data);
        values.put(RECORDING_COL_RETRY_COUNT, item.retryCount);
        values.put(RECORDING_COL_LAST_RETRY_AT, item.lastRetryAt);
        values.put(RECORDING_COL_ERROR_MESSAGE, item.errorMessage);
        values.put(RECORDING_COL_UPLOAD_STATUS, item.uploadStatus.name());
        values.put(RECORDING_COL_IS_PROCESSING, item.isProcessing ? 1 : 0);

        String whereClause = RECORDING_COL_ID + " = ?";
        String[] whereArgs = {item.id};

        int result = db.update(TABLE_RECORDING_QUEUE, values, whereClause, whereArgs);
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Updated recording queue item: " + item.id + " (status: " + item.uploadStatus + ")");

        return result;
    }

    /**
     * Update call log queue item
     */
    public int updateCallLogQueueItem(CallLogQueueItem item) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();

        values.put(CALL_LOG_COL_CALL_RECORDING_URL, item.callRecordingUrl);
        values.put(CALL_LOG_COL_RETRY_COUNT, item.retryCount);
        values.put(CALL_LOG_COL_LAST_RETRY_AT, item.lastRetryAt);
        values.put(CALL_LOG_COL_ERROR_MESSAGE, item.errorMessage);
        values.put(CALL_LOG_COL_SYNC_STATUS, item.syncStatus.name());
        values.put(CALL_LOG_COL_IS_PROCESSING, item.isProcessing ? 1 : 0);

        String whereClause = CALL_LOG_COL_ID + " = ?";
        String[] whereArgs = {item.id};

        int result = db.update(TABLE_CALL_LOG_QUEUE, values, whereClause, whereArgs);
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Updated call log queue item: " + item.id + " (status: " + item.syncStatus + ")");

        return result;
    }

    /**
     * Delete completed recording queue items
     */
    public int deleteCompletedRecordingQueueItems() {
        SQLiteDatabase db = this.getWritableDatabase();

        String whereClause = RECORDING_COL_UPLOAD_STATUS + " = ?";
        String[] whereArgs = {RecordingQueueItem.UploadStatus.COMPLETED.name()};

        int result = db.delete(TABLE_RECORDING_QUEUE, whereClause, whereArgs);
        Log.d(TAG, "📁 [OFFLINE_QUEUE] Deleted " + result + " completed recording queue items");

        return result;
    }

    /**
     * Delete completed call log queue items
     */
    public int deleteCompletedCallLogQueueItems() {
        SQLiteDatabase db = this.getWritableDatabase();

        String whereClause = CALL_LOG_COL_SYNC_STATUS + " = ?";
        String[] whereArgs = {CallLogQueueItem.SyncStatus.COMPLETED.name()};

        int result = db.delete(TABLE_CALL_LOG_QUEUE, whereClause, whereArgs);
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Deleted " + result + " completed call log queue items");

        return result;
    }

    /**
     * Update call log queue items when recording upload is completed
     */
    public int updateCallLogQueueItemsForCompletedRecording(String recordingQueueItemId, String recordingUrl) {
        SQLiteDatabase db = this.getWritableDatabase();
        ContentValues values = new ContentValues();

        values.put(CALL_LOG_COL_CALL_RECORDING_URL, recordingUrl);
        values.put(CALL_LOG_COL_SYNC_STATUS, CallLogQueueItem.SyncStatus.PENDING.name());

        String whereClause = CALL_LOG_COL_RECORDING_QUEUE_ITEM_ID + " = ? AND " +
                           CALL_LOG_COL_SYNC_STATUS + " = ?";
        String[] whereArgs = {recordingQueueItemId, CallLogQueueItem.SyncStatus.WAITING_FOR_RECORDING.name()};

        int result = db.update(TABLE_CALL_LOG_QUEUE, values, whereClause, whereArgs);
        Log.d(TAG, "📞 [OFFLINE_QUEUE] Updated " + result + " call log items for completed recording: " + recordingQueueItemId);

        return result;
    }

    /**
     * Create RecordingQueueItem from cursor
     */
    private RecordingQueueItem createRecordingQueueItemFromCursor(Cursor cursor) {
        RecordingQueueItem item = new RecordingQueueItem();

        item.id = cursor.getString(cursor.getColumnIndex(RECORDING_COL_ID));
        item.phoneNumber = cursor.getString(cursor.getColumnIndex(RECORDING_COL_PHONE_NUMBER));
        item.recordingFilePath = cursor.getString(cursor.getColumnIndex(RECORDING_COL_RECORDING_FILE_PATH));
        item.base64Data = cursor.getString(cursor.getColumnIndex(RECORDING_COL_BASE64_DATA));
        item.entityId = cursor.getString(cursor.getColumnIndex(RECORDING_COL_ENTITY_ID));
        item.isLead = cursor.getInt(cursor.getColumnIndex(RECORDING_COL_IS_LEAD)) == 1;
        item.callDirection = CallDirection.values()[cursor.getInt(cursor.getColumnIndex(RECORDING_COL_CALL_DIRECTION))];
        item.callStatus = CallStatus.values()[cursor.getInt(cursor.getColumnIndex(RECORDING_COL_CALL_STATUS))];
        item.callStartTime = cursor.getLong(cursor.getColumnIndex(RECORDING_COL_CALL_START_TIME));
        item.callEndTime = cursor.getLong(cursor.getColumnIndex(RECORDING_COL_CALL_END_TIME));
        item.callDuration = cursor.getDouble(cursor.getColumnIndex(RECORDING_COL_CALL_DURATION));
        item.notes = cursor.getString(cursor.getColumnIndex(RECORDING_COL_NOTES));
        item.createdAt = cursor.getLong(cursor.getColumnIndex(RECORDING_COL_CREATED_AT));
        item.retryCount = cursor.getInt(cursor.getColumnIndex(RECORDING_COL_RETRY_COUNT));
        item.lastRetryAt = cursor.getLong(cursor.getColumnIndex(RECORDING_COL_LAST_RETRY_AT));
        item.errorMessage = cursor.getString(cursor.getColumnIndex(RECORDING_COL_ERROR_MESSAGE));
        item.uploadStatus = RecordingQueueItem.UploadStatus.valueOf(cursor.getString(cursor.getColumnIndex(RECORDING_COL_UPLOAD_STATUS)));
        item.isProcessing = cursor.getInt(cursor.getColumnIndex(RECORDING_COL_IS_PROCESSING)) == 1;

        return item;
    }

    /**
     * Create CallLogQueueItem from cursor
     */
    private CallLogQueueItem createCallLogQueueItemFromCursor(Cursor cursor) {
        CallLogQueueItem item = new CallLogQueueItem();

        item.id = cursor.getString(cursor.getColumnIndex(CALL_LOG_COL_ID));
        item.phoneNumber = cursor.getString(cursor.getColumnIndex(CALL_LOG_COL_PHONE_NUMBER));
        item.entityId = cursor.getString(cursor.getColumnIndex(CALL_LOG_COL_ENTITY_ID));
        item.isLead = cursor.getInt(cursor.getColumnIndex(CALL_LOG_COL_IS_LEAD)) == 1;
        item.callDirection = CallDirection.values()[cursor.getInt(cursor.getColumnIndex(CALL_LOG_COL_CALL_DIRECTION))];
        item.callStatus = CallStatus.values()[cursor.getInt(cursor.getColumnIndex(CALL_LOG_COL_CALL_STATUS))];
        item.callStartTime = cursor.getLong(cursor.getColumnIndex(CALL_LOG_COL_CALL_START_TIME));
        item.callEndTime = cursor.getLong(cursor.getColumnIndex(CALL_LOG_COL_CALL_END_TIME));
        item.callDuration = cursor.getDouble(cursor.getColumnIndex(CALL_LOG_COL_CALL_DURATION));
        item.notes = cursor.getString(cursor.getColumnIndex(CALL_LOG_COL_NOTES));
        item.callRecordingUrl = cursor.getString(cursor.getColumnIndex(CALL_LOG_COL_CALL_RECORDING_URL));
        item.recordingQueueItemId = cursor.getString(cursor.getColumnIndex(CALL_LOG_COL_RECORDING_QUEUE_ITEM_ID));
        item.createdAt = cursor.getLong(cursor.getColumnIndex(CALL_LOG_COL_CREATED_AT));
        item.retryCount = cursor.getInt(cursor.getColumnIndex(CALL_LOG_COL_RETRY_COUNT));
        item.lastRetryAt = cursor.getLong(cursor.getColumnIndex(CALL_LOG_COL_LAST_RETRY_AT));
        item.errorMessage = cursor.getString(cursor.getColumnIndex(CALL_LOG_COL_ERROR_MESSAGE));
        item.syncStatus = CallLogQueueItem.SyncStatus.valueOf(cursor.getString(cursor.getColumnIndex(CALL_LOG_COL_SYNC_STATUS)));
        item.isProcessing = cursor.getInt(cursor.getColumnIndex(CALL_LOG_COL_IS_PROCESSING)) == 1;

        return item;
    }

    /**
     * Get queue statistics for debugging
     */
    public String getQueueStatistics() {
        SQLiteDatabase db = this.getReadableDatabase();
        StringBuilder stats = new StringBuilder();

        // Recording queue stats
        Cursor recordingCursor = db.rawQuery(
            "SELECT " + RECORDING_COL_UPLOAD_STATUS + ", COUNT(*) as count FROM " + TABLE_RECORDING_QUEUE +
            " GROUP BY " + RECORDING_COL_UPLOAD_STATUS, null);

        stats.append("📁 Recording Queue:\n");
        if (recordingCursor != null) {
            while (recordingCursor.moveToNext()) {
                String status = recordingCursor.getString(0);
                int count = recordingCursor.getInt(1);
                stats.append("  ").append(status).append(": ").append(count).append("\n");
            }
            recordingCursor.close();
        }

        // Call log queue stats
        Cursor callLogCursor = db.rawQuery(
            "SELECT " + CALL_LOG_COL_SYNC_STATUS + ", COUNT(*) as count FROM " + TABLE_CALL_LOG_QUEUE +
            " GROUP BY " + CALL_LOG_COL_SYNC_STATUS, null);

        stats.append("📞 Call Log Queue:\n");
        if (callLogCursor != null) {
            while (callLogCursor.moveToNext()) {
                String status = callLogCursor.getString(0);
                int count = callLogCursor.getInt(1);
                stats.append("  ").append(status).append(": ").append(count).append("\n");
            }
            callLogCursor.close();
        }

        return stats.toString();
    }

    /**
     * Get all recording queue items for debugging
     */
    public List<RecordingQueueItem> getAllRecordingQueueItems() {
        List<RecordingQueueItem> items = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();

        Cursor cursor = db.query(TABLE_RECORDING_QUEUE, null, null, null, null, null,
                                RECORDING_COL_CREATED_AT + " DESC");

        if (cursor != null) {
            while (cursor.moveToNext()) {
                items.add(mapCursorToRecordingQueueItem(cursor));
            }
            cursor.close();
        }

        return items;
    }

    /**
     * Get all call log queue items for debugging
     */
    public List<CallLogQueueItem> getAllCallLogQueueItems() {
        List<CallLogQueueItem> items = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();

        Cursor cursor = db.query(TABLE_CALL_LOG_QUEUE, null, null, null, null, null,
                                CALL_LOG_COL_CREATED_AT + " DESC");

        if (cursor != null) {
            while (cursor.moveToNext()) {
                items.add(mapCursorToCallLogQueueItem(cursor));
            }
            cursor.close();
        }

        return items;
    }

    /**
     * Map cursor to RecordingQueueItem
     */
    private RecordingQueueItem mapCursorToRecordingQueueItem(Cursor cursor) {
        RecordingQueueItem item = new RecordingQueueItem();

        item.id = cursor.getString(cursor.getColumnIndexOrThrow(RECORDING_COL_ID));
        item.phoneNumber = cursor.getString(cursor.getColumnIndexOrThrow(RECORDING_COL_PHONE_NUMBER));
        item.recordingFilePath = cursor.getString(cursor.getColumnIndexOrThrow(RECORDING_COL_RECORDING_FILE_PATH));
        item.base64Data = cursor.getString(cursor.getColumnIndexOrThrow(RECORDING_COL_BASE64_DATA));
        item.entityId = cursor.getString(cursor.getColumnIndexOrThrow(RECORDING_COL_ENTITY_ID));
        item.isLead = cursor.getInt(cursor.getColumnIndexOrThrow(RECORDING_COL_IS_LEAD)) == 1;

        // Map call direction
        int directionOrdinal = cursor.getInt(cursor.getColumnIndexOrThrow(RECORDING_COL_CALL_DIRECTION));
        item.callDirection = CallDirection.values()[directionOrdinal];

        // Map call status
        int statusOrdinal = cursor.getInt(cursor.getColumnIndexOrThrow(RECORDING_COL_CALL_STATUS));
        item.callStatus = CallStatus.values()[statusOrdinal];

        item.callStartTime = cursor.getLong(cursor.getColumnIndexOrThrow(RECORDING_COL_CALL_START_TIME));
        item.callEndTime = cursor.getLong(cursor.getColumnIndexOrThrow(RECORDING_COL_CALL_END_TIME));
        item.callDuration = cursor.getDouble(cursor.getColumnIndexOrThrow(RECORDING_COL_CALL_DURATION));
        item.notes = cursor.getString(cursor.getColumnIndexOrThrow(RECORDING_COL_NOTES));
        item.createdAt = cursor.getLong(cursor.getColumnIndexOrThrow(RECORDING_COL_CREATED_AT));
        item.retryCount = cursor.getInt(cursor.getColumnIndexOrThrow(RECORDING_COL_RETRY_COUNT));
        item.lastRetryAt = cursor.getLong(cursor.getColumnIndexOrThrow(RECORDING_COL_LAST_RETRY_AT));
        item.errorMessage = cursor.getString(cursor.getColumnIndexOrThrow(RECORDING_COL_ERROR_MESSAGE));
        item.isProcessing = cursor.getInt(cursor.getColumnIndexOrThrow(RECORDING_COL_IS_PROCESSING)) == 1;

        // Map upload status
        String uploadStatusStr = cursor.getString(cursor.getColumnIndexOrThrow(RECORDING_COL_UPLOAD_STATUS));
        item.uploadStatus = RecordingQueueItem.UploadStatus.valueOf(uploadStatusStr);

        return item;
    }

    /**
     * Map cursor to CallLogQueueItem
     */
    private CallLogQueueItem mapCursorToCallLogQueueItem(Cursor cursor) {
        CallLogQueueItem item = new CallLogQueueItem();

        item.id = cursor.getString(cursor.getColumnIndexOrThrow(CALL_LOG_COL_ID));
        item.phoneNumber = cursor.getString(cursor.getColumnIndexOrThrow(CALL_LOG_COL_PHONE_NUMBER));
        item.entityId = cursor.getString(cursor.getColumnIndexOrThrow(CALL_LOG_COL_ENTITY_ID));
        item.isLead = cursor.getInt(cursor.getColumnIndexOrThrow(CALL_LOG_COL_IS_LEAD)) == 1;

        // Map call direction
        int directionOrdinal = cursor.getInt(cursor.getColumnIndexOrThrow(CALL_LOG_COL_CALL_DIRECTION));
        item.callDirection = CallDirection.values()[directionOrdinal];

        // Map call status
        int statusOrdinal = cursor.getInt(cursor.getColumnIndexOrThrow(CALL_LOG_COL_CALL_STATUS));
        item.callStatus = CallStatus.values()[statusOrdinal];

        item.callStartTime = cursor.getLong(cursor.getColumnIndexOrThrow(CALL_LOG_COL_CALL_START_TIME));
        item.callEndTime = cursor.getLong(cursor.getColumnIndexOrThrow(CALL_LOG_COL_CALL_END_TIME));
        item.callDuration = cursor.getDouble(cursor.getColumnIndexOrThrow(CALL_LOG_COL_CALL_DURATION));
        item.notes = cursor.getString(cursor.getColumnIndexOrThrow(CALL_LOG_COL_NOTES));
        item.recordingQueueItemId = cursor.getString(cursor.getColumnIndexOrThrow(CALL_LOG_COL_RECORDING_QUEUE_ITEM_ID));
        item.callRecordingUrl = cursor.getString(cursor.getColumnIndexOrThrow(CALL_LOG_COL_CALL_RECORDING_URL));
        item.createdAt = cursor.getLong(cursor.getColumnIndexOrThrow(CALL_LOG_COL_CREATED_AT));
        item.retryCount = cursor.getInt(cursor.getColumnIndexOrThrow(CALL_LOG_COL_RETRY_COUNT));
        item.lastRetryAt = cursor.getLong(cursor.getColumnIndexOrThrow(CALL_LOG_COL_LAST_RETRY_AT));
        item.errorMessage = cursor.getString(cursor.getColumnIndexOrThrow(CALL_LOG_COL_ERROR_MESSAGE));
        item.isProcessing = cursor.getInt(cursor.getColumnIndexOrThrow(CALL_LOG_COL_IS_PROCESSING)) == 1;

        // Map sync status
        String syncStatusStr = cursor.getString(cursor.getColumnIndexOrThrow(CALL_LOG_COL_SYNC_STATUS));
        item.syncStatus = CallLogQueueItem.SyncStatus.valueOf(syncStatusStr);

        return item;
    }
}
