import 'dart:async';
import 'dart:io';
import 'package:call_detection/core/enums/page_state_enum.dart';
import 'package:call_detection/core/utils/preference_helper.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'package:call_detection/features/call_recordings/data/services/global_settings_cache_service.dart';

import '../../../../permissions/utils/permission_utils.dart';

part 'call_recordoings_event.dart';
part 'call_recordoings_state.dart';

class CallRecordingsBloc
    extends Bloc<CallRecordingsEvent, CallRecordingsState> {
  final TextEditingController controller = TextEditingController();
  CallRecordingsBloc() : super(CallRecordingsState()) {
    on<CallRecordingsInitialEvent>(_onCallRecordingsInitialEvent);

    on<GetCallRecordingsEvent>(_onGetCallRecordingsEvent);
    on<StartCallDetectionEvent>(_onStartCallDetection);
    on<CheckTheCallDetectionIsActiveEvent>(_onCheckTheCallDetectionIsActive);
    on<InBuiltCallRecorderSelectedEvent>(_onInBuiltCallRecorderSelected);
    on<OpenTheFileExplorerEvent>(_onOpenTheFileExplorerEvent);
    on<OnCancelPressedEvent>(_onCancelPressed);
    on<IsGoogleDialerEvent>(_isGoogleDialerCheck);
    on<IsCallRecordingEnabledForTenantEvent>(_isCallRecordingEnabledForTenant);
  }

  FutureOr<void> _onCallRecordingsInitialEvent(CallRecordingsInitialEvent event,
      Emitter<CallRecordingsState> emit) async {
    String? directoryPath = await PreferencesHelper.getString("directoryPath");
    if (directoryPath != null) {
      add(GetCallRecordingsEvent(recordingsPath: directoryPath));
      emit(state.copyWith(directoryPath: directoryPath));
    }

    add(CheckTheCallDetectionIsActiveEvent());
    add(IsGoogleDialerEvent());
  }

  FutureOr<void> _isCallRecordingEnabledForTenant(
      IsCallRecordingEnabledForTenantEvent event,
      Emitter<CallRecordingsState> emit) async {
    const platform = MethodChannel('call_detection_service');
    String tenantId = "";
    try {
      tenantId = await platform.invokeMethod('getTenantId');
    } on PlatformException catch (e, stackTrace) {
      print("Failed to get tenant ID: '${e.message}'.");
      emit(state.copyWith(
          pageState: PageState.failure,
          isServiceRunning: false,
          message: "failed to get the global settings of the tenant"));
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
      );
      return;
    }

    emit(state.copyWith(
        pageState: PageState.loading, message: "loading the call recording!"));

    if (tenantId.isEmpty) {
      emit(state.copyWith(
          pageState: PageState.initial,
          isCallRecordingEnabled: false,
          isFinalSetupDone: false,
          message:
              "Kindly close and reopen the leadrat application to sync the call recording feature! After it is done comeback to this application to continue the setup."));
    } else {
      try {
        // Use the caching service to get settings
        final settings =
            await GlobalSettingsCacheService.getCallRecordingSettings(
          tenantId: tenantId,
          forceRefresh: false, // Use cache if available
        );

        print('Global settings retrieved: $settings');

        // Emit a new state or handle the value as needed
        if (settings.isCallRecordingEnabled) {
          emit(state.copyWith(
              pageState: PageState.initial,
              isCallRecordingEnabled: settings.isCallRecordingEnabled));
        } else {
          emit(state.copyWith(
              pageState: PageState.initial,
              isCallRecordingEnabled: settings.isCallRecordingEnabled,
              isFinalSetupDone: true,
              message:
                  "Call recording feature is not available for your tenant! Please contact admin."));
        }
      } catch (e) {
        print('Error getting call recording settings: $e');
        emit(state.copyWith(
            pageState: PageState.initial,
            isCallRecordingEnabled: false,
            isFinalSetupDone: true,
            message:
                "There was some problem in loading the call recording feature"));
      }
    }
  }

  FutureOr<void> _isGoogleDialerCheck(
      IsGoogleDialerEvent event, Emitter<CallRecordingsState> emit) async {
    const platform = MethodChannel('call_detection_service');
    try {
      bool isGoogleDialer =
          await platform.invokeMethod('isGoogleDialerDefault');
      // If Google Dialer is detected, enable the finish button since call recording is not available
      emit(state.copyWith(
        isGoogleDialer: isGoogleDialer,
        isFinalSetupDone:
            isGoogleDialer, // Enable finish button for Google Dialer users
      ));
    } on PlatformException catch (e, stackTrace) {
      print("Failed to check google dialer: '${e.message}'.");
      emit(state.copyWith(
          pageState: PageState.failure,
          isServiceRunning: false,
          message:
              "There was an error starting the service! Please try again"));
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
      );
    }
  }

  FutureOr<void> _onGetCallRecordingsEvent(
      GetCallRecordingsEvent event, Emitter<CallRecordingsState> emit) async {
    if (event.recordingsPath == null ||
        (event.recordingsPath?.trim().isEmpty ?? true)) {
      emit(state.copyWith(
          pageState: PageState.failure,
          message: "Enter valid call recordings path"));
      return;
    }

    // For built-in call recorder, we only need storage permission to read files
    PermissionRequestResult permissionResult =
        await PermissionUtils.requestStoragePermission();

    if (!permissionResult.allGranted) {
      String errorMessage =
          PermissionUtils.generateErrorMessage(permissionResult);
      emit(state.copyWith(pageState: PageState.failure, message: errorMessage));
      return;
    }
    Directory dir = Directory(event.recordingsPath ?? '');
    if (dir.existsSync()) {
      List<FileSystemEntity> files = dir.listSync();
      final recordingsList = files
          .whereType<File>()
          .toList()
          .where((file) => _checkIsFileAudioType(file))
          .map((filteredFile) => filteredFile.path)
          .toList();
      emit(state.copyWith(recordingsList: recordingsList));
    } else {
      emit(state.copyWith(
          pageState: PageState.failure, message: "Directory does not exist"));
    }
  }

  bool _checkIsFileAudioType(FileSystemEntity file) {
    String path = file.path.toLowerCase();
    return path.endsWith('.mp3') ||
        path.endsWith('.wav') ||
        path.endsWith('.m4a');
  }

  FutureOr<void> _onStartCallDetection(
      StartCallDetectionEvent event, Emitter<CallRecordingsState> emit) async {
    const platform = MethodChannel('call_detection_service');
    try {
      // Request service-related permissions first (with optional display permission)
      PermissionRequestResult permissionResult =
          await PermissionUtils.requestServicePermissionsWithOptionalDisplay();

      // Check if required permissions are granted (optional permissions can be denied)
      if (!permissionResult.allGranted) {
        String errorMessage =
            PermissionUtils.generateErrorMessageWithOptional(permissionResult);
        emit(state.copyWith(
            pageState: PageState.failure,
            isServiceRunning: false,
            message: errorMessage));
        return;
      }

      // Store information about optional permissions for later use
      String optionalPermissionMessage = "";
      if (permissionResult.optionalDeniedPermissions.isNotEmpty) {
        optionalPermissionMessage =
            PermissionUtils.generateErrorMessageWithOptional(
                PermissionRequestResult(
          allGranted: true, // Required permissions are granted
          optionalDeniedPermissions: permissionResult.optionalDeniedPermissions,
        ));
      }

      // Check if phone and call log permissions are granted (these should be granted from the permission result above)
      bool hasPermissions = false;
      try {
        hasPermissions =
            await platform.invokeMethod('checkPhoneAndCallLogPermissions');
      } catch (e) {
        // If method not found, assume we need to check permissions
        hasPermissions = false;
      }

      if (!hasPermissions) {
        // Request permissions first
        try {
          await platform.invokeMethod('requestPhoneAndCallLogPermissions');
          // Check again after requesting
          hasPermissions =
              await platform.invokeMethod('checkPhoneAndCallLogPermissions');
        } catch (e) {
          // Fallback to permission_handler
          await Permission.phone.request();
        }

        if (!hasPermissions) {
          emit(state.copyWith(
              pageState: PageState.failure,
              isServiceRunning: false,
              message:
                  "Call logs and phone permissions are required. Please grant these permissions and try again."));
          return;
        }
      }

      // Now try to start the service
      await platform.invokeMethod('startService');

      // Emit success state with optional permission message if applicable
      if (optionalPermissionMessage.isNotEmpty) {
        emit(state.copyWith(
          isServiceRunning: true,
          pageState: PageState.success,
          message: optionalPermissionMessage,
        ));
      } else {
        emit(state.copyWith(isServiceRunning: true));
      }
    } on PlatformException catch (e, stackTrace) {
      if (e.code == 'PERMISSION_DENIED') {
        // Special handling for permission denied error
        emit(state.copyWith(
            pageState: PageState.failure,
            isServiceRunning: false,
            message:
                "Call logs and phone permissions are required. Please grant these permissions and try again."));
      } else {
        // Handle other errors
        if (kReleaseMode) {
          await Sentry.captureException(e, stackTrace: stackTrace);
        } else {
          print("Failed to start service: '${e.message}'.");
        }
        emit(state.copyWith(
            pageState: PageState.failure,
            isServiceRunning: false,
            message:
                "There was an error starting the service! Please try again"));
      }
    } finally {
      add(IsCallRecordingEnabledForTenantEvent());
    }
  }

  FutureOr<void> _onCheckTheCallDetectionIsActive(
      CheckTheCallDetectionIsActiveEvent event,
      Emitter<CallRecordingsState> emit) async {
    const platform = MethodChannel('call_detection_service');
    try {
      bool result = await platform.invokeMethod('isServiceRunning');
      emit(state.copyWith(
        isServiceRunning: result,
      ));
    } on PlatformException catch (e, stackTrace) {
      print("Failed to check service status: '${e.message}'.");
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
      );
    }
  }

  FutureOr<void> _onInBuiltCallRecorderSelected(
      InBuiltCallRecorderSelectedEvent event,
      Emitter<CallRecordingsState> emit) async {
    emit(state.copyWith(
      openTheInBuiltCallRecorder: true,
    ));
  }

  FutureOr<void> _onOpenTheFileExplorerEvent(OpenTheFileExplorerEvent event,
      Emitter<CallRecordingsState> emit) async {}

  FutureOr<void> _onCancelPressed(
      OnCancelPressedEvent event, Emitter<CallRecordingsState> emit) async {
    emit(state.copyWith(
      openTheInBuiltCallRecorder: false,
    ));
  }
}
