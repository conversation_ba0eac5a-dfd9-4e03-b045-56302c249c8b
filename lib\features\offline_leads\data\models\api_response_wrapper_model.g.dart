// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'api_response_wrapper_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApiResponseWrapperModel _$ApiResponseWrapperModelFromJson(
        Map<String, dynamic> json) =>
    ApiResponseWrapperModel(
      succeeded: json['succeeded'] as bool,
      message: json['message'] as String?,
      errors:
          (json['errors'] as List<dynamic>?)?.map((e) => e as String).toList(),
      data: (json['data'] as List<dynamic>)
          .map((e) => OfflineLeadModel.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ApiResponseWrapperModelToJson(
        ApiResponseWrapperModel instance) =>
    <String, dynamic>{
      'succeeded': instance.succeeded,
      'message': instance.message,
      'errors': instance.errors,
      'data': instance.data,
    };
