

import '../../constants/hive_model_constants.dart';
import '../../models/base_url_model.dart';
import '../local_storage_service/local_storage_service.dart';

abstract class SecretManagerLocalService {
  Future<void> saveBaseUrl(BaseUrlModel baseUrlModel);

  BaseUrlModel? getBaseUrl();
}

class SecretManagerLocalServiceImpl implements SecretManagerLocalService {
  final LocalStorageService _localStorageService;

  SecretManagerLocalServiceImpl(this._localStorageService);

  @override
  BaseUrlModel? getBaseUrl() {
    try {
      return _localStorageService.getAllItems<BaseUrlModel?>(HiveModelConstants.baseUrlModelBoxName).firstOrNull;
    } catch (ex) {
      return null;
    }
  }

  @override
  Future<void> saveBaseUrl(BaseUrlModel baseUrlModel) async {
    try {
      await _localStorageService.clearContainer(HiveModelConstants.baseUrlModelBoxName);
      await _localStorageService.addItem<BaseUrlModel>(HiveModelConstants.baseUrlModelBoxName, baseUrlModel);
    } catch (ex) {
    }
  }
}