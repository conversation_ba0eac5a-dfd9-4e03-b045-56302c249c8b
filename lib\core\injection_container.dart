import 'package:call_detection/core/services/local_storage_service/local_storage_service.dart';
import 'package:call_detection/core/services/local_storage_service/local_storage_service_impl.dart';
import 'package:call_detection/core/services/secret_manager_service/secret_manager_local_service.dart';
import 'package:call_detection/core/services/secret_manager_service/secret_manager_service.dart';
import 'package:call_detection/core/services/secret_manager_service/secret_manager_service_impl.dart';
import 'package:call_detection/core/services/force_update_service/force_update_service.dart';
import 'package:call_detection/core/services/force_update_service/force_update_service_impl.dart';
import 'package:call_detection/core/config/aws_config.dart';
import 'package:call_detection/features/call_recordings/presentation/bloc/call_recordings_bloc/call_recordoings_bloc.dart';
import 'package:call_detection/features/home/<USER>/home_bloc.dart';
import 'package:call_detection/features/offline_leads/presentation/bloc/offline_leads_bloc.dart';
import 'package:call_detection/features/offline_leads/domain/usecases/sync_leads_usecase.dart';
import 'package:call_detection/features/offline_leads/domain/repository/offline_leads_repository.dart';
import 'package:call_detection/features/offline_leads/data/repository/offline_leads_repository_impl.dart';
import 'package:call_detection/features/offline_leads/data/datasources/offline_leads_local_datasource.dart';
import 'package:call_detection/features/offline_leads/data/datasources/offline_leads_remote_datasource.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import '../features/permissions/presentation/bloc/permissions_bloc.dart';
import 'managers/shared_preference_manager.dart';
import 'managers/shared_preference_manager_impl.dart';
import 'managers/token_manager.dart';

part 'di.dart';

final GetIt getIt = GetIt.instance;

Future<void> init() async {
  _registerBlocs();
  _registerUseCases();
  _registerRepositories();
  _registerDataSources();
  _registerServices();
}

void _registerBlocs() {
  getIt
    ..registerLazySingleton(() => PermissionsBloc())
    ..registerLazySingleton(() => CallRecordingsBloc())
    ..registerLazySingleton(() => HomeBloc())
    ..registerLazySingleton(() => OfflineLeadsBloc(
          syncLeadsUseCase: getIt(),
          getAllLeadsUseCase: getIt(),
          getLeadByIdUseCase: getIt(),
          deleteLeadUseCase: getIt(),
          getLeadsCountUseCase: getIt(),
          getLastSyncTimeUseCase: getIt(),
          getHiveNativeDbCountUseCase: getIt(),
        ));
}

void _registerUseCases() {
  getIt
    ..registerLazySingleton(() => SyncLeadsUseCase(getIt()))
    ..registerLazySingleton(() => GetAllLeadsUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadByIdUseCase(getIt()))
    ..registerLazySingleton(() => DeleteLeadUseCase(getIt()))
    ..registerLazySingleton(() => GetLeadsCountUseCase(getIt()))
    ..registerLazySingleton(() => GetLastSyncTimeUseCase(getIt()))
    ..registerLazySingleton(() => GetHiveFlutterDbCountUseCase(getIt()))
    ..registerLazySingleton(() => GetHiveNativeDbCountUseCase());
}

void _registerRepositories() {
  getIt.registerLazySingleton<OfflineLeadsRepository>(
    () => OfflineLeadsRepositoryImpl(
      localDataSource: getIt(),
      remoteDataSource: getIt(),
    ),
  );
}

void _registerDataSources() {
  getIt
    ..registerLazySingleton<OfflineLeadsLocalDataSource>(
      () => OfflineLeadsLocalDataSourceImpl(),
    )
    ..registerLazySingleton<OfflineLeadsRemoteDataSource>(
      () => OfflineLeadsRemoteDataSourceImpl(
        httpClient: getIt(),
        baseUrl: 'https://prd-mobile.leadrat.com',
        tenantId: 'sleep',
      ),
    );
}

void _registerServices() {
  getIt
    ..registerLazySingleton<LocalStorageService>(() => HiveServiceImpl())
    ..registerLazySingleton<SharedPreferenceManager>(
        () => SharedPreferenceManagerImpl())
    ..registerLazySingleton<TokenManager>(() =>
        TokenManager(sharedPreferenceManager: getIt<SharedPreferenceManager>()))
    ..registerLazySingleton(() => http.Client())
    ..registerLazySingleton<SecretManagerLocalService>(() => SecretManagerLocalServiceImpl(
        getIt<LocalStorageService>(),
      ))
    ..registerLazySingleton<SecretsManagerService>(() => SecretsManagerServiceImpl(
        region: AwsConfig.region,
        accessKey: AwsConfig.accessKey,
        secretKey: AwsConfig.secretKey,
        localStorageService: getIt<SecretManagerLocalService>(),
      ))
    ..registerLazySingleton<ForceUpdateService>(() => ForceUpdateServiceImpl(
        secretsManagerService: getIt<SecretsManagerService>(),
      ));
}
