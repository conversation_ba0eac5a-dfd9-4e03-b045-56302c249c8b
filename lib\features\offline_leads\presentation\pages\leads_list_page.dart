import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:async';
import 'dart:math';
import '../../../../core/enums/page_state_enum.dart';
import '../../../../core/constants/routes_names.dart';
import '../../../../core/injection_container.dart';
import '../../../call_recordings/presentation/pages/call_recordings_path_setters_page.dart';
import '../../domain/usecases/sync_leads_usecase.dart';
import '../bloc/offline_leads_bloc.dart';

class LeadsListPage extends StatefulWidget {
  const LeadsListPage({super.key});

  @override
  State<LeadsListPage> createState() => _LeadsListPageState();
}

class _LeadsListPageState extends State<LeadsListPage>
    with TickerProviderStateMixin {
  Timer? _refreshTimer;
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late AnimationController _rotationController;
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _bounceController;
  late AnimationController _floatingController;
  late AnimationController _waveController;
  late AnimationController _particleController;
  late AnimationController _morphController;

  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _floatingAnimation;
  late Animation<double> _waveAnimation;
  late Animation<double> _particleAnimation;
  late Animation<double> _morphAnimation;

  // Design theme cycling
  int _currentDesignTheme = 0;
  Timer? _themeTimer;

  // Separate notifier for Hive count to avoid full page rebuilds
  final ValueNotifier<int> _hiveCountNotifier = ValueNotifier<int>(0);
  final ValueNotifier<int> _totalCountNotifier = ValueNotifier<int>(0);
  final ValueNotifier<bool> _isSyncingNotifier = ValueNotifier<bool>(false);
  Timer? _hiveCountTimer;
  Timer? _totalCountTimer;

  @override
  void initState() {
    super.initState();
    // Use InitializeOfflineLeadsEvent instead of GetAllLeadsEvent to get Hive Native DB count
    context.read<OfflineLeadsBloc>().add(InitializeOfflineLeadsEvent());

    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _rotationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _floatingController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    _particleController = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    );
    _morphController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.bounceOut,
    ));

    _floatingAnimation = Tween<double>(
      begin: -20.0,
      end: 20.0,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    _waveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _waveController,
      curve: Curves.linear,
    ));

    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.linear,
    ));

    _morphAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _morphController,
      curve: Curves.easeInOut,
    ));

    // Start animations with delays for staggered effect
    _slideController.forward();
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
    _floatingController.repeat(reverse: true);
    _waveController.repeat();
    _particleController.repeat();
    _morphController.repeat(reverse: true);

    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) _scaleController.forward();
    });

    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) _fadeController.forward();
    });

    Future.delayed(const Duration(milliseconds: 600), () {
      if (mounted) _bounceController.forward();
    });

    // Start theme cycling timer (change design every 15 seconds)
    _startThemeCycling();

    // Initialize notifiers with current bloc state
    final currentState = context.read<OfflineLeadsBloc>().state;
    _hiveCountNotifier.value = currentState.hiveNativeDbCount;
    _totalCountNotifier.value = currentState.totalLeadsCount;
    _isSyncingNotifier.value = currentState.isSyncing;

    // Start separate timer for Hive count updates only
    _startHiveCountTimer();

    // Start separate timer for Hive count updates only
    _startTotalCountTimer();
  }

  void _startHiveCountTimer() {
    _hiveCountTimer =
        Timer.periodic(const Duration(seconds: 15), (timer) async {
      if (!mounted) return;

      try {
        final useCase = GetHiveNativeDbCountUseCase();
        final result = await useCase.call();
        result.fold(
          (error) {
            // Keep current value on error
          },
          (count) {
            if (_hiveCountNotifier.value != count) {
              _hiveCountNotifier.value = count;
            }
          },
        );

        // Also update syncing status from bloc
        if (mounted) {
          final currentState = context.read<OfflineLeadsBloc>().state;
          if (_isSyncingNotifier.value != currentState.isSyncing) {
            _isSyncingNotifier.value = currentState.isSyncing;
          }
        }
      } catch (e) {
        // Ignore errors in periodic updates
      }
    });
  }

  void _startTotalCountTimer() {
    _totalCountTimer =
        Timer.periodic(const Duration(seconds: 15), (timer) async {
      if (!mounted) return;

      try {
        final useCase = getIt<GetHiveFlutterDbCountUseCase>();
        final result = await useCase.call();
        result.fold(
          (error) {
            // Keep current value on error
          },
          (count) {
            if (_totalCountNotifier.value != count) {
              _totalCountNotifier.value = count;
            }
          },
        );

        // Also update syncing status from bloc
        if (mounted) {
          final currentState = context.read<OfflineLeadsBloc>().state;
          if (_isSyncingNotifier.value != currentState.isSyncing) {
            _isSyncingNotifier.value = currentState.isSyncing;
          }
        }
      } catch (e) {
        // Ignore errors in periodic updates
      }
    });
  }

  void _startThemeCycling() {
    _themeTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
      if (mounted) {
        setState(() {
          _currentDesignTheme =
              (_currentDesignTheme + 1) % 4; // Cycle through 4 themes
        });
      }
    });
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _hiveCountTimer?.cancel();
    _totalCountTimer?.cancel();
    _themeTimer?.cancel();
    _hiveCountNotifier.dispose();
    _totalCountNotifier.dispose();
    _isSyncingNotifier.dispose();
    _pulseController.dispose();
    _slideController.dispose();
    _rotationController.dispose();
    _scaleController.dispose();
    _fadeController.dispose();
    _bounceController.dispose();
    _floatingController.dispose();
    _waveController.dispose();
    _particleController.dispose();
    _morphController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.topRight,
            radius: 1.5,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F0F23),
              Color(0xFF0A0A0A),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Stack(
          children: [
            // Animated background particles/circles
            ...List.generate(6, (index) => _buildFloatingCircle(index)),

            // Main content
            _buildMainContent(),
          ],
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return BlocConsumer<OfflineLeadsBloc, OfflineLeadsState>(
      listener: (context, state) {
        if (state.pageState == PageState.failure &&
            state.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
        if (state.successMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.successMessage!),
              backgroundColor: Colors.green,
            ),
          );
        }
      },
      buildWhen: (previous, current) {
        // Only rebuild for loading states and complete refresh
        return previous.pageState != current.pageState ||
            previous.isCompleteRefreshing != current.isCompleteRefreshing ||
            previous.refreshProgress != current.refreshProgress;
      },
      builder: (context, state) {
        if (state.pageState == PageState.loading ||
            state.isCompleteRefreshing) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  color: Color(0XFF50BFA8),
                ),
                if (state.isCompleteRefreshing &&
                    state.refreshProgress != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    state.refreshProgress!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          );
        }

        // Dynamic animated UI structure
        return SlideTransition(
          position: _slideAnimation,
          child: Padding(
            padding: EdgeInsets.fromLTRB(
                20, MediaQuery.of(context).padding.top + 20, 20, 20),
            child: Column(
              children: [
                // Animated Header Section
                _buildAnimatedHeader(),

                const SizedBox(height: 20),

                // Dynamic Stats Grid with Staggered Animation
                Expanded(
                  flex: 3,
                  child: _buildAnimatedStatsGrid(state),
                ),

                const SizedBox(height: 20),

                // Animated Action Section
                _buildAnimatedActionSection(state),

                const SizedBox(height: 20),

                // Sync Progress (if syncing) - Animated
                BlocBuilder<OfflineLeadsBloc, OfflineLeadsState>(
                  buildWhen: (previous, current) {
                    return previous.isSyncing != current.isSyncing ||
                        previous.currentSyncPage != current.currentSyncPage ||
                        previous.totalSyncedLeads != current.totalSyncedLeads;
                  },
                  builder: (context, progressState) {
                    if (progressState.isSyncing &&
                        progressState.currentSyncPage > 0) {
                      return FadeTransition(
                        opacity: _fadeAnimation,
                        child: ScaleTransition(
                          scale: _scaleAnimation,
                          child: _buildSyncProgressCard(progressState),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedHeader() {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value.clamp(0.0, 1.0),
          child: Column(
            children: [
              // Rotating icon with title
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  AnimatedBuilder(
                    animation: _rotationAnimation,
                    builder: (context, child) {
                      return Transform.rotate(
                        angle: _rotationAnimation.value * 2 * 3.14159,
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [Color(0xFF50BFA8), Color(0xFF2DD4BF)],
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFF50BFA8)
                                    .withValues(alpha: 0.4),
                                blurRadius: 15,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.dashboard_rounded,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 16),
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: const Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Dashboard',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.2,
                          ),
                        ),
                        Text(
                          'Real-time sync status',
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 14,
                            fontWeight: FontWeight.w300,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnimatedStatsGrid(OfflineLeadsState state) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth =
            constraints.maxWidth * 0.5; // 50% of available width (half screen)
        final cardHeight = cardWidth * 0.6; // Maintain aspect ratio

        return SingleChildScrollView(
          child: Column(
            children: [
              // Row 1: Total Leads (Left)
              _buildStaggeredRow(
                isLeftAligned: true,
                cardWidth: cardWidth,
                cardHeight: cardHeight,
                child: ValueListenableBuilder<int>(
                  valueListenable: _totalCountNotifier,
                  builder: (context, hiveCount, child) {
                    return ValueListenableBuilder<bool>(
                      valueListenable: _isSyncingNotifier,
                      builder: (context, isSyncing, child) {
                        return AnimatedBuilder(
                          animation: _bounceAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: isSyncing ? _bounceAnimation.value : 1.0,
                              child: AnimatedBuilder(
                                animation: _fadeAnimation,
                                builder: (context, child) {
                                  return Opacity(
                                    opacity:
                                        _fadeAnimation.value.clamp(0.0, 1.0),
                                    child: _buildAnimatedStatsCard(
                                      title: 'Total Leads',
                                      count: '$hiveCount',
                                      icon: Icons.people_alt_rounded,
                                      gradient: const LinearGradient(
                                        colors: [
                                          Color(0xFF667eea),
                                          Color(0xFF764ba2)
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      isActive: isSyncing,
                                      delay: 0,
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        );
                      },
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),

              // Row 2: Synced Leads (Right)
              _buildStaggeredRow(
                isLeftAligned: false,
                cardWidth: cardWidth,
                cardHeight: cardHeight,
                child: ValueListenableBuilder<int>(
                  valueListenable: _hiveCountNotifier,
                  builder: (context, hiveCount, child) {
                    return ValueListenableBuilder<bool>(
                      valueListenable: _isSyncingNotifier,
                      builder: (context, isSyncing, child) {
                        return AnimatedBuilder(
                          animation: _pulseAnimation,
                          builder: (context, child) {
                            return Transform.scale(
                              scale: isSyncing ? _pulseAnimation.value : 1.0,
                              child: AnimatedBuilder(
                                animation: _fadeAnimation,
                                builder: (context, child) {
                                  return Opacity(
                                    opacity:
                                        _fadeAnimation.value.clamp(0.0, 1.0),
                                    child: _buildAnimatedStatsCard(
                                      title: 'Synced',
                                      count: '$hiveCount',
                                      icon: Icons.sync_rounded,
                                      gradient: const LinearGradient(
                                        colors: [
                                          Color(0xFF50BFA8),
                                          Color(0xFF2DD4BF)
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      isActive: isSyncing,
                                      delay: 200,
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        );
                      },
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),

              // Row 3: Quick Actions (Left)
              _buildStaggeredRow(
                isLeftAligned: true,
                cardWidth: cardWidth,
                cardHeight: cardHeight,
                child: ScaleTransition(
                  scale: Tween<double>(
                    begin: 0.8,
                    end: 1.0,
                  ).animate(CurvedAnimation(
                    parent: _scaleController,
                    curve: const Interval(0.6, 1.0, curve: Curves.elasticOut),
                  )),
                  child: _buildQuickActionsCard(),
                ),
              ),

              const SizedBox(height: 16),

              // Row 4: Last Sync Time (Right)
              _buildStaggeredRow(
                isLeftAligned: false,
                cardWidth: cardWidth,
                cardHeight: cardHeight,
                child: BlocBuilder<OfflineLeadsBloc, OfflineLeadsState>(
                  buildWhen: (previous, current) {
                    return previous.lastSyncTime != current.lastSyncTime;
                  },
                  builder: (context, timeState) {
                    return SlideTransition(
                      position: Tween<Offset>(
                        begin: const Offset(0, 1),
                        end: Offset.zero,
                      ).animate(CurvedAnimation(
                        parent: _slideController,
                        curve:
                            const Interval(0.4, 1.0, curve: Curves.elasticOut),
                      )),
                      child: _buildAnimatedSyncTimeCard(timeState),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStaggeredRow({
    required bool isLeftAligned,
    required double cardWidth,
    required double cardHeight,
    required Widget child,
  }) {
    return SizedBox(
      height: cardHeight,
      child: Stack(
        children: [
          // Animated design elements in free space
          _buildFreeSpaceDesigns(isLeftAligned, cardWidth, cardHeight),

          // Main card
          Positioned(
            left: isLeftAligned ? 0 : null,
            right: !isLeftAligned ? 0 : null,
            child: SizedBox(
              width: cardWidth,
              height: cardHeight,
              child: child,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFreeSpaceDesigns(
      bool isLeftAligned, double cardWidth, double cardHeight) {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _floatingAnimation,
          _waveAnimation,
          _rotationAnimation,
          _particleAnimation,
          _morphAnimation
        ]),
        builder: (context, child) {
          // Switch between different design themes
          switch (_currentDesignTheme) {
            case 0:
              return _buildTheme1_FloatingDots(
                  isLeftAligned, cardWidth, cardHeight);
            case 1:
              return _buildTheme2_ParticleField(
                  isLeftAligned, cardWidth, cardHeight);
            case 2:
              return _buildTheme3_GeometricPatterns(
                  isLeftAligned, cardWidth, cardHeight);
            case 3:
              return _buildTheme4_LiquidMorphing(
                  isLeftAligned, cardWidth, cardHeight);
            default:
              return _buildTheme1_FloatingDots(
                  isLeftAligned, cardWidth, cardHeight);
          }
        },
      ),
    );
  }

  // Theme 1: Original Floating Dots & Waves (Enhanced)
  Widget _buildTheme1_FloatingDots(
      bool isLeftAligned, double cardWidth, double cardHeight) {
    return Stack(
      children: [
        // Enhanced floating dots with trails
        if (!isLeftAligned) ...[
          // Left side free space when card is on right
          Positioned(
            left: 20 + _floatingAnimation.value,
            top: cardHeight * 0.2,
            child: _buildFloatingDotWithTrail(
              size: 8,
              color: const Color(0xFF50BFA8).withValues(alpha: 0.6),
              delay: 0,
            ),
          ),
          Positioned(
            left: 60 + _floatingAnimation.value * 0.5,
            top: cardHeight * 0.6,
            child: _buildFloatingDotWithTrail(
              size: 12,
              color: const Color(0xFF667eea).withValues(alpha: 0.4),
              delay: 500,
            ),
          ),
          Positioned(
            left: 100 + _floatingAnimation.value * 0.8,
            top: cardHeight * 0.4,
            child: _buildFloatingDotWithTrail(
              size: 6,
              color: const Color(0xFF764ba2).withValues(alpha: 0.5),
              delay: 1000,
            ),
          ),
        ] else ...[
          // Right side free space when card is on left
          Positioned(
            right: 20 - _floatingAnimation.value,
            top: cardHeight * 0.3,
            child: _buildFloatingDotWithTrail(
              size: 10,
              color: const Color(0xFF2DD4BF).withValues(alpha: 0.6),
              delay: 200,
            ),
          ),
          Positioned(
            right: 70 - _floatingAnimation.value * 0.7,
            top: cardHeight * 0.7,
            child: _buildFloatingDotWithTrail(
              size: 8,
              color: const Color(0xFF50BFA8).withValues(alpha: 0.4),
              delay: 700,
            ),
          ),
          Positioned(
            right: 40 - _floatingAnimation.value * 0.3,
            top: cardHeight * 0.1,
            child: _buildFloatingDotWithTrail(
              size: 14,
              color: const Color(0xFF667eea).withValues(alpha: 0.3),
              delay: 1200,
            ),
          ),
        ],

        // Enhanced wave lines
        _buildWaveLines(isLeftAligned, cardWidth, cardHeight),

        // Rotating geometric shapes
        _buildGeometricShapes(isLeftAligned, cardWidth, cardHeight),
      ],
    );
  }

  Widget _buildFloatingDotWithTrail({
    required double size,
    required Color color,
    required int delay,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 2000 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Stack(
          children: [
            // Trail effect
            for (int i = 0; i < 3; i++)
              Transform.translate(
                offset: Offset(
                  -i * 4.0 * _floatingAnimation.value.sign,
                  i * 2.0,
                ),
                child: Transform.scale(
                  scale: (0.5 + (value * 0.5)) * (1.0 - i * 0.2),
                  child: Container(
                    width: size,
                    height: size,
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: color.a * (1.0 - i * 0.3)),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: color.withValues(alpha: 0.2 * (1.0 - i * 0.3)),
                          blurRadius: size * 0.3,
                          spreadRadius: size * 0.1,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            // Main dot
            Transform.scale(
              scale: 0.5 + (value * 0.5),
              child: Container(
                width: size,
                height: size,
                decoration: BoxDecoration(
                  color: color,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: color.withValues(alpha: 0.4),
                      blurRadius: size * 0.6,
                      spreadRadius: size * 0.3,
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // Theme 2: Particle Field
  Widget _buildTheme2_ParticleField(
      bool isLeftAligned, double cardWidth, double cardHeight) {
    return Stack(
      children: [
        // Particle field effect
        ...List.generate(12, (index) {
          final double x = isLeftAligned
              ? cardWidth + 20 + (index % 4) * 30.0
              : 20 + (index % 4) * 30.0;
          final double y = (index ~/ 4) * (cardHeight / 3) + cardHeight * 0.1;

          return Positioned(
            left: isLeftAligned ? x : null,
            right: !isLeftAligned ? x : null,
            top: y + sin(_particleAnimation.value * 2 * pi + index) * 10,
            child: _buildParticle(
              size: 4 + (index % 3) * 2.0,
              color: [
                const Color(0xFF50BFA8),
                const Color(0xFF667eea),
                const Color(0xFF764ba2),
                const Color(0xFF2DD4BF),
              ][index % 4]
                  .withValues(alpha: 0.6),
              animationOffset: index * 0.2,
            ),
          );
        }),

        // Connecting lines between particles
        CustomPaint(
          painter: ParticleConnectionPainter(
            animationValue: _particleAnimation.value,
            isLeftAligned: isLeftAligned,
            cardWidth: cardWidth,
            cardHeight: cardHeight,
          ),
        ),
      ],
    );
  }

  // Theme 3: Geometric Patterns
  Widget _buildTheme3_GeometricPatterns(
      bool isLeftAligned, double cardWidth, double cardHeight) {
    return Stack(
      children: [
        // Hexagonal pattern
        ...List.generate(6, (index) {
          final double angle = (index * 60) * pi / 180;
          final double radius = 40;
          final double centerX = isLeftAligned ? cardWidth + 60 : 60;
          final double centerY = cardHeight * 0.5;

          return Positioned(
            left: isLeftAligned
                ? centerX +
                    cos(angle + _rotationAnimation.value * 2 * pi) * radius
                : null,
            right: !isLeftAligned
                ? centerX +
                    cos(angle + _rotationAnimation.value * 2 * pi) * radius
                : null,
            top: centerY +
                sin(angle + _rotationAnimation.value * 2 * pi) * radius,
            child: _buildGeometricShape(
              size: 12 + sin(_morphAnimation.value * 2 * pi + index) * 4,
              color: const Color(0xFF50BFA8).withValues(alpha: 0.5),
              shape: index % 3,
            ),
          );
        }),

        // Spiral pattern
        CustomPaint(
          painter: SpiralPainter(
            animationValue: _rotationAnimation.value,
            isLeftAligned: isLeftAligned,
            cardWidth: cardWidth,
            cardHeight: cardHeight,
          ),
        ),
      ],
    );
  }

  // Theme 4: Liquid Morphing
  Widget _buildTheme4_LiquidMorphing(
      bool isLeftAligned, double cardWidth, double cardHeight) {
    return Stack(
      children: [
        // Morphing blobs
        ...List.generate(4, (index) {
          final double x = isLeftAligned
              ? cardWidth + 30 + (index % 2) * 50.0
              : 30 + (index % 2) * 50.0;
          final double y = (index ~/ 2) * (cardHeight / 2) + cardHeight * 0.2;

          return Positioned(
            left: isLeftAligned ? x : null,
            right: !isLeftAligned ? x : null,
            top: y,
            child: _buildMorphingBlob(
              baseSize: 20 + index * 5.0,
              color: [
                const Color(0xFF50BFA8),
                const Color(0xFF667eea),
                const Color(0xFF764ba2),
                const Color(0xFF2DD4BF),
              ][index]
                  .withValues(alpha: 0.4),
              morphPhase: index * 0.5,
            ),
          );
        }),

        // Liquid connections
        CustomPaint(
          painter: LiquidConnectionPainter(
            animationValue: _morphAnimation.value,
            isLeftAligned: isLeftAligned,
            cardWidth: cardWidth,
            cardHeight: cardHeight,
          ),
        ),
      ],
    );
  }

  Widget _buildWaveLines(
      bool isLeftAligned, double cardWidth, double cardHeight) {
    return Positioned(
      left: isLeftAligned ? cardWidth + 10 : 10,
      right: !isLeftAligned ? cardWidth + 10 : 10,
      top: 0,
      bottom: 0,
      child: CustomPaint(
        painter: WavePainter(
          animationValue: _waveAnimation.value,
          color: const Color(0xFF50BFA8).withValues(alpha: 0.2),
        ),
      ),
    );
  }

  Widget _buildGeometricShapes(
      bool isLeftAligned, double cardWidth, double cardHeight) {
    return Stack(
      children: [
        if (!isLeftAligned) ...[
          // Left side geometric shapes
          Positioned(
            left: 30,
            top: cardHeight * 0.1,
            child: Transform.rotate(
              angle: _rotationAnimation.value * 2 * 3.14159,
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: const Color(0xFF667eea).withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
          Positioned(
            left: 80,
            top: cardHeight * 0.8,
            child: Transform.rotate(
              angle: -_rotationAnimation.value * 2 * 3.14159,
              child: Container(
                width: 16,
                height: 16,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: const Color(0xFF50BFA8).withValues(alpha: 0.4),
                    width: 2,
                  ),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        ] else ...[
          // Right side geometric shapes
          Positioned(
            right: 30,
            top: cardHeight * 0.2,
            child: Transform.rotate(
              angle: _rotationAnimation.value * 1.5 * 3.14159,
              child: Container(
                width: 18,
                height: 18,
                decoration: BoxDecoration(
                  color: const Color(0xFF764ba2).withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
          Positioned(
            right: 60,
            top: cardHeight * 0.6,
            child: Transform.rotate(
              angle: -_rotationAnimation.value * 1.2 * 3.14159,
              child: Container(
                width: 14,
                height: 14,
                decoration: BoxDecoration(
                  color: const Color(0xFF2DD4BF).withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  // Helper methods for new themes
  Widget _buildParticle({
    required double size,
    required Color color,
    required double animationOffset,
  }) {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        final scale = 0.5 +
            0.5 * sin(_particleAnimation.value * 2 * pi + animationOffset);
        return Transform.scale(
          scale: scale,
          child: Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: color.withValues(alpha: 0.4),
                  blurRadius: size * 0.8,
                  spreadRadius: size * 0.2,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGeometricShape({
    required double size,
    required Color color,
    required int shape,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: color,
        shape: shape == 0 ? BoxShape.circle : BoxShape.rectangle,
        borderRadius: shape == 1
            ? BorderRadius.circular(4)
            : shape == 2
                ? BorderRadius.circular(size / 2)
                : null,
        border: shape == 2 ? Border.all(color: color, width: 2) : null,
      ),
    );
  }

  Widget _buildMorphingBlob({
    required double baseSize,
    required Color color,
    required double morphPhase,
  }) {
    return AnimatedBuilder(
      animation: _morphAnimation,
      builder: (context, child) {
        final morphValue = sin(_morphAnimation.value * 2 * pi + morphPhase);
        final width = baseSize + morphValue * 8;
        final height = baseSize - morphValue * 4;

        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(width / 4),
            boxShadow: [
              BoxShadow(
                color: color.withValues(alpha: 0.3),
                blurRadius: baseSize * 0.6,
                spreadRadius: baseSize * 0.1,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAnimatedActionSection(OfflineLeadsState state) {
    return Column(
      children: [
        // Resync Button with enhanced animation
        BlocBuilder<OfflineLeadsBloc, OfflineLeadsState>(
          buildWhen: (previous, current) {
            return previous.isCompleteRefreshing !=
                    current.isCompleteRefreshing ||
                previous.refreshProgress != current.refreshProgress;
          },
          builder: (context, refreshState) {
            return AnimatedBuilder(
              animation: _bounceAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _bounceAnimation.value,
                  child: _buildEnhancedResyncButton(refreshState),
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildFloatingCircle(int index) {
    final colors = [
      const Color(0xFF50BFA8).withValues(alpha: 0.1),
      const Color(0xFF667eea).withValues(alpha: 0.08),
      const Color(0xFF764ba2).withValues(alpha: 0.06),
      const Color(0xFF2DD4BF).withValues(alpha: 0.12),
      const Color(0xFF50BFA8).withValues(alpha: 0.05),
      const Color(0xFF667eea).withValues(alpha: 0.04),
    ];

    final sizes = [120.0, 80.0, 150.0, 60.0, 100.0, 90.0];
    final positions = [
      const Alignment(0.8, -0.6),
      const Alignment(-0.7, -0.3),
      const Alignment(0.9, 0.4),
      const Alignment(-0.8, 0.7),
      const Alignment(0.3, -0.9),
      const Alignment(-0.5, 0.2),
    ];

    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Positioned.fill(
          child: Align(
            alignment: positions[index],
            child: Transform.scale(
              scale: 1.0 +
                  (_pulseAnimation.value - 1.0) *
                      0.3 *
                      (index % 2 == 0 ? 1 : -1),
              child: Container(
                width: sizes[index],
                height: sizes[index],
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: colors[index],
                  boxShadow: [
                    BoxShadow(
                      color: colors[index],
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAnimatedStatsCard({
    required String title,
    required String count,
    required IconData icon,
    required Gradient gradient,
    bool isActive = false,
    int delay = 0,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.elasticOut,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value.clamp(0.0, 1.0),
          child: Opacity(
            opacity: value.clamp(0.0, 1.0),
            child: _buildStatsCard(
              title: title,
              count: count,
              icon: icon,
              gradient: gradient,
              isActive: isActive,
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsCard({
    required String title,
    required String count,
    required IconData icon,
    required Gradient gradient,
    bool isActive = false,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.2),
            Colors.white.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(18),
      ),
      child: Container(
          padding: const EdgeInsets.fromLTRB(20, 10, 20, 10),
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: gradient.colors.first.withValues(alpha: 0.4),
                blurRadius: 20,
                offset: const Offset(0, 8),
                spreadRadius: 2,
              ),
              BoxShadow(
                color: gradient.colors.last.withValues(alpha: 0.2),
                blurRadius: 40,
                offset: const Offset(0, 16),
                spreadRadius: 4,
              ),
            ],
          ),
          child: Center(
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(5),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          letterSpacing: 0.5,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        count,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          letterSpacing: 1.0,
                        ),
                      ),
                    ],
                  ),
                ),
                if (isActive)
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.sync,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
              ],
            ),
          )),
    );
  }

  Widget _buildSyncTimeCard(OfflineLeadsState state) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.15),
            Colors.white.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(18),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFF2A2A3E).withValues(alpha: 0.8),
              const Color(0xFF1A1A2E).withValues(alpha: 0.9),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.schedule_rounded,
              color: Colors.grey[400],
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              'Last Sync',
              style: TextStyle(
                color: Colors.grey[400],
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              state.lastSyncTime != null
                  ? _formatDateTime(state.lastSyncTime!)
                  : 'Never synced',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedSyncTimeCard(OfflineLeadsState state) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.bounceOut,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value.clamp(0.0, 1.0))),
          child: Opacity(
            opacity: value.clamp(0.0, 1.0),
            child: _buildSyncTimeCard(state),
          ),
        );
      },
    );
  }

  Widget _buildQuickActionsCard() {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF667eea),
            Color(0xFF764ba2),
            Color(0xFF50BFA8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withValues(alpha: 0.4),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 2,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => _showQuickActionsBottomSheet(context),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                AnimatedBuilder(
                  animation: _rotationAnimation,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _rotationAnimation.value * 0.1,
                      child: const Icon(
                        Icons.settings_rounded,
                        color: Colors.white,
                        size: 32,
                      ),
                    );
                  },
                ),
                const SizedBox(height: 8),
                const Text(
                  'Quick Actions',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                const Text(
                  'Tap to expand',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showQuickActionsBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0F0F23),
            ],
          ),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(24),
            topRight: Radius.circular(24),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 24),

              // Title
              const Text(
                'Quick Actions',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Choose an action to perform',
                style: TextStyle(
                  color: Colors.grey[400],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 32),

              // Action buttons
              _buildQuickActionButton(
                title: 'Allow Permissions',
                subtitle: 'Grant necessary app permissions',
                icon: Icons.security,
                gradient: const LinearGradient(
                  colors: [Color(0xFF667eea), Color(0xFF764ba2)],
                ),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, RoutesName.permissions);
                },
              ),
              const SizedBox(height: 16),

              _buildQuickActionButton(
                title: 'Reset Call Recording',
                subtitle: 'Configure call recording settings',
                icon: Icons.settings_backup_restore,
                gradient: const LinearGradient(
                  colors: [Color(0xFF50BFA8), Color(0xFF2DD4BF)],
                ),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CallRecordingsPathSetPage(),
                    ),
                  );
                },
              ),
              const SizedBox(height: 16),

              _buildQuickActionButton(
                title: 'Sync Data',
                subtitle: 'Manually sync all data',
                icon: Icons.sync_rounded,
                gradient: const LinearGradient(
                  colors: [Color(0xFFf093fb), Color(0xFFf5576c)],
                ),
                onTap: () {
                  Navigator.pop(context);
                  context
                      .read<OfflineLeadsBloc>()
                      .add(CompleteDatabaseRefreshEvent());
                },
              ),

              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActionButton({
    required String title,
    required String subtitle,
    required IconData icon,
    required Gradient gradient,
    required VoidCallback onTap,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: gradient.colors.first.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.8),
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.white.withValues(alpha: 0.7),
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEnhancedResyncButton(OfflineLeadsState state) {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: state.isCompleteRefreshing
            ? LinearGradient(
                colors: [
                  Colors.grey.withValues(alpha: 0.6),
                  Colors.grey.withValues(alpha: 0.8),
                ],
              )
            : const LinearGradient(
                colors: [
                  Color(0xFF50BFA8),
                  Color(0xFF2DD4BF),
                  Color(0xFF667eea),
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: state.isCompleteRefreshing
            ? []
            : [
                BoxShadow(
                  color: const Color(0xFF50BFA8).withValues(alpha: 0.5),
                  blurRadius: 25,
                  offset: const Offset(0, 10),
                  spreadRadius: 3,
                ),
                BoxShadow(
                  color: const Color(0xFF2DD4BF).withValues(alpha: 0.3),
                  blurRadius: 35,
                  offset: const Offset(0, 18),
                  spreadRadius: 5,
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(28),
          onTap: state.isCompleteRefreshing
              ? null
              : () {
                  context
                      .read<OfflineLeadsBloc>()
                      .add(CompleteDatabaseRefreshEvent());
                },
          child: Center(
            child: state.isCompleteRefreshing
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AnimatedBuilder(
                        animation: _rotationAnimation,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _rotationAnimation.value * 2 * 3.14159,
                            child: const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 2,
                              ),
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 12),
                      Flexible(
                        child: Text(
                          state.refreshProgress ?? 'Syncing...',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      AnimatedBuilder(
                        animation: _pulseAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _pulseAnimation.value,
                            child: const Icon(
                              Icons.refresh_rounded,
                              color: Colors.white,
                              size: 24,
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'Resync Data',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildSyncProgressCard(OfflineLeadsState state) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667eea).withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Icon(
                Icons.sync,
                color: Colors.white,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                'Syncing in Progress',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Page ${state.currentSyncPage}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${state.totalSyncedLeads} synced',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

class ParticleConnectionPainter extends CustomPainter {
  final double animationValue;
  final bool isLeftAligned;
  final double cardWidth;
  final double cardHeight;

  ParticleConnectionPainter({
    required this.animationValue,
    required this.isLeftAligned,
    required this.cardWidth,
    required this.cardHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF50BFA8).withValues(alpha: 0.3)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // Draw connecting lines between particles
    for (int i = 0; i < 12; i++) {
      for (int j = i + 1; j < 12; j++) {
        final x1 = isLeftAligned
            ? cardWidth + 20 + (i % 4) * 30.0
            : 20 + (i % 4) * 30.0;
        final y1 = (i ~/ 4) * (cardHeight / 3) + cardHeight * 0.1;

        final x2 = isLeftAligned
            ? cardWidth + 20 + (j % 4) * 30.0
            : 20 + (j % 4) * 30.0;
        final y2 = (j ~/ 4) * (cardHeight / 3) + cardHeight * 0.1;

        final distance = sqrt(pow(x2 - x1, 2) + pow(y2 - y1, 2));

        if (distance < 60) {
          final opacity =
              (1.0 - distance / 60) * sin(animationValue * 2 * pi + i);
          if (opacity > 0) {
            paint.color =
                const Color(0xFF50BFA8).withValues(alpha: opacity * 0.3);
            canvas.drawLine(Offset(x1, y1), Offset(x2, y2), paint);
          }
        }
      }
    }
  }

  @override
  bool shouldRepaint(ParticleConnectionPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

class SpiralPainter extends CustomPainter {
  final double animationValue;
  final bool isLeftAligned;
  final double cardWidth;
  final double cardHeight;

  SpiralPainter({
    required this.animationValue,
    required this.isLeftAligned,
    required this.cardWidth,
    required this.cardHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF667eea).withValues(alpha: 0.4)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final centerX = isLeftAligned ? cardWidth + 60 : 60;
    final centerY = cardHeight * 0.5;

    for (double t = 0; t < 4 * pi; t += 0.1) {
      final radius = t * 3 + animationValue * 20;
      final x = centerX + cos(t + animationValue * 2 * pi) * radius;
      final y = centerY + sin(t + animationValue * 2 * pi) * radius;

      if (t == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(SpiralPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

class LiquidConnectionPainter extends CustomPainter {
  final double animationValue;
  final bool isLeftAligned;
  final double cardWidth;
  final double cardHeight;

  LiquidConnectionPainter({
    required this.animationValue,
    required this.isLeftAligned,
    required this.cardWidth,
    required this.cardHeight,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFF2DD4BF).withValues(alpha: 0.3)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    // Create flowing liquid connections
    for (int i = 0; i < 4; i++) {
      final x1 =
          isLeftAligned ? cardWidth + 30 + (i % 2) * 50.0 : 30 + (i % 2) * 50.0;
      final y1 = (i ~/ 2) * (cardHeight / 2) + cardHeight * 0.2;

      if (i < 3) {
        final x2 = isLeftAligned
            ? cardWidth + 30 + ((i + 1) % 2) * 50.0
            : 30 + ((i + 1) % 2) * 50.0;
        final y2 = ((i + 1) ~/ 2) * (cardHeight / 2) + cardHeight * 0.2;

        final path = Path();
        path.moveTo(x1, y1);

        // Create flowing curve
        final controlX = (x1 + x2) / 2 + sin(animationValue * 2 * pi + i) * 20;
        final controlY = (y1 + y2) / 2 + cos(animationValue * 2 * pi + i) * 15;

        path.quadraticBezierTo(controlX, controlY, x2, y2);
        canvas.drawPath(path, paint);
      }
    }
  }

  @override
  bool shouldRepaint(LiquidConnectionPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

class WavePainter extends CustomPainter {
  final double animationValue;
  final Color color;

  WavePainter({
    required this.animationValue,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final path = Path();
    final waveHeight = size.height * 0.1;
    final waveLength = size.width * 0.3;

    // Create animated wave
    for (double x = 0; x <= size.width; x += 1) {
      final y = size.height * 0.5 +
          waveHeight * sin((x / waveLength + animationValue * 2 * pi) * 2 * pi);

      if (x == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }

    // Draw multiple wave lines with different phases
    canvas.drawPath(path, paint);

    // Second wave with different phase
    final path2 = Path();
    for (double x = 0; x <= size.width; x += 1) {
      final y = size.height * 0.3 +
          waveHeight *
              0.7 *
              sin((x / waveLength + animationValue * 2 * pi + pi / 2) * 2 * pi);

      if (x == 0) {
        path2.moveTo(x, y);
      } else {
        path2.lineTo(x, y);
      }
    }

    final paint2 = Paint()
      ..color = color.withValues(alpha: color.a * 0.5)
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    canvas.drawPath(path2, paint2);
  }

  @override
  bool shouldRepaint(WavePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}
