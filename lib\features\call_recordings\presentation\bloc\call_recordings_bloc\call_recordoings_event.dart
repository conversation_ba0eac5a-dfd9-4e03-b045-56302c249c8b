part of 'call_recordoings_bloc.dart';

@immutable
sealed class CallRecordingsEvent {}

class CallRecordingsInitialEvent extends CallRecordingsEvent {}

class GetCallRecordingsEvent extends CallRecordingsEvent {
  final String? recordingsPath;

  GetCallRecordingsEvent({
    this.recordingsPath,
  });
}

final class StartCallDetectionEvent extends CallRecordingsEvent {}

final class CheckTheCallDetectionIsActiveEvent extends CallRecordingsEvent {}

final class InBuiltCallRecorderSelectedEvent extends CallRecordingsEvent {}

final class OpenTheFileExplorerEvent extends CallRecordingsEvent {}

final class OnCancelPressedEvent extends CallRecordingsEvent {}

final class IsGoogleDialerEvent extends CallRecordingsEvent {}

final class IsCallRecordingEnabledForTenantEvent extends CallRecordingsEvent {}
