package com.leadrat.call_detection.call_detection;

import android.Manifest;
import android.app.ActivityManager;
import android.content.pm.PackageManager;
import androidx.core.content.ContextCompat;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;

import com.leadrat.call_detection.call_detection.services.CallDetectionService;

/**
 * ServiceMonitor is responsible for ensuring the CallDetectionService
 * stays running. It provides methods to check if the service is running
 * and restart it if necessary.
 */
public class ServiceMonitor {
    private static final String TAG = "ServiceMonitor";
    private static final long CHECK_INTERVAL = 60000; // 1 minute
    private static Handler handler;
    private static boolean isMonitoring = false;
    private static final Runnable serviceChecker = new Runnable() {
        @Override
        public void run() {
            Context context = MainActivity.getAppContext();
            if (context != null) {
                checkAndRestartService(context);
                // Schedule next check
                if (handler != null && isMonitoring) {
                    handler.postDelayed(this, CHECK_INTERVAL);
                }
            }
        }
    };

    /**
     * Start monitoring the CallDetectionService
     * @param context Application context
     */
    public static void startMonitoring(Context context) {
        if (isMonitoring) {
            return;
        }

        Log.d(TAG, "Starting service monitoring");
        if (handler == null) {
            handler = new Handler(Looper.getMainLooper());
        }

        isMonitoring = true;

        // Run immediately first time
        checkAndRestartService(context);

        // Schedule periodic checks
        handler.postDelayed(serviceChecker, CHECK_INTERVAL);
    }

    /**
     * Stop monitoring the CallDetectionService
     */
    public static void stopMonitoring() {
        Log.d(TAG, "Stopping service monitoring");
        isMonitoring = false;
        if (handler != null) {
            handler.removeCallbacks(serviceChecker);
        }
    }

    /**
     * Check if the CallDetectionService is running and restart it if necessary
     * @param context Application context
     */
    public static void checkAndRestartService(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null, cannot check service");
            return;
        }

        boolean isRunning = isServiceRunning(context, CallDetectionService.class);
        Log.d(TAG, "Service running check: " + isRunning);

        if (!isRunning) {
            Log.d(TAG, "Service not running, attempting to restart");
            startCallDetectionService(context);
        }

        // Also check if call recording is enabled
        checkCallRecordingEnabled(context);
    }

    /**
     * Start the CallDetectionService
     * @param context Application context
     */
    public static void startCallDetectionService(Context context) {
        try {
            // Check for required permissions before starting service
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                boolean hasBasicPermissions = ContextCompat.checkSelfPermission(context, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED &&
                    ContextCompat.checkSelfPermission(context, Manifest.permission.READ_CALL_LOG) == PackageManager.PERMISSION_GRANTED;

                // Also check notification permission for Android 13+
                boolean hasNotificationPermission = true;
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    hasNotificationPermission = ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED;
                    Log.d(TAG, "Notification permission check: " + hasNotificationPermission);
                }

                if (!hasBasicPermissions || !hasNotificationPermission) {
                    Log.e(TAG, "Required permissions not granted. Cannot start CallDetectionService.");
                    Log.e(TAG, "Phone/Call Log permissions: " + hasBasicPermissions + ", Notification permission: " + hasNotificationPermission);

                    // If only notification permission is missing, request it
                    if (hasBasicPermissions && !hasNotificationPermission && Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        Log.d(TAG, "Requesting notification permission via MainActivity");
                        MainActivity mainActivity = MainActivity.getInstance();
                        if (mainActivity != null) {
                            mainActivity.requestNotificationPermission();
                        }
                    }

                    return; // Don't start service if permissions are not granted
                }
            }

            Intent serviceIntent = new Intent(context, CallDetectionService.class);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent);
            } else {
                context.startService(serviceIntent);
            }
            Log.d(TAG, "Service start requested");
        } catch (Exception e) {
            Log.e(TAG, "Failed to start service: " + e.getMessage());
        }
    }

    /**
     * Check if a service is running
     * @param context Application context
     * @param serviceClass Service class to check
     * @return true if service is running, false otherwise
     */
    public static boolean isServiceRunning(Context context, Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                return true;
            }
        }
        return false;
    }

    /**
     * Check if call recording is enabled for the tenant
     * @param context Application context
     */
    private static void checkCallRecordingEnabled(Context context) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        long lastCheck = preferences.getLong("LastCallRecordingCheck", 0);
        long currentTime = System.currentTimeMillis();

        // Check once per hour (60000 ms)
        if (currentTime - lastCheck > 60000) {
            Log.d(TAG, "Checking if call recording is enabled for tenant");

            // This will be handled by the CallReceiver
            Intent intent = new Intent("com.leadrat.call_detection.CHECK_RECORDING_ENABLED");
            context.sendBroadcast(intent);

            // Update last check time
            preferences.edit().putLong("LastCallRecordingCheck", currentTime).apply();
        }
    }
}
