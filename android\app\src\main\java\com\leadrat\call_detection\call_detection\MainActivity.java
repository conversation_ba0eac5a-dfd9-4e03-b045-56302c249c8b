package com.leadrat.call_detection.call_detection;

import io.flutter.embedding.android.FlutterActivity;
import io.flutter.embedding.engine.FlutterEngine;
import io.flutter.plugin.common.MethodChannel;
import java.util.List;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import android.Manifest;
import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.provider.Settings;
import android.text.TextUtils;
import android.content.BroadcastReceiver;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.telecom.TelecomManager;

import com.leadrat.call_detection.call_detection.services.CallDetectionService;

import com.leadrat.call_detection.call_detection.services.DataSyncService;

import java.lang.ref.WeakReference;

public class MainActivity extends FlutterActivity {
    private static final String CHANNEL = "call_detection_service";
    private static final String DATA_SYNC_CHANNEL = "data_sync_service";
    private static final String LEADRAT_GLOBAL_DATA_CHANNEL = "leadrat_global_data";
    private static final int REQUEST_CODE = 1001;
    private static final int NOTIFICATION_PERMISSION_REQUEST_CODE = 1002;
    private static WeakReference<Context> appContextRef;
    private static WeakReference<MainActivity> instanceRef;
    private MethodChannel dataSyncMethodChannel;

    // Database synchronization object to prevent concurrent access
    private static final Object HIVE_DB_LOCK = new Object();

    // Provide a static method to get the application context
    public static Context getAppContext() {
        return appContextRef != null ? appContextRef.get() : null;
    }

    // Provide a static method to get the MainActivity instance
    public static MainActivity getInstance() {
        return instanceRef != null ? instanceRef.get() : null;
    }

    // Provide access to the data sync method channel
    public MethodChannel getDataSyncMethodChannel() {
        return dataSyncMethodChannel;
    }

    // Check if Flutter engine is available and ready
    public boolean isFlutterEngineReady() {
        try {
            if (getFlutterEngine() == null) {
                return false;
            }

            if (getFlutterEngine().getDartExecutor() == null) {
                return false;
            }

            if (getFlutterEngine().getDartExecutor().getBinaryMessenger() == null) {
                return false;
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Store application context in WeakReference
        appContextRef = new WeakReference<>(getApplicationContext());

        // Store MainActivity instance in WeakReference
        instanceRef = new WeakReference<>(this);

        // Initialize the ServiceMonitor in background thread to prevent ANR
        new Thread(() -> {
            try {
                ServiceMonitor.startMonitoring(getApplicationContext());
            } catch (Exception e) {
                Log.e("MainActivity", "Failed to start ServiceMonitor: " + e.getMessage());
            }
        }).start();

        // Initialize data sync method channel
        dataSyncMethodChannel = new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), DATA_SYNC_CHANNEL);
        dataSyncMethodChannel.setMethodCallHandler((call, result) -> {
            Log.d("MainActivity", "🔥 [METHOD_CHANNEL] Received method call: " + call.method);
            if (call.method.equals("getNativeLeadsData")) {
                // Get leads data from native SQLite database (in background thread to avoid ANR)
                new Thread(() -> {
                    try {
                        // Use the database helper directly
                        DataSyncService.NativeLeadsDbHelper dbHelper = new DataSyncService.NativeLeadsDbHelper(this);
                        List<Map<String, Object>> leadsData = getNativeLeadsFromDatabase(dbHelper);

                        // Return result on main thread
                        runOnUiThread(() -> result.success(leadsData));
                    } catch (Exception e) {
                        runOnUiThread(() -> result.error("DATABASE_ERROR", "Failed to get native leads data: " + e.getMessage(), null));
                    }
                }).start();
            } else if (call.method.equals("saveHiveDataToNative")) {
                // Save Hive data to native SQLite database (in background thread to avoid ANR)
                new Thread(() -> {
                    try {
                        List<Map<String, Object>> hiveData = (List<Map<String, Object>>) call.arguments;
                        boolean success = saveHiveDataToNativeDatabase(hiveData);

                        // Return result on main thread
                        runOnUiThread(() -> result.success(success));
                    } catch (Exception e) {
                        runOnUiThread(() -> result.error("DATABASE_ERROR", "Failed to save Hive data to native database: " + e.getMessage(), null));
                    }
                }).start();
            } else if (call.method.equals("clearNativeLeadsData")) {
                // Clear native SQLite database (in background thread to avoid ANR)
                new Thread(() -> {
                    try {
                        boolean success = clearNativeLeadsDatabase();

                        // Return result on main thread
                        runOnUiThread(() -> result.success(success));
                    } catch (Exception e) {
                        runOnUiThread(() -> result.error("DATABASE_ERROR", "Failed to clear native leads data: " + e.getMessage(), null));
                    }
                }).start();
            } else if (call.method.equals("getTenantId")) {
                // Get Tenant ID from SharedPreferences
                String tenantId = getTenantIdFromPreferences();
                result.success(tenantId);
            } else if (call.method.equals("getUserId")) {
                // Get User ID from SharedPreferences
                String userId = getUserIdFromPreferences();
                result.success(userId);
            } else if (call.method.equals("getHiveNativeDbCount")) {
                // Get Hive Native DB count - use EXACT same code as DataSyncService
                try {
                    Log.d("MainActivity", "🔍 [HIVE_NATIVE_COUNT] Getting Hive Native DB count using DataSyncService method...");

                    // Check Hive native database status (EXACT same code as DataSyncService)
                    SharedPreferences hiveDbPrefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
                    boolean hiveHasData = hiveDbPrefs.getBoolean("has_data", false);
                    int hiveTotalLeads = hiveDbPrefs.getInt("total_leads", 0);
                    long hiveLastUpdate = hiveDbPrefs.getLong("last_update_time", 0);

                    Log.d("MainActivity", "🔍 [HIVE_NATIVE_COUNT] Hive Native DB: hasData=" + hiveHasData + ", totalLeads=" + hiveTotalLeads + ", lastUpdate=" + new java.util.Date(hiveLastUpdate));

                    result.success(hiveTotalLeads);

                } catch (Exception e) {
                    Log.e("MainActivity", "❌ [HIVE_NATIVE_COUNT] Error: " + e.getMessage());
                    result.error("DATABASE_ERROR", "Failed to get Hive Native DB count: " + e.getMessage(), null);
                }
            } else {
                result.notImplemented();
            }
        });

        // Initialize LeadRat global data method channel
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), LEADRAT_GLOBAL_DATA_CHANNEL)
                .setMethodCallHandler((call, result) -> {
                    if (call.method.equals("getLeadratTenantId")) {
                        String tenantId = getLeadratTenantId();
                        result.success(tenantId);
                    } else if (call.method.equals("getLeadratUserId")) {
                        String userId = getLeadratUserId();
                        result.success(userId);
                    } else if (call.method.equals("setTestLeadratData")) {
                        // For testing purposes - set test data
                        setTestLeadratData();
                        result.success("Test data set");
                    } else {
                        result.notImplemented();
                    }
                });

        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), CHANNEL)
                .setMethodCallHandler((call, result) -> {
                    if (call.method.equals("startService")) {
                        // Run service start in background thread to prevent ANR
                        new Thread(() -> {
                            try {
                                // Check permissions before starting service
                                if (checkPhoneAndCallLogPermissions()) {
                                    Intent serviceIntent = new Intent(this, CallDetectionService.class);
                                    startForegroundService(serviceIntent);
                                    runOnUiThread(() -> result.success("Service Started"));
                                } else {
                                    // Don't start service if permissions not granted
                                    runOnUiThread(() -> result.error("PERMISSION_DENIED", "Phone and call log permissions are required", null));
                                }
                            } catch (Exception e) {
                                runOnUiThread(() -> result.error("SERVICE_ERROR", "Failed to start service: " + e.getMessage(), null));
                            }
                        }).start();
                    } else if (call.method.equals("isServiceRunning")) {
                        boolean isRunning = isServiceRunning(CallDetectionService.class);
                        result.success(isRunning);
                    }  else if (call.method.equals("isGoogleDialerDefault")) {
                        boolean isGoogleDefault = isGoogleDialerDefault(this);
                        result.success(isGoogleDefault);
                    } else if (call.method.equals("requestPermissions")) {
                        requestPermissions();
                        result.success(true);
                    } else if (call.method.equals("checkPhoneAndCallLogPermissions")) {
                        boolean hasPermissions = checkPhoneAndCallLogPermissions();
                        result.success(hasPermissions);
                    } else if (call.method.equals("requestPhoneAndCallLogPermissions")) {
                        // Run permission request in background thread with timeout to prevent ANR
                        new Thread(() -> {
                            try {
                                requestPhoneAndCallLogPermissions();
                                runOnUiThread(() -> result.success(true));
                            } catch (Exception e) {
                                runOnUiThread(() -> result.error("PERMISSION_ERROR", "Failed to request permissions: " + e.getMessage(), null));
                            }
                        }).start();
                    } else if (call.method.equals("saveDirectoryPath")) {
                        String directoryPath = call.argument("path");
                        if (directoryPath != null) {
                            saveDirectoryPathInPreferences(getApplicationContext(), directoryPath);
                            result.success("Saved Successfully");
                        } else {
                            result.error("INVALID_ARGUMENT", "Path is null", null);
                        }
                    }   else if (call.method.equals("removeDirectoryPath")) {
                        removeDirectoryPathFromPreferences(getApplicationContext());
                        result.success("Removed Successfully");
                    } else if (call.method.equals("getTenantId")) {
                        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());
                        String tenantId = prefs.getString("TenantId", "");
                        result.success(tenantId);
                    } else if (call.method.equals("startDataSyncService")) {
                        DataSyncService.startService(getApplicationContext());
                        result.success("Data sync service started");
                    } else if (call.method.equals("stopDataSyncService")) {
                        DataSyncService.stopService(getApplicationContext());
                        result.success("Data sync service stopped");
                    } else if (call.method.equals("isDataSyncServiceRunning")) {
                        boolean isRunning = DataSyncService.isServiceRunning(getApplicationContext());
                        result.success(isRunning);
                    } else if (call.method.equals("getDataSyncStats")) {
                        String stats = DataSyncService.getSyncStats(getApplicationContext());
                        result.success(stats);
                    } else if (call.method.equals("searchLeadByPhoneNumber")) {
                        String phoneNumber = call.argument("phoneNumber");
                        if (phoneNumber != null) {
                            searchLeadByPhoneNumber(phoneNumber, result);
                        } else {
                            result.error("INVALID_ARGUMENT", "Phone number is required", null);
                        }
                    } else if (call.method.equals("clearHiveNativeDatabase")) {
                        // Clear Hive Native database (in background thread to avoid ANR)
                        new Thread(() -> {
                            try {
                                boolean success = clearHiveNativeDatabase();

                                // Return result on main thread
                                runOnUiThread(() -> result.success(success));
                            } catch (Exception e) {
                                runOnUiThread(() -> result.error("DATABASE_ERROR", "Failed to clear Hive Native database: " + e.getMessage(), null));
                            }
                        }).start();
                    } else if (call.method.equals("searchInNativeDatabase")) {
                        // Search in native database (in background thread to avoid ANR)
                        String phoneNumber = call.argument("phoneNumber");
                        if (phoneNumber != null) {
                            new Thread(() -> {
                                try {
                                    Log.d("MainActivity", "📞 [NATIVE_SEARCH] Searching native database for: " + phoneNumber);

                                    // Use the same native search logic as CallReceiver
                                    Map<String, Object> result_data = searchInNativeDatabaseForFlutter(phoneNumber);

                                    // Return result on main thread
                                    runOnUiThread(() -> result.success(result_data));
                                } catch (Exception e) {
                                    Log.e("MainActivity", "❌ [NATIVE_SEARCH] Error: " + e.getMessage());
                                    runOnUiThread(() -> result.error("SEARCH_ERROR", "Failed to search native database: " + e.getMessage(), null));
                                }
                            }).start();
                        } else {
                            result.error("INVALID_ARGUMENT", "Phone number is required", null);
                        }
                    } else  {
                        result.notImplemented();
                    }
                });
    }

    private boolean isServiceRunning(Class<?> serviceClass) {
        ActivityManager manager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
        for (ActivityManager.RunningServiceInfo service : manager.getRunningServices(Integer.MAX_VALUE)) {
            if (serviceClass.getName().equals(service.service.getClassName())) {
                Log.d("Service Check", "CallDetectionService is running");
                return true;
            }
        }
        Log.d("Service Check", "CallDetectionService is NOT running");
        return false;
    }

    public static boolean isGoogleDialerDefault(MainActivity context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            TelecomManager telecomManager = (TelecomManager) context.getSystemService(Context.TELECOM_SERVICE);
            String defaultDialer = telecomManager.getDefaultDialerPackage();
            return "com.google.android.dialer".equals(defaultDialer);
        }
        return false;
    }

    private void requestPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED ||
                    ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CALL_LOG) != PackageManager.PERMISSION_GRANTED ||
                    (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE &&
                            (ContextCompat.checkSelfPermission(this, Manifest.permission.FOREGROUND_SERVICE_PHONE_CALL) != PackageManager.PERMISSION_GRANTED ||
                             ContextCompat.checkSelfPermission(this, Manifest.permission.FOREGROUND_SERVICE_DATA_SYNC) != PackageManager.PERMISSION_GRANTED))) {

                ActivityCompat.requestPermissions(this, new String[]{
                        Manifest.permission.READ_PHONE_STATE,
                        Manifest.permission.READ_CALL_LOG,
                        Manifest.permission.FOREGROUND_SERVICE_PHONE_CALL,
                        Manifest.permission.FOREGROUND_SERVICE_DATA_SYNC
                }, REQUEST_CODE);
            }
        }

        // Request MANAGE_OWN_CALLS permission separately if needed
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
            Intent roleRequestIntent = new Intent(Settings.ACTION_MANAGE_DEFAULT_APPS_SETTINGS);
            startActivity(roleRequestIntent);
        }
    }

    private boolean checkPhoneAndCallLogPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean hasPhonePermissions = ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) == PackageManager.PERMISSION_GRANTED &&
                   ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CALL_LOG) == PackageManager.PERMISSION_GRANTED;

            // Also check notification permission for Android 13+
            boolean hasNotificationPermission = true;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                hasNotificationPermission = ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED;
                Log.d("Permission Check", "Notification permission: " + hasNotificationPermission);
            }

            // Check foreground service permissions for Android 14+
            boolean hasForegroundServicePermissions = true;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE) {
                hasForegroundServicePermissions = ContextCompat.checkSelfPermission(this, Manifest.permission.FOREGROUND_SERVICE_PHONE_CALL) == PackageManager.PERMISSION_GRANTED &&
                        ContextCompat.checkSelfPermission(this, Manifest.permission.FOREGROUND_SERVICE_DATA_SYNC) == PackageManager.PERMISSION_GRANTED;
                Log.d("Permission Check", "Foreground service permissions: " + hasForegroundServicePermissions);
            }

            return hasPhonePermissions && hasNotificationPermission && hasForegroundServicePermissions;
        }
        return true; // On older Android versions, permissions are granted at install time
    }

    private void requestPhoneAndCallLogPermissions() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.READ_PHONE_STATE) != PackageManager.PERMISSION_GRANTED ||
                ContextCompat.checkSelfPermission(this, Manifest.permission.READ_CALL_LOG) != PackageManager.PERMISSION_GRANTED ||
                (Build.VERSION.SDK_INT >= Build.VERSION_CODES.UPSIDE_DOWN_CAKE &&
                        (ContextCompat.checkSelfPermission(this, Manifest.permission.FOREGROUND_SERVICE_PHONE_CALL) != PackageManager.PERMISSION_GRANTED ||
                         ContextCompat.checkSelfPermission(this, Manifest.permission.FOREGROUND_SERVICE_DATA_SYNC) != PackageManager.PERMISSION_GRANTED))) {

                ActivityCompat.requestPermissions(this, new String[]{
                        Manifest.permission.READ_PHONE_STATE,
                        Manifest.permission.READ_CALL_LOG,
                        Manifest.permission.FOREGROUND_SERVICE_PHONE_CALL,
                        Manifest.permission.FOREGROUND_SERVICE_DATA_SYNC
                }, REQUEST_CODE);
            }
        }
    }

    /**
     * Request notification permission for Android 13+ (API 33+)
     * This is needed for the foreground service to show notifications
     */
    public void requestNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                Log.d("Permission", "Requesting notification permission");
                ActivityCompat.requestPermissions(this, new String[]{
                        Manifest.permission.POST_NOTIFICATIONS
                }, NOTIFICATION_PERMISSION_REQUEST_CODE);
            } else {
                Log.d("Permission", "Notification permission already granted");
                // Restart the service since permission is already granted
                Intent serviceIntent = new Intent(this, CallDetectionService.class);
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    startForegroundService(serviceIntent);
                } else {
                    startService(serviceIntent);
                }
            }
        }
    }

    @Override
    protected void onDestroy() {
        Log.d("MainActivity", "🔄 [LIFECYCLE] MainActivity onDestroy() called");

        try {
            // Clear the instance reference when activity is destroyed
            instanceRef = null;
            dataSyncMethodChannel = null;
            Log.d("MainActivity", "✅ [LIFECYCLE] MainActivity instance cleared on destroy");
        } catch (Exception e) {
            Log.e("MainActivity", "❌ [LIFECYCLE] Error during onDestroy: " + e.getMessage());
        }

        super.onDestroy();
    }

    @Override
    protected void onPause() {
        Log.d("MainActivity", "🔄 [LIFECYCLE] MainActivity onPause() called");
        super.onPause();
    }

    @Override
    protected void onStop() {
        Log.d("MainActivity", "🔄 [LIFECYCLE] MainActivity onStop() called");
        super.onStop();
    }

    public void searchLeadByPhoneNumber(String phoneNumber, MethodChannel.Result result) {
        Log.d("MainActivity", "📞 [CALL_DETECTION] Searching for lead by phone number: " + phoneNumber);

        // Call Flutter method to search in local database
        new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), "offline_leads_search")
                .invokeMethod("searchByPhoneNumber", phoneNumber, new MethodChannel.Result() {
                    @Override
                    public void success(Object leadData) {
                        Log.d("MainActivity", "📞 [CALL_DETECTION] Lead search result: " + (leadData != null ? "Found" : "Not found"));
                        result.success(leadData);
                    }

                    @Override
                    public void error(String errorCode, String errorMessage, Object errorDetails) {
                        Log.e("MainActivity", "📞 [CALL_DETECTION] Lead search failed: " + errorMessage);
                        result.error(errorCode, errorMessage, errorDetails);
                    }

                    @Override
                    public void notImplemented() {
                        Log.e("MainActivity", "📞 [CALL_DETECTION] Lead search method not implemented");
                        result.error("NOT_IMPLEMENTED", "Lead search method not implemented", null);
                    }
                });
    }

    /**
     * Search in native database for Flutter calls - uses the same logic as CallReceiver
     */
    private Map<String, Object> searchInNativeDatabaseForFlutter(String phoneNumber) {
        Log.d("MainActivity", "📞 [NATIVE_SEARCH_FLUTTER] Starting native database search for: " + phoneNumber);

        try {
            // FIRST: Try to search in Hive native database (primary source)
            Map<String, Object> hiveLeadData = searchInHiveNativeDatabase(phoneNumber);

            if (hiveLeadData != null) {
                String leadName = (String) hiveLeadData.get("name");
                String leadId = (String) hiveLeadData.get("id");
                String contactNo = (String) hiveLeadData.get("contactNo");
                Object isLeadValue = hiveLeadData.get("isLead");

                Log.d("MainActivity", "📞 [NATIVE_SEARCH_FLUTTER] Lead found in Hive native database: " + leadName + " (" + contactNo + ") with ID: " + leadId);

                // Return the result in the format expected by Flutter
                Map<String, Object> result = new HashMap<>();
                result.put("id", leadId);
                result.put("name", leadName);
                result.put("contactNo", contactNo);
                result.put("alternateContactNo", hiveLeadData.get("alternateContactNo"));
                result.put("assignTo", hiveLeadData.get("assignTo"));
                result.put("isDeleted", hiveLeadData.get("isDeleted"));
                result.put("isArchived", hiveLeadData.get("isArchived"));
                result.put("isLead", isLeadValue != null ? isLeadValue : true);
                result.put("lastModifiedOn", hiveLeadData.get("lastModifiedOn"));

                return result;
            }

            Log.d("MainActivity", "📞 [NATIVE_SEARCH_FLUTTER] No lead found in Hive native database");
            return null;

        } catch (Exception e) {
            Log.e("MainActivity", "📞 [NATIVE_SEARCH_FLUTTER] Error during native search: " + e.getMessage());
            return null;
        }
    }

    /**
     * Search for lead in Hive native database using ultra-fast lookup
     */
    private Map<String, Object> searchInHiveNativeDatabase(String phoneNumber) {
        long startTime = System.currentTimeMillis();
        Log.d("MainActivity", "⚡ [ULTRA_FAST_HIVE] Starting ultra-fast search for: " + phoneNumber);

        try {
            // Check if Hive database has data
            SharedPreferences hiveDbPrefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            boolean hasData = hiveDbPrefs.getBoolean("has_data", false);
            int totalLeads = hiveDbPrefs.getInt("total_leads", 0);
            long lastUpdateTime = hiveDbPrefs.getLong("last_update_time", 0);

            Log.d("MainActivity", "⚡ [ULTRA_FAST_HIVE] Database status: hasData=" + hasData + ", totalLeads=" + totalLeads + ", lastUpdate=" + new java.util.Date(lastUpdateTime));

            if (!hasData || totalLeads == 0) {
                Log.d("MainActivity", "⚡ [ULTRA_FAST_HIVE] Hive native database is empty or has no data");
                return null;
            }

            // Use DataSyncService's static ultra-fast search method
            Map<String, Object> result = DataSyncService.searchLeadInHiveDatabaseStatic(this, phoneNumber);

            long endTime = System.currentTimeMillis();

            if (result != null) {
                String leadName = (String) result.get("name");
                String leadId = (String) result.get("id");
                String contactNo = (String) result.get("contactNo");

                Log.d("MainActivity", "⚡ [ULTRA_FAST_HIVE] ✅ FOUND in " + (endTime - startTime) + "ms: " + leadName + " (" + contactNo + ") with ID: " + leadId);
                return result;
            } else {
                Log.d("MainActivity", "⚡ [ULTRA_FAST_HIVE] ❌ NOT FOUND in " + (endTime - startTime) + "ms for: " + phoneNumber);
                return null;
            }

        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            Log.e("MainActivity", "⚡ [ULTRA_FAST_HIVE] Error in " + (endTime - startTime) + "ms: " + e.getMessage());
            return null;
        }
    }





    private void saveDirectoryPathInPreferences(Context context, String directoryPath) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();

        // Flutter stores String values with a "prefix_" in SharedPreferences
        editor.putString("flutter.directoryPath", directoryPath);
        editor.apply(); // Save the changes

        Log.d("Preferences", "Saved directoryPath: " + directoryPath);
    }

    private void removeDirectoryPathFromPreferences(Context context) {
        SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = preferences.edit();

        // Remove the stored directoryPath key
        editor.remove("flutter.directoryPath");
        editor.apply(); // Apply changes

        Log.d("Preferences", "directoryPath removed from SharedPreferences");
    }

    /**
     * Get LeadRat tenant ID from SharedPreferences
     */
    private String getLeadratTenantId() {
        try {
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

            // Try LeadRat global keys first
            String tenantId = prefs.getString("LeadratGlobal_TenantId", null);
            if (tenantId != null && !tenantId.isEmpty() && !"null".equals(tenantId)) {
                Log.d("LeadratData", "🔧 [LEADRAT_TENANT] Found tenant ID from LeadratGlobal_TenantId: " + tenantId);
                return tenantId;
            }

            // Try standard keys
            tenantId = prefs.getString("TenantId", null);
            if (tenantId != null && !tenantId.isEmpty() && !"null".equals(tenantId)) {
                Log.d("LeadratData", "🔧 [LEADRAT_TENANT] Found tenant ID from TenantId: " + tenantId);
                return tenantId;
            }

            // Try domain as fallback
            tenantId = prefs.getString("domain", null);
            if (tenantId != null && !tenantId.isEmpty() && !"null".equals(tenantId)) {
                Log.d("LeadratData", "🔧 [LEADRAT_TENANT] Found tenant ID from domain: " + tenantId);
                return tenantId;
            }

            Log.d("LeadratData", "⚠️ [LEADRAT_TENANT] No tenant ID found in SharedPreferences");
            return null;

        } catch (Exception e) {
            Log.e("LeadratData", "❌ [LEADRAT_TENANT] Error getting tenant ID: " + e.getMessage());
            return null;
        }
    }

    /**
     * Get LeadRat user ID from SharedPreferences
     */
    private String getLeadratUserId() {
        try {
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

            // Try LeadRat global keys first
            String userId = prefs.getString("LeadratGlobal_UserId", null);
            if (userId != null && !userId.isEmpty() && !"null".equals(userId)) {
                Log.d("LeadratData", "🔧 [LEADRAT_USER] Found user ID from LeadratGlobal_UserId: " + userId);
                return userId;
            }

            // Try standard keys
            userId = prefs.getString("UserId", null);
            if (userId != null && !userId.isEmpty() && !"null".equals(userId)) {
                Log.d("LeadratData", "🔧 [LEADRAT_USER] Found user ID from UserId: " + userId);
                return userId;
            }

            // Try user_id as fallback
            userId = prefs.getString("user_id", null);
            if (userId != null && !userId.isEmpty() && !"null".equals(userId)) {
                Log.d("LeadratData", "🔧 [LEADRAT_USER] Found user ID from user_id: " + userId);
                return userId;
            }

            Log.d("LeadratData", "⚠️ [LEADRAT_USER] No user ID found in SharedPreferences");
            return null;

        } catch (Exception e) {
            Log.e("LeadratData", "❌ [LEADRAT_USER] Error getting user ID: " + e.getMessage());
            return null;
        }
    }

    /**
     * Set test LeadRat data for testing purposes
     */
    private void setTestLeadratData() {
        try {
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());
            SharedPreferences.Editor editor = prefs.edit();

            // Set test data
            editor.putString("TenantId", "sleep");
            editor.putString("UserId", "df0a3af3-7d3d-41b5-8419-e1a6532cea8c");
            editor.apply();

            Log.d("LeadratData", "✅ [TEST_DATA] Test LeadRat data set: tenant=sleep, user=df0a3af3-7d3d-41b5-8419-e1a6532cea8c");

        } catch (Exception e) {
            Log.e("LeadratData", "❌ [TEST_DATA] Error setting test data: " + e.getMessage());
        }
    }

    /**
     * Get leads data from native SQLite database
     */
    private List<Map<String, Object>> getNativeLeadsFromDatabase(DataSyncService.NativeLeadsDbHelper dbHelper) {
        List<Map<String, Object>> leadsList = new java.util.ArrayList<>();

        try {
            android.database.sqlite.SQLiteDatabase db = dbHelper.getReadableDatabase();

            android.database.Cursor cursor = db.query(
                DataSyncService.NativeLeadsDbHelper.TABLE_LEADS,
                null, // Select all columns
                null, // No WHERE clause
                null, // No WHERE args
                null, // No GROUP BY
                null, // No HAVING
                null  // No ORDER BY
            );

            while (cursor.moveToNext()) {
                Map<String, Object> leadMap = new java.util.HashMap<>();

                leadMap.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                leadMap.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                leadMap.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                leadMap.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                leadMap.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));
                leadMap.put("isDeleted", cursor.getInt(cursor.getColumnIndexOrThrow("isDeleted")) == 1);
                leadMap.put("lastModifiedOn", cursor.getString(cursor.getColumnIndexOrThrow("lastModifiedOn")));
                leadMap.put("lastSyncedAt", cursor.getString(cursor.getColumnIndexOrThrow("lastSyncedAt")));

                leadsList.add(leadMap);
            }

            cursor.close();
            db.close();

        } catch (Exception e) {
            android.util.Log.e("MainActivity", "Error reading from native database: " + e.getMessage());
        }

        return leadsList;
    }

    /**
     * Save Hive data to separate Hive native database for call detection (includes both Hive and native data)
     */
    private boolean saveHiveDataToNativeDatabase(List<Map<String, Object>> hiveData) {
        // Use synchronization to prevent concurrent access to Hive Native DB
        synchronized (HIVE_DB_LOCK) {
            try {
                android.util.Log.d("MainActivity", "💾 [HIVE_TO_NATIVE] Saving " + hiveData.size() + " leads from Hive to separate native database...");

                DataSyncService.HiveLeadsDbHelper dbHelper = new DataSyncService.HiveLeadsDbHelper(this);
                android.database.sqlite.SQLiteDatabase db = dbHelper.getWritableDatabase();

            // Clear existing data first
            db.delete(DataSyncService.HiveLeadsDbHelper.TABLE_LEADS, null, null);
            android.util.Log.d("MainActivity", "🗑️ [HIVE_TO_NATIVE] Cleared existing Hive native database data");

            // Insert new leads from Hive
            db.beginTransaction();
            try {
                for (Map<String, Object> leadData : hiveData) {
                    String insertSql = "INSERT OR REPLACE INTO " + DataSyncService.HiveLeadsDbHelper.TABLE_LEADS +
                        " (id, name, contactNo, alternateContactNo, assignTo, isDeleted, isLead, lastModifiedOn, lastSyncedAt) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    String id = leadData.get("id") != null ? leadData.get("id").toString() : "";
                    String name = leadData.get("name") != null ? leadData.get("name").toString() : "";
                    String contactNo = leadData.get("contactNo") != null ? leadData.get("contactNo").toString() : "";
                    String alternateContactNo = leadData.get("alternateContactNo") != null ? leadData.get("alternateContactNo").toString() : "";
                    String assignTo = leadData.get("assignTo") != null ? leadData.get("assignTo").toString() : "";
                    boolean isDeleted = leadData.get("isDeleted") != null ? (Boolean) leadData.get("isDeleted") : false;
                    boolean isLead = leadData.get("isLead") != null ? (Boolean) leadData.get("isLead") : false;
                    String lastModifiedOn = leadData.get("lastModifiedOn") != null ? leadData.get("lastModifiedOn").toString() : "";
                    String lastSyncedAt = new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", java.util.Locale.US).format(new java.util.Date());

                    db.execSQL(insertSql, new Object[]{
                        id, name, contactNo, alternateContactNo, assignTo,
                        isDeleted ? 1 : 0, isLead ? 1 : 0, lastModifiedOn, lastSyncedAt
                    });
                }

                db.setTransactionSuccessful();
                android.util.Log.d("MainActivity", "✅ [HIVE_TO_NATIVE] Successfully inserted " + hiveData.size() + " Hive leads");

            } finally {
                db.endTransaction();
            }

            db.close();

            int totalLeads = hiveData.size();

            // Update metadata for Hive database
            android.content.SharedPreferences prefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("has_data", totalLeads > 0)
                .putInt("total_leads", totalLeads)
                .putLong("last_update_time", System.currentTimeMillis())
                .apply();

                android.util.Log.d("MainActivity", "✅ [HIVE_TO_NATIVE] Successfully synced " + totalLeads + " leads from Hive to separate native database");
                android.util.Log.d("MainActivity", "ℹ️ [HIVE_TO_NATIVE] Note: Removed duplicate native data addition since Hive already contains the most up-to-date data");
                return true;

            } catch (Exception e) {
                android.util.Log.e("MainActivity", "❌ [HIVE_TO_NATIVE] Error saving Hive data to separate native database: " + e.getMessage());
                return false;
            }
        } // End synchronized block
    }

    /**
     * Get leads data from original native SQLite database
     */
    private List<Map<String, Object>> getNativeLeadsData() {
        List<Map<String, Object>> leadsList = new ArrayList<>();

        try {
            DataSyncService.NativeLeadsDbHelper dbHelper = new DataSyncService.NativeLeadsDbHelper(this);
            android.database.sqlite.SQLiteDatabase db = dbHelper.getReadableDatabase();

            android.database.Cursor cursor = db.query(
                DataSyncService.NativeLeadsDbHelper.TABLE_LEADS,
                null, // Select all columns
                "isDeleted = 0 AND isArchived = 0", // Only non-deleted and non-archived leads
                null, // No WHERE args
                null, // No GROUP BY
                null, // No HAVING
                null  // No ORDER BY
            );

            android.util.Log.d("MainActivity", "📞 [NATIVE_DB] Found " + cursor.getCount() + " leads in original native database");

            while (cursor.moveToNext()) {
                Map<String, Object> leadMap = new HashMap<>();

                leadMap.put("id", cursor.getString(cursor.getColumnIndexOrThrow("id")));
                leadMap.put("name", cursor.getString(cursor.getColumnIndexOrThrow("name")));
                leadMap.put("contactNo", cursor.getString(cursor.getColumnIndexOrThrow("contactNo")));
                leadMap.put("alternateContactNo", cursor.getString(cursor.getColumnIndexOrThrow("alternateContactNo")));
                leadMap.put("assignTo", cursor.getString(cursor.getColumnIndexOrThrow("assignTo")));
                leadMap.put("isDeleted", cursor.getInt(cursor.getColumnIndexOrThrow("isDeleted")) == 1);
                leadMap.put("lastModifiedOn", cursor.getString(cursor.getColumnIndexOrThrow("lastModifiedOn")));
                leadMap.put("lastSyncedAt", cursor.getString(cursor.getColumnIndexOrThrow("lastSyncedAt")));

                leadsList.add(leadMap);
            }

            cursor.close();
            db.close();

            android.util.Log.d("MainActivity", "✅ [NATIVE_DB] Retrieved " + leadsList.size() + " leads from original native database");

        } catch (Exception e) {
            android.util.Log.e("MainActivity", "❌ [NATIVE_DB] Error reading from original native database: " + e.getMessage());
        }

        return leadsList;
    }

    /**
     * Clear native SQLite database
     */
    private boolean clearNativeLeadsDatabase() {
        try {
            DataSyncService.NativeLeadsDbHelper dbHelper = new DataSyncService.NativeLeadsDbHelper(this);
            android.database.sqlite.SQLiteDatabase db = dbHelper.getWritableDatabase();

            int deletedRows = db.delete(DataSyncService.NativeLeadsDbHelper.TABLE_LEADS, null, null);
            db.close();

            // Clear metadata
            android.content.SharedPreferences prefs = getSharedPreferences("native_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("has_data", false)
                .putInt("total_leads", 0)
                .apply();

            android.util.Log.d("MainActivity", "Cleared " + deletedRows + " leads from native database");
            return true;

        } catch (Exception e) {
            android.util.Log.e("MainActivity", "Error clearing native database: " + e.getMessage());
            return false;
        }
    }

    /**
     * Clear Hive Native database
     */
    private boolean clearHiveNativeDatabase() {
        try {
            android.util.Log.d("MainActivity", "🗑️ [HIVE_DB_CLEAR] Clearing Hive Native database...");

            DataSyncService.HiveLeadsDbHelper dbHelper = new DataSyncService.HiveLeadsDbHelper(this);
            android.database.sqlite.SQLiteDatabase db = dbHelper.getWritableDatabase();

            // Clear both tables
            int deletedLeads = db.delete(DataSyncService.HiveLeadsDbHelper.TABLE_LEADS, null, null);
            int deletedLookups = db.delete(DataSyncService.HiveLeadsDbHelper.TABLE_PHONE_LOOKUP, null, null);
            db.close();

            // Clear metadata
            android.content.SharedPreferences prefs = getSharedPreferences("hive_leads_db", Context.MODE_PRIVATE);
            prefs.edit()
                .putBoolean("has_data", false)
                .putInt("total_leads", 0)
                .putLong("last_update_time", 0)
                .apply();

            android.util.Log.d("MainActivity", "✅ [HIVE_DB_CLEAR] Cleared " + deletedLeads + " leads and " + deletedLookups + " phone lookup entries");
            return true;

        } catch (Exception e) {
            android.util.Log.e("MainActivity", "❌ [HIVE_DB_CLEAR] Error clearing Hive Native database: " + e.getMessage());
            return false;
        }
    }

    /**
     * Get Tenant ID from SharedPreferences (for data_sync_service channel)
     */
    private String getTenantIdFromPreferences() {
        try {
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

            // Try standard key first (used by CallReceiver)
            String tenantId = prefs.getString("TenantId", null);
            if (tenantId != null && !tenantId.isEmpty() && !"null".equals(tenantId)) {
                Log.d("MainActivity", "🔧 [TENANT_ID] Found tenant ID from TenantId: " + tenantId);
                return tenantId;
            }

            // Try LeadRat global keys as fallback
            tenantId = prefs.getString("LeadratGlobal_TenantId", null);
            if (tenantId != null && !tenantId.isEmpty() && !"null".equals(tenantId)) {
                Log.d("MainActivity", "🔧 [TENANT_ID] Found tenant ID from LeadratGlobal_TenantId: " + tenantId);
                return tenantId;
            }

            // Try domain as fallback
            tenantId = prefs.getString("domain", null);
            if (tenantId != null && !tenantId.isEmpty() && !"null".equals(tenantId)) {
                Log.d("MainActivity", "🔧 [TENANT_ID] Found tenant ID from domain: " + tenantId);
                return tenantId;
            }

            Log.d("MainActivity", "⚠️ [TENANT_ID] No tenant ID found in SharedPreferences");
            return null;

        } catch (Exception e) {
            Log.e("MainActivity", "❌ [TENANT_ID] Error getting tenant ID: " + e.getMessage());
            return null;
        }
    }

    /**
     * Get User ID from SharedPreferences (for data_sync_service channel)
     */
    private String getUserIdFromPreferences() {
        try {
            SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(getApplicationContext());

            // Try standard key first (used by CallReceiver)
            String userId = prefs.getString("UserId", null);
            if (userId != null && !userId.isEmpty() && !"null".equals(userId)) {
                Log.d("MainActivity", "🔧 [USER_ID] Found user ID from UserId: " + userId);
                return userId;
            }

            // Try LeadRat global keys as fallback
            userId = prefs.getString("LeadratGlobal_UserId", null);
            if (userId != null && !userId.isEmpty() && !"null".equals(userId)) {
                Log.d("MainActivity", "🔧 [USER_ID] Found user ID from LeadratGlobal_UserId: " + userId);
                return userId;
            }

            // Try user_id as fallback
            userId = prefs.getString("user_id", null);
            if (userId != null && !userId.isEmpty() && !"null".equals(userId)) {
                Log.d("MainActivity", "🔧 [USER_ID] Found user ID from user_id: " + userId);
                return userId;
            }

            Log.d("MainActivity", "⚠️ [USER_ID] No user ID found in SharedPreferences");
            return null;

        } catch (Exception e) {
            Log.e("MainActivity", "❌ [USER_ID] Error getting user ID: " + e.getMessage());
            return null;
        }
    }
}
