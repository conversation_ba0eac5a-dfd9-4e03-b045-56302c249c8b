import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'core/constants/routes.dart';
import 'core/constants/routes_names.dart';
import 'core/injection_container.dart';
import 'core/services/navigation_service.dart';

import 'features/offline_leads/data/services/offline_leads_service_initializer.dart';
import 'features/call_detection/call_detection_method_channel.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await initDependencies();
  // await getIt<SecretsManagerService>().getBaseUrl();

  // Initialize services in parallel to avoid blocking app startup
  final futures = <Future<void>>[];

  // Initialize offline leads background sync service (non-blocking with timeout)
  futures.add(
    OfflineLeadsServiceInitializer.initialize()
        .timeout(Duration(seconds: 30))
        .catchError((e) {
      if (kDebugMode) {
        print('Failed to initialize offline leads service: $e');
      }
      // Don't let initialization failures block app startup
      return null;
    }),
  );

  // Initialize call detection method channel (non-blocking with timeout)
  futures.add(
    Future(() {
      try {
        CallDetectionMethodChannel.initialize();
      } catch (e) {
        if (kDebugMode) {
          print('Failed to initialize call detection method channel: $e');
        }
      }
    }).timeout(Duration(seconds: 10)).catchError((e) {
      if (kDebugMode) {
        print('Call detection initialization timed out or failed: $e');
      }
      return null;
    }),
  );

  // Check and request phone and call log permissions (non-blocking with timeout)
  futures.add(
    _checkAndRequestCriticalPermissions()
        .timeout(Duration(seconds: 15))
        .then((permissionsGranted) {
      // Log permission status
      if (kDebugMode) {
        print('Critical permissions granted: $permissionsGranted');
      } else if (!permissionsGranted) {
        // In release mode, log to Sentry if permissions not granted
        Sentry.captureMessage(
          'App started without required permissions',
          level: SentryLevel.warning,
        );
      }
    }).catchError((e) {
      if (kDebugMode) {
        print('Error checking permissions (timeout or failure): $e');
      }
      // Don't block app startup for permission issues
      return null;
    }),
  );

  // Start all initialization tasks in parallel with overall timeout
  // This allows the app to start immediately while services initialize in background
  Future.wait(futures)
      .timeout(Duration(seconds: 45)) // Overall timeout for all initialization
      .catchError((e) {
    if (kDebugMode) {
      print('Some initialization tasks failed or timed out: $e');
    }
    // Log to Sentry in release mode for monitoring
    if (kReleaseMode) {
      Sentry.captureException(
        e,
        hint: Hint.withMap({'context': 'app_initialization'}),
      );
    }
    return <void>[];
  }).ignore();
  if (kReleaseMode == true) {
    await SentryFlutter.init(
      (options) {
        options.dsn =
            'https://<EMAIL>/4508951836950528';
        // Set tracesSampleRate to 1.0 to capture 100% of transactions for tracing.
        // We recommend adjusting this value in production.
        options.tracesSampleRate = 1.0;
        // The sampling rate for profiling is relative to tracesSampleRate
        // Setting to 1.0 will profile 100% of sampled transactions:
        options.profilesSampleRate = 1.0;
      },
      appRunner: () => runApp(
        SentryWidget(
          child: MyApp(),
        ),
      ),
    );
  } else {
    runApp(const MyApp());
  }
}

// Function to check and request critical permissions (phone and call logs)
// This function is now non-blocking with timeout to prevent ANR
Future<bool> _checkAndRequestCriticalPermissions() async {
  try {
    // Set a timeout to prevent ANR during permission requests
    return await _performPermissionChecksWithTimeout();
  } catch (e) {
    if (kDebugMode) {
      print('Error in permission check: $e');
    }
    return false;
  }
}

// Fallback method with timeout to prevent ANR
Future<bool> _performPermissionChecksWithTimeout() async {
  try {
    // Set a timeout to prevent ANR
    return await _performPermissionChecksInternal().timeout(
      Duration(seconds: 10), // 10 second timeout
    );
  } on TimeoutException {
    if (kDebugMode) {
      print('Permission check timed out - continuing with app startup');
    }
    // Don't block app startup if permissions take too long
    return false;
  } catch (e) {
    if (kDebugMode) {
      print('Error in permission check with timeout: $e');
    }
    return false;
  }
}

// Internal method for permission checks
Future<bool> _performPermissionChecksInternal() async {
  // Check if phone permission is granted
  if (!await Permission.phone.isGranted) {
    var status = await Permission.phone.request();
    if (status != PermissionStatus.granted) {
      return false;
    }
  }

  // Check for call log permission specifically using platform channel
  const platform = MethodChannel('call_detection_service');
  try {
    // First check if permissions are already granted
    bool hasPermissions = false;
    try {
      hasPermissions =
          await platform.invokeMethod('checkPhoneAndCallLogPermissions');
    } catch (e) {
      // If method not found, assume we need to check permissions
      hasPermissions = false;
    }

    if (!hasPermissions) {
      // Request permissions with timeout
      await platform.invokeMethod('requestPhoneAndCallLogPermissions')
          .timeout(Duration(seconds: 5));

      // Check again after requesting with timeout
      hasPermissions = await platform.invokeMethod('checkPhoneAndCallLogPermissions')
          .timeout(Duration(seconds: 3));
      if (!hasPermissions) {
        return false;
      }
    }

    return true;
  } catch (e, stackTrace) {
    // Log error with Sentry
    if (kReleaseMode) {
      await Sentry.captureException(
        e,
        stackTrace: stackTrace,
      );
    } else {
      // Only print in debug mode
      print('Error checking or requesting permissions: $e');
    }

    // Fallback to permission_handler if platform channel fails
    if (!await Permission.phone.isGranted) {
      var status = await Permission.phone.request();
      return status == PermissionStatus.granted;
    }

    return false;
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: registerBlocProviders(context),
      child: MaterialApp(
        title: 'LeadRat',
        navigatorKey: navigatorKey,
        debugShowCheckedModeBanner: false,
        initialRoute: RoutesName.splash,
        onGenerateRoute: Routes.generateRoute,
      ),
    );
  }
}
