import 'package:call_detection/features/call_recordings/presentation/widgets/path_popup.dart';
import 'package:flutter/material.dart';

import '../../../../core/constants/color_pallete.dart';
import '../../../../core/injection_container.dart';
import '../bloc/call_recordings_bloc/call_recordoings_bloc.dart';

class RecordingCard extends StatelessWidget {
  final String title;
  final String description;
  final String buttonText;
  final String? secondaryButtonText;
  final String imagePath;
  final double? width;
  final bool? isImageVisible;
  final bool? isEnterPathManuallyVisible;
  final VoidCallback onButtonPressed;
  final VoidCallback? onSecondaryButtonPressed;
  final TextEditingController controller = TextEditingController();

  RecordingCard({
    super.key,
    required this.title,
    required this.description,
    required this.buttonText,
    this.secondaryButtonText,
    required this.imagePath,
    this.width,
    this.isImageVisible,
    this.isEnterPathManuallyVisible,
    required this.onButtonPressed,
    this.onSecondaryButtonPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0XFF070709), Color(0XFF272531)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        image: isImageVisible ?? true
            ? DecorationImage(
                image: AssetImage(imagePath),
                alignment: Alignment.bottomRight,
              )
            : null,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Color(0XFF252525)),
      ),
      width: width,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Color(0XFFFFFFFF),
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: Color(0XFFD1D1D1),
                    fontSize: 10,
                    fontWeight: FontWeight.w300,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: 10),
                Row(spacing: 10, children: [
                  SizedBox(
                    height: 30,
                    child: ElevatedButton(
                      onPressed: onButtonPressed,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0XFF50BFA8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                      child: Text(
                        buttonText,
                        style: TextStyle(
                          color: Color(0XFFFFFFFF),
                          fontSize: 10,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                  if (secondaryButtonText != null)
                    SizedBox(
                      height: 30,
                      child: ElevatedButton(
                        onPressed: onSecondaryButtonPressed,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorPalette.lightBackground,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                          ),
                        ),
                        child: Text(
                          secondaryButtonText ?? "",
                          style: TextStyle(
                            color: Color(0XFFFFFFFF),
                            fontSize: 10,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                ]),
                if (isEnterPathManuallyVisible ?? false) ...[
                  SizedBox(height: 10),
                  Row(
                    children: [
                      // SizedBox(width: 10),
                      GestureDetector(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (context) => PathDialog(
                                title: "Enter Path",
                                isSetupMode: false,
                                controller: controller,
                                onCancel: () => Navigator.pop(context),
                                onConfirm: () {
                                  getIt<CallRecordingsBloc>().add(GetCallRecordingsEvent(recordingsPath: controller.text));
                                  Navigator.pop(context);
                                }),
                          );
                        },
                        child: Text(
                          "Enter Path manually",
                          style: TextStyle(
                            decoration: TextDecoration.underline,
                            decorationColor: Colors.grey,
                            color: Color(0XFF50BFA8),
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ],
                  )
                ]
              ],
            ),
          ),
        ],
      ),
    );
  }
}
