import 'package:fpdart/fpdart.dart';
import 'package:flutter/services.dart';
import '../repository/offline_leads_repository.dart';
import '../../data/models/offline_lead_model.dart';

class SyncLeadsUseCase {
  final OfflineLeadsRepository repository;

  SyncLeadsUseCase(this.repository);

  Future<Either<String, List<OfflineLeadModel>>> call() async {
    return await repository.syncLeads();
  }
}

class GetAllLeadsUseCase {
  final OfflineLeadsRepository repository;

  GetAllLeadsUseCase(this.repository);

  Future<Either<String, List<OfflineLeadModel>>> call() async {
    return await repository.getAllLeads();
  }
}

class GetLeadByIdUseCase {
  final OfflineLeadsRepository repository;

  GetLeadByIdUseCase(this.repository);

  Future<Either<String, OfflineLeadModel?>> call(String id) async {
    return await repository.getLeadById(id);
  }
}

class DeleteLeadUseCase {
  final OfflineLeadsRepository repository;

  DeleteLeadUseCase(this.repository);

  Future<Either<String, void>> call(String id) async {
    return await repository.deleteLead(id);
  }
}

class GetLeadsCountUseCase {
  final OfflineLeadsRepository repository;

  GetLeadsCountUseCase(this.repository);

  Future<Either<String, int>> call() async {
    return await repository.getLeadsCount();
  }
}

class GetLastSyncTimeUseCase {
  final OfflineLeadsRepository repository;

  GetLastSyncTimeUseCase(this.repository);

  Future<Either<String, DateTime?>> call() async {
    return await repository.getLastSyncTime();
  }
}

class GetHiveNativeDbCountUseCase {
  static const MethodChannel _methodChannel =
      MethodChannel('data_sync_service');

  Future<Either<String, int>> call() async {
    try {
      print('🚀 [HIVE_NATIVE_COUNT] === USE CASE STARTED ===');
      print('🔍 [HIVE_NATIVE_COUNT] Calling getHiveNativeDbCount method...');
      final int count =
          await _methodChannel.invokeMethod('getHiveNativeDbCount');
      print('🔍 [HIVE_NATIVE_COUNT] Received count: $count');
      print('✅ [HIVE_NATIVE_COUNT] === USE CASE COMPLETED SUCCESSFULLY ===');
      return Right(count);
    } catch (e) {
      print('❌ [HIVE_NATIVE_COUNT] Error: ${e.toString()}');
      print('💥 [HIVE_NATIVE_COUNT] === USE CASE FAILED ===');
      return Left('Failed to get Hive Native DB count: ${e.toString()}');
    }
  }
}

class GetHiveFlutterDbCountUseCase {
  final OfflineLeadsRepository repository;

  GetHiveFlutterDbCountUseCase(this.repository);

  Future<Either<String, int>> call() async {
    try {
      print('🚀 [HIVE_FLUTTER_COUNT] === USE CASE STARTED ===');
      print('🔍 [HIVE_FLUTTER_COUNT] Calling getLeadsCount method...');
      final result = await repository.getLeadsCount();
      return result.fold(
        (error) {
          print('❌ [HIVE_FLUTTER_COUNT] Error: $error');
          print('💥 [HIVE_FLUTTER_COUNT] === USE CASE FAILED ===');
          return Left(error);
        },
        (count) {
          print('🔍 [HIVE_FLUTTER_COUNT] Received count: $count');
          print(
              '✅ [HIVE_FLUTTER_COUNT] === USE CASE COMPLETED SUCCESSFULLY ===');
          return Right(count);
        },
      );
    } catch (e) {
      print('❌ [HIVE_FLUTTER_COUNT] Exception: ${e.toString()}');
      print('💥 [HIVE_FLUTTER_COUNT] === USE CASE FAILED ===');
      return Left('Failed to get Hive Flutter DB count: ${e.toString()}');
    }
  }
}
