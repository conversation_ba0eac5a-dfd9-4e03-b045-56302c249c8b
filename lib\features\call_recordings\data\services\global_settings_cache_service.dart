import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:call_detection/features/offline_leads/data/datasources/offline_leads_local_datasource.dart';

/// Service to cache global settings and reduce API calls
class GlobalSettingsCacheService {
  static const String _cacheKeyPrefix = 'global_settings_cache_';
  static const String _lastFetchTimeKey = 'global_settings_last_fetch_';
  static const Duration _cacheValidityDuration =
      Duration(minutes: 1); // Cache for 1 minute

  /// Get call recording settings with caching
  static Future<CallRecordingSettings> getCallRecordingSettings({
    required String tenantId,
    bool forceRefresh = false,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cacheKeyPrefix$tenantId';
      final lastFetchKey = '$_lastFetchTimeKey$tenantId';

      // Check if we have cached data and it's still valid
      if (!forceRefresh) {
        final cachedData = prefs.getString(cacheKey);
        final lastFetchTime = prefs.getInt(lastFetchKey);

        if (cachedData != null && lastFetchTime != null) {
          final lastFetch = DateTime.fromMillisecondsSinceEpoch(lastFetchTime);
          final now = DateTime.now();

          if (now.difference(lastFetch) < _cacheValidityDuration) {
            print(
                '🔧 [GLOBAL_SETTINGS_CACHE] Using cached settings for tenant: $tenantId');
            final Map<String, dynamic> cachedJson = json.decode(cachedData);
            return CallRecordingSettings.fromJson(cachedJson);
          } else {
            print(
                '🔧 [GLOBAL_SETTINGS_CACHE] Cache expired for tenant: $tenantId, fetching fresh data');
          }
        } else {
          print(
              '🔧 [GLOBAL_SETTINGS_CACHE] No cached data found for tenant: $tenantId, fetching fresh data');
        }
      } else {
        print(
            '🔧 [GLOBAL_SETTINGS_CACHE] Force refresh requested for tenant: $tenantId');
      }

      // Fetch fresh data from API
      final settings = await _fetchFromApi(tenantId);

      // Cache the fresh data
      await _cacheSettings(prefs, cacheKey, lastFetchKey, settings);

      return settings;
    } catch (e) {
      print(
          '❌ [GLOBAL_SETTINGS_CACHE] Error getting call recording settings: $e');

      // Try to return cached data as fallback even if expired
      try {
        final prefs = await SharedPreferences.getInstance();
        final cacheKey = '$_cacheKeyPrefix$tenantId';
        final cachedData = prefs.getString(cacheKey);

        if (cachedData != null) {
          print(
              '🔧 [GLOBAL_SETTINGS_CACHE] Using expired cached data as fallback');
          final Map<String, dynamic> cachedJson = json.decode(cachedData);
          return CallRecordingSettings.fromJson(cachedJson);
        }
      } catch (cacheError) {
        print(
            '❌ [GLOBAL_SETTINGS_CACHE] Failed to read cached data: $cacheError');
      }

      // Return default settings if everything fails
      print(
          '🔧 [GLOBAL_SETTINGS_CACHE] Returning default settings due to errors');
      return CallRecordingSettings.defaultSettings();
    }
  }

  /// Fetch settings from API
  static Future<CallRecordingSettings> _fetchFromApi(String tenantId) async {
    const apiUrl =
        'https://prd-mobile.leadrat.com/api/v1/globalsettings/callsettings/anonymous';

    print(
        '🌐 [GLOBAL_SETTINGS_CACHE] Fetching fresh settings from API for tenant: $tenantId');

    // Get previous settings before fetching new ones
    CallRecordingSettings? previousSettings;
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cacheKeyPrefix$tenantId';
      final cachedData = prefs.getString(cacheKey);
      if (cachedData != null) {
        final Map<String, dynamic> cachedJson = json.decode(cachedData);
        previousSettings = CallRecordingSettings.fromJson(cachedJson);
        print(
            '🔧 [GLOBAL_SETTINGS_CACHE] Previous settings: $previousSettings');
      }
    } catch (e) {
      print('⚠️ [GLOBAL_SETTINGS_CACHE] Failed to get previous settings: $e');
    }

    final response = await http.get(
      Uri.parse(apiUrl),
      headers: {
        'accept': 'application/json',
        'tenant': tenantId,
      },
    ).timeout(
      const Duration(seconds: 30),
      onTimeout: () {
        throw Exception('API request timeout');
      },
    );

    if (response.statusCode == 200) {
      final jsonResponse = json.decode(response.body);
      final data = jsonResponse['data'];

      final newSettings = CallRecordingSettings(
        isCallRecordingEnabled: data['isCallRecordingEnabled'] ?? false,
        shouldViewOnlyAssigned: data['shouldViewOnlyAssigned'] ?? false,
        shouldOpenPopupAfterCall: data['shouldOpenPopupAfterCall'] ?? true,
      );

      print(
          '✅ [GLOBAL_SETTINGS_CACHE] Successfully fetched settings: $newSettings');

      // Check if shouldViewOnlyAssigned has changed
      if (previousSettings != null &&
          previousSettings.shouldViewOnlyAssigned !=
              newSettings.shouldViewOnlyAssigned) {
        print(
            '🔄 [GLOBAL_SETTINGS_CACHE] shouldViewOnlyAssigned changed from ${previousSettings.shouldViewOnlyAssigned} to ${newSettings.shouldViewOnlyAssigned}');

        // Check if native database sync recently completed
        final prefs = await SharedPreferences.getInstance();
        final nativeDbSynced = prefs.getBool('native_db_synced') ?? false;
        final nativeDbSyncTime = prefs.getInt('native_db_sync_time') ?? 0;
        final currentTime = DateTime.now().millisecondsSinceEpoch;
        final timeSinceNativeSync = currentTime - nativeDbSyncTime;

        // Set flag to indicate that Hive database should be cleared on next native sync
        print(
            '🔄 [GLOBAL_SETTINGS_CACHE] Setting flag to clear Hive database on next native sync due to shouldViewOnlyAssigned change');
        await prefs.setBool('should_clear_hive_for_settings_change', true);

        // If native database was recently synced (within last 2 minutes), skip Flutter database operations
        if (nativeDbSynced && timeSinceNativeSync < 2 * 60 * 1000) {
          print(
              '✅ [GLOBAL_SETTINGS_CACHE] Native database recently synced (${timeSinceNativeSync}ms ago), skipping immediate database clear');
          print(
              '✅ [GLOBAL_SETTINGS_CACHE] Flag set for next native sync to clear and reload Hive database');

          // Clear the native sync flag since we've acknowledged it
          await prefs.remove('native_db_synced');
          await prefs.remove('native_db_sync_time');
        } else {
          print(
              '🗑️ [GLOBAL_SETTINGS_CACHE] No recent native sync detected, triggering database clear and reload...');
          // Trigger database clear and reload
          await _clearDatabaseAndReload(tenantId);
        }
      } else {
        print(
            '✅ [GLOBAL_SETTINGS_CACHE] shouldViewOnlyAssigned unchanged: ${newSettings.shouldViewOnlyAssigned}');
      }

      return newSettings;
    } else {
      throw Exception('API call failed with status: ${response.statusCode}');
    }
  }

  /// Clear database and reload with new settings
  static Future<void> _clearDatabaseAndReload(String tenantId) async {
    try {
      print(
          '🗑️ [GLOBAL_SETTINGS_CACHE] Starting database clear and reload process...');

      // Check if a sync is already in progress to prevent infinite loops
      final prefs = await SharedPreferences.getInstance();
      final syncInProgress = prefs.getBool('sync_in_progress') ?? false;

      if (syncInProgress) {
        print(
            '⚠️ [GLOBAL_SETTINGS_CACHE] Sync already in progress, skipping database clear and reload to prevent infinite loop');
        return;
      }

      // Import the required classes
      final localDataSource = OfflineLeadsLocalDataSourceImpl();

      // Clear the database
      await localDataSource.deleteAllLeads();
      print('🗑️ [GLOBAL_SETTINGS_CACHE] Database cleared successfully');

      // Clear last sync time to force full reload
      await prefs.remove('last_sync_time');
      print('🔄 [GLOBAL_SETTINGS_CACHE] Reset sync time to force full reload');

      // Set flag to indicate that a full sync should be triggered on next app interaction
      await prefs.setBool('should_trigger_full_sync', true);
      print(
          '🔄 [GLOBAL_SETTINGS_CACHE] Set flag to trigger full sync on next app interaction');

      print(
          '✅ [GLOBAL_SETTINGS_CACHE] Database clear completed, full sync will be triggered when safe');
    } catch (e) {
      print('❌ [GLOBAL_SETTINGS_CACHE] Error in database clear and reload: $e');
    }
  }

  /// Cache settings to SharedPreferences
  static Future<void> _cacheSettings(
    SharedPreferences prefs,
    String cacheKey,
    String lastFetchKey,
    CallRecordingSettings settings,
  ) async {
    try {
      final jsonData = json.encode(settings.toJson());
      await prefs.setString(cacheKey, jsonData);
      await prefs.setInt(lastFetchKey, DateTime.now().millisecondsSinceEpoch);

      // Also store individual values for backward compatibility
      await prefs.setBool(
          'shouldViewOnlyAssigned', settings.shouldViewOnlyAssigned);

      print('✅ [GLOBAL_SETTINGS_CACHE] Settings cached successfully');
    } catch (e) {
      print('❌ [GLOBAL_SETTINGS_CACHE] Failed to cache settings: $e');
    }
  }

  /// Clear cache for a specific tenant
  static Future<void> clearCache(String tenantId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '$_cacheKeyPrefix$tenantId';
      final lastFetchKey = '$_lastFetchTimeKey$tenantId';

      await prefs.remove(cacheKey);
      await prefs.remove(lastFetchKey);

      print('✅ [GLOBAL_SETTINGS_CACHE] Cache cleared for tenant: $tenantId');
    } catch (e) {
      print('❌ [GLOBAL_SETTINGS_CACHE] Failed to clear cache: $e');
    }
  }

  /// Clear all cached settings
  static Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();

      for (final key in keys) {
        if (key.startsWith(_cacheKeyPrefix) ||
            key.startsWith(_lastFetchTimeKey)) {
          await prefs.remove(key);
        }
      }

      print('✅ [GLOBAL_SETTINGS_CACHE] All cache cleared');
    } catch (e) {
      print('❌ [GLOBAL_SETTINGS_CACHE] Failed to clear all cache: $e');
    }
  }
}

/// Model for call recording settings
class CallRecordingSettings {
  final bool isCallRecordingEnabled;
  final bool shouldViewOnlyAssigned;
  final bool shouldOpenPopupAfterCall;

  CallRecordingSettings({
    required this.isCallRecordingEnabled,
    required this.shouldViewOnlyAssigned,
    required this.shouldOpenPopupAfterCall,
  });

  factory CallRecordingSettings.fromJson(Map<String, dynamic> json) {
    return CallRecordingSettings(
      isCallRecordingEnabled: json['isCallRecordingEnabled'] ?? false,
      shouldViewOnlyAssigned: json['shouldViewOnlyAssigned'] ?? false,
      shouldOpenPopupAfterCall: json['shouldOpenPopupAfterCall'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'isCallRecordingEnabled': isCallRecordingEnabled,
      'shouldViewOnlyAssigned': shouldViewOnlyAssigned,
      'shouldOpenPopupAfterCall': shouldOpenPopupAfterCall,
    };
  }

  factory CallRecordingSettings.defaultSettings() {
    return CallRecordingSettings(
      isCallRecordingEnabled: false,
      shouldViewOnlyAssigned: false,
      shouldOpenPopupAfterCall: true,
    );
  }

  @override
  String toString() {
    return 'CallRecordingSettings(isCallRecordingEnabled: $isCallRecordingEnabled, shouldViewOnlyAssigned: $shouldViewOnlyAssigned, shouldOpenPopupAfterCall: $shouldOpenPopupAfterCall)';
  }
}
