
import 'package:call_detection/core/managers/shared_preference_manager.dart';

import '../utils/preference_helper.dart';

class SharedPreferenceManagerImpl implements SharedPreferenceManager {
  @override
  Future<String?> getString(String key) async {
    return await PreferencesHelper.getString(key);
  }

  @override
  Future<void> setString(String key, String value) async {
    await PreferencesHelper.saveString(key, value);
  }

  @override
  Future<void> remove(String key) {
    throw UnimplementedError();
  }
  @override
  Future<void> clearAllData() async {
    await PreferencesHelper.clearAllData();
  }
}