package com.leadrat.call_detection.call_detection.models;

import com.leadrat.call_detection.call_detection.enums.BHKType;

import java.util.UUID;
import java.util.Date;
import java.util.List;

public class CallLogBaseDTO {
    public UUID id;
    public String name;
    public boolean isFound;
    public PropertyTypeDTO propertyType;
    public PropertyTypeDTO basePropertyType;
    public Double noOfBHK;
    public BHKType bhkType;
    public Long lowerBudget;
    public Long upperBudget;
    public List<ProjectDTO> projects;
    public Date scheduledDate;
}