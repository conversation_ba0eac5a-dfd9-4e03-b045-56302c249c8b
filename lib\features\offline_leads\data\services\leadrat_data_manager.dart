import 'dart:async';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Centralized manager for LeadRat tenant ID and user ID
/// Handles waiting for data availability and provides reactive updates
class LeadratDataManager {
  static LeadratDataManager? _instance;
  static LeadratDataManager get instance => _instance ??= LeadratDataManager._();
  
  LeadratDataManager._();

  // Stream controllers for reactive updates
  final StreamController<String?> _tenantIdController = StreamController<String?>.broadcast();
  final StreamController<String?> _userIdController = StreamController<String?>.broadcast();
  final StreamController<bool> _dataAvailabilityController = StreamController<bool>.broadcast();

  // Streams for listening to data changes
  Stream<String?> get tenantIdStream => _tenantIdController.stream;
  Stream<String?> get userIdStream => _userIdController.stream;
  Stream<bool> get dataAvailabilityStream => _dataAvailabilityController.stream;

  // Current cached values
  String? _cachedTenantId;
  String? _cachedUserId;
  DateTime? _lastFetchTime;
  bool _isInitialized = false;

  // Constants for SharedPreferences keys
  static const String _tenantIdKey = 'TenantId';
  static const String _userIdKey = 'UserId';
  static const String _leadratTenantIdKey = 'LeadratGlobal_TenantId';
  static const String _leadratUserIdKey = 'LeadratGlobal_UserId';

  /// Initialize the data manager and start monitoring for data
  Future<void> initialize() async {
    if (_isInitialized) return;

    print('🔧 [LEADRAT_DATA] Initializing LeadratDataManager...');
    
    // Load initial data
    await _loadDataFromSources();
    
    // Start periodic monitoring for data updates
    _startPeriodicMonitoring();
    
    _isInitialized = true;
    print('✅ [LEADRAT_DATA] LeadratDataManager initialized successfully');
  }

  /// Get tenant ID with waiting mechanism
  Future<String?> getTenantId({Duration? timeout}) async {
    await _ensureInitialized();

    // If we have cached data, return it
    if (_cachedTenantId != null && _cachedTenantId!.isNotEmpty && _cachedTenantId != 'null') {
      print('🔧 [LEADRAT_DATA] Returning cached tenant ID: $_cachedTenantId');
      return _cachedTenantId;
    }

    // Wait for data to become available with shorter default timeout
    print('⏳ [LEADRAT_DATA] Waiting for tenant ID to become available...');
    return await _waitForTenantId(timeout ?? const Duration(seconds: 10));
  }

  /// Get user ID with waiting mechanism
  Future<String?> getUserId({Duration? timeout}) async {
    await _ensureInitialized();

    // If we have cached data, return it
    if (_cachedUserId != null && _cachedUserId!.isNotEmpty && _cachedUserId != 'null') {
      print('🔧 [LEADRAT_DATA] Returning cached user ID: $_cachedUserId');
      return _cachedUserId;
    }

    // Wait for data to become available with shorter default timeout
    print('⏳ [LEADRAT_DATA] Waiting for user ID to become available...');
    return await _waitForUserId(timeout ?? const Duration(seconds: 10));
  }

  /// Check if both tenant ID and user ID are available
  bool get isDataAvailable {
    return _cachedTenantId != null &&
           _cachedTenantId!.isNotEmpty &&
           _cachedTenantId != 'null' &&
           _cachedUserId != null &&
           _cachedUserId!.isNotEmpty &&
           _cachedUserId != 'null';
  }

  /// Get current data immediately without waiting (non-blocking)
  Map<String, String?> getCurrentData() {
    return {
      'tenantId': _cachedTenantId,
      'userId': _cachedUserId,
    };
  }

  /// Wait for both tenant ID and user ID to become available
  Future<Map<String, String?>> waitForData({Duration? timeout}) async {
    await _ensureInitialized();

    if (isDataAvailable) {
      print('✅ [LEADRAT_DATA] Data already available: tenant=$_cachedTenantId, user=$_cachedUserId');
      return {'tenantId': _cachedTenantId, 'userId': _cachedUserId};
    }

    print('⏳ [LEADRAT_DATA] Waiting for both tenant ID and user ID to become available...');

    final completer = Completer<Map<String, String?>>();
    final timeoutDuration = timeout ?? const Duration(seconds: 10);
    
    // Set up timeout
    Timer? timeoutTimer;
    if (timeoutDuration.inMilliseconds > 0) {
      timeoutTimer = Timer(timeoutDuration, () {
        if (!completer.isCompleted) {
          print('❌ [LEADRAT_DATA] Timeout waiting for data after ${timeoutDuration.inSeconds} seconds');
          completer.complete({'tenantId': null, 'userId': null});
        }
      });
    }

    // Listen for data availability
    late StreamSubscription subscription;
    subscription = _dataAvailabilityController.stream.listen((isAvailable) {
      if (isAvailable && !completer.isCompleted) {
        timeoutTimer?.cancel();
        subscription.cancel();
        print('✅ [LEADRAT_DATA] Data became available: tenant=$_cachedTenantId, user=$_cachedUserId');
        completer.complete({'tenantId': _cachedTenantId, 'userId': _cachedUserId});
      }
    });

    // Trigger immediate check
    await _loadDataFromSources();

    return completer.future;
  }

  /// Force refresh data from all sources
  Future<void> refreshData() async {
    print('🔄 [LEADRAT_DATA] Force refreshing data from all sources...');
    await _loadDataFromSources();
  }

  /// Load data from all available sources
  Future<void> _loadDataFromSources() async {
    String? newTenantId;
    String? newUserId;

    try {
      // Try method channel first (from LeadRat app)
      const MethodChannel leadratChannel = MethodChannel('leadrat_global_data');
      
      try {
        newTenantId = await leadratChannel.invokeMethod('getLeadratTenantId');
        newUserId = await leadratChannel.invokeMethod('getLeadratUserId');
        
        if (newTenantId != null && newTenantId.isNotEmpty && newTenantId != 'null' &&
            newUserId != null && newUserId.isNotEmpty && newUserId != 'null') {
          print('🔧 [LEADRAT_DATA] Got data from method channel: tenant=$newTenantId, user=$newUserId');
        }
      } catch (e) {
        print('⚠️ [LEADRAT_DATA] Method channel failed: $e');
      }

      // Fallback to SharedPreferences
      if (newTenantId == null || newTenantId.isEmpty || newTenantId == 'null' ||
          newUserId == null || newUserId.isEmpty || newUserId == 'null') {
        
        final prefs = await SharedPreferences.getInstance();
        
        // Try LeadRat global keys first
        newTenantId ??= prefs.getString(_leadratTenantIdKey);
        newUserId ??= prefs.getString(_leadratUserIdKey);
        
        // Try standard keys as fallback
        newTenantId ??= prefs.getString(_tenantIdKey);
        newUserId ??= prefs.getString(_userIdKey);
        
        // Try domain key as another fallback for tenant
        newTenantId ??= prefs.getString('domain');
        newUserId ??= prefs.getString('user_id');
        
        if (newTenantId != null && newTenantId.isNotEmpty && newTenantId != 'null' &&
            newUserId != null && newUserId.isNotEmpty && newUserId != 'null') {
          print('🔧 [LEADRAT_DATA] Got data from SharedPreferences: tenant=$newTenantId, user=$newUserId');
        }
      }

    } catch (e) {
      print('❌ [LEADRAT_DATA] Error loading data: $e');
    }

    // Update cached values and notify listeners
    _updateCachedData(newTenantId, newUserId);
  }

  /// Update cached data and notify listeners
  void _updateCachedData(String? tenantId, String? userId) {
    bool dataChanged = false;
    
    if (_cachedTenantId != tenantId) {
      _cachedTenantId = tenantId;
      _tenantIdController.add(tenantId);
      dataChanged = true;
    }
    
    if (_cachedUserId != userId) {
      _cachedUserId = userId;
      _userIdController.add(userId);
      dataChanged = true;
    }
    
    if (dataChanged) {
      _lastFetchTime = DateTime.now();
      _dataAvailabilityController.add(isDataAvailable);
      
      if (isDataAvailable) {
        print('✅ [LEADRAT_DATA] Data updated and available: tenant=$_cachedTenantId, user=$_cachedUserId');
      } else {
        print('⚠️ [LEADRAT_DATA] Data updated but not complete: tenant=$_cachedTenantId, user=$_cachedUserId');
      }
    }
  }

  /// Start periodic monitoring for data updates
  void _startPeriodicMonitoring() {
    Timer.periodic(const Duration(seconds: 10), (timer) async {
      if (!_isInitialized) {
        timer.cancel();
        return;
      }
      
      // Only refresh if we don't have complete data or it's been a while
      if (!isDataAvailable || 
          (_lastFetchTime != null && DateTime.now().difference(_lastFetchTime!).inMinutes > 1)) {
        await _loadDataFromSources();
      }
    });
  }

  /// Wait specifically for tenant ID
  Future<String?> _waitForTenantId(Duration timeout) async {
    if (_cachedTenantId != null && _cachedTenantId!.isNotEmpty && _cachedTenantId != 'null') {
      return _cachedTenantId;
    }

    final completer = Completer<String?>();
    late StreamSubscription subscription;
    
    Timer? timeoutTimer = Timer(timeout, () {
      if (!completer.isCompleted) {
        subscription.cancel();
        completer.complete(null);
      }
    });

    subscription = _tenantIdController.stream.listen((tenantId) {
      if (tenantId != null && tenantId.isNotEmpty && tenantId != 'null' && !completer.isCompleted) {
        timeoutTimer.cancel();
        subscription.cancel();
        completer.complete(tenantId);
      }
    });

    await _loadDataFromSources();
    return completer.future;
  }

  /// Wait specifically for user ID
  Future<String?> _waitForUserId(Duration timeout) async {
    if (_cachedUserId != null && _cachedUserId!.isNotEmpty && _cachedUserId != 'null') {
      return _cachedUserId;
    }

    final completer = Completer<String?>();
    late StreamSubscription subscription;
    
    Timer? timeoutTimer = Timer(timeout, () {
      if (!completer.isCompleted) {
        subscription.cancel();
        completer.complete(null);
      }
    });

    subscription = _userIdController.stream.listen((userId) {
      if (userId != null && userId.isNotEmpty && userId != 'null' && !completer.isCompleted) {
        timeoutTimer.cancel();
        subscription.cancel();
        completer.complete(userId);
      }
    });

    await _loadDataFromSources();
    return completer.future;
  }

  /// Ensure the manager is initialized
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }

  /// Dispose resources
  void dispose() {
    _tenantIdController.close();
    _userIdController.close();
    _dataAvailabilityController.close();
    _isInitialized = false;
  }
}
